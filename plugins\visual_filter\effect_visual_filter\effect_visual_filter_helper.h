#pragma once

#include <string>
#include <vector>

#include "nlohmann/json.hpp"

#ifndef _EFFECT_VISUAL_FILTER_NO_NEED_HELPER_FUNCTIONS
#include "mediasdk_api_visual_filter.h"
#endif  //_EFFECT_VISUAL_FILTER_NO_NEED_HELPER_FUNCTIONS

namespace mediasdk::effect_visual_filter {

const char* const kEffectVisualFilterName = "EffectVisualFilter";

const char* const kComposerAdderKey = "add_composer";
const char* const kComposerSetterKey = "set_composer";
const char* const kComposerUpdaterKey = "update_composer";
const char* const kComposerReplaceKey = "replace_composer";
const char* const kComposerRemoverKey = "remove_composer";
const char* const kComposerSetTextKey = "set_text_composer";
const char* const kComposerSetBackgroundKey = "set_background_composer";
// return json_data {"action":"get_composer_exclusion", "isExclusion": int}
const char* const kComposerGetExclusionKey = "get_composer_exclusion";
const char* const kSendMsg = "send_msg";

// return json_data
// {
//   "action":"action_get_effct_fps",
//   "effectFps":float,
//   "effectAchieveRate": double
// }
const char* const kActionGetEffectFps = "action_get_effect_fps";

// visual observer notify
//  json_data {"enable": bool}
const char* const kNotifyStatus = "effect_notify_status";
// json_data {"msgID":uint32,"arg1":int,"arg2":int,"arg3":string}
const char* const kNotifyResourceLoad = "effect_notify_resource_load";
// json_data {"use":bool}
const char* const kNotifyAlgoUse = "effect_notify_algo_use";
// json_data {"version":int}
const char* const kNotifyGLESVersion = "effect_notify_gles_version";

const char* const kNotifyEffectLag = "effect_notify_lag";
// json_data {"msgID":uint32,"arg1":int,"arg2":int,"arg3":string}
const char* const kNotifyEffectMsg = "effect_notify_msg";

// init params
// codec: utf-8
struct InitParams {
  std::vector<std::string> composers;  // json key: "composers"
  std::vector<std::string> tags;
  std::string background_path;         // json key: "backgroundPath"
  std::string background_key;          // json key: "backgroundKey"
  // json key: "useEmptyFrameBeforeEffectProcess"
  bool use_empty_frame_before_effect_process;
};

// helper functions
#ifndef _EFFECT_VISUAL_FILTER_NO_NEED_HELPER_FUNCTIONS

// // CreateFilter: Create a Effect Filter, usually used for camera viausl
// // filter_id: filter id, GUID, not repeat
// // owner_canvas_item_id: id of CanvasItem that contains the filter
// // param: init effect param, include composer array, background information
// // closure: Asynchronous callback result, bool
// // Notice: You can directly use the function CreateVisualFilter.
// inline void CreateFilter(const char* filter_id,
//                          const char* owner_canvas_item_id,
//                          const InitParams& param,
//                          const Closure& closure) {
//   nlohmann::json j = {{"composers", param.composers},
//                       {"backgroundPath", param.background_path},
//                       {"backgroundKey", param.background_key}};

//   CreateVisualFilter(filter_id, kEffectVisualFilterName,
//   owner_canvas_item_id,
//                      j.dump().c_str(), closure);
// }

// // DestroyFilter: Destroy a created effect filter
// // filter_id: The filter ID passed in during creation
// // owner_canvas_item_id: id of CanvasItem that contains the filter
// // closure: Asynchronous callback result, bool
// // Notice: You can use DestroyVisualFilter directly
// inline void DestroyFilter(const char* filter_id,
//                           const char* owner_canvas_item_id,
//                           const Closure& closure) {
//   DestroyVisualFilter(filter_id, owner_canvas_item_id, closure);
// }

// // SetFilterActive: let filter active or deactive
// // filter_id: The filter ID passed in during creation
// // owner_canvas_item_id: id of CanvasItem that contains the filter
// // active: true or false
// // Notice: You can use VisualFilterSetActive directly,
// //   -id: filter id
// //   -active: bool
// //   -closure
// inline void SetFilterActive(const char* filter_id,
//                             const char* owner_canvas_item_id,
//                             bool active,
//                             Closure closure) {
//   VisualFilterSetActive(filter_id, owner_canvas_item_id, active,
//                         std::move(closure));
// }

// // GetFilterActive: filter is active
// // id: The filter ID passed in during creation
// // closure: Asynchronous callback result, ResultBoolBool
// // Notice: You can use SetVisualFilterProperty directly,
// //   -id: filter id
// //   -closure
// inline void GetFilterActive(const char* filter_id,
//                             const char* owner_canvas_item_id,
//                             Closure closure) {
//   VisualFilterGetActive(filter_id, owner_canvas_item_id, std::move(closure));
// }

// // FilterAddComposer: Add composers to the filter
// // composers: composer array, (composer:<effect path>[;<key>;<value>])
// // closure: Asynchronous callback result, bool
// // Notice: You can use SetVisualFilterProperty directly,
// //   -id: filter id
// //   -property_key: kComposerAdderKey
// //   -property_value: json, e.g. ["path1;key1;value1", "path2;key2;value2"]
// //   -closure
// inline void FilterAddComposer(const char* filter_id,
//                               const char* owner_canvas_item_id,
//                               const std::vector<std::string>& composers,
//                               const Closure& closure) {
//   nlohmann::json j = composers;
//   std::string json_param = j.dump();
//   SetVisualFilterProperty(filter_id, owner_canvas_item_id, kComposerAdderKey,
//                           json_param.c_str(), closure);
// }

// // FilterSetComposer: Set composer for Filter
// // composers: composer array(composer:<effect path>[;<key>;<value>])
// // closure: Asynchronous callback result, bool
// // Notice: You can use SetVisualFilterProperty directly,
// //   -id: filter id
// //   -property_key: kComposerSetterKey
// //   -property_value: json, e.g. ["path1;key1;value1", "path2;key2;value2"]
// //   -closure
// inline void FilterSetComposer(const char* filter_id,
//                               const char* owner_canvas_item_id,
//                               const std::vector<std::string>& composers,
//                               const Closure& closure) {
//   nlohmann::json j = composers;
//   std::string json_param = j.dump();
//   SetVisualFilterProperty(filter_id, owner_canvas_item_id,
//   kComposerSetterKey,
//                           json_param.c_str(), closure);
// }

// // FilterUpdateComposer: Update a composer for the filter
// // id: filter id
// // path: composer path
// // key: composer key
// // value: composer value
// // closure: Asynchronous callback result, bool
// // Notice: You can use SetVisualFilterProperty directly
// //   -id: filter id
// //   -property_key: kComposerUpdaterKey
// //   -property_value: json, e.g. {"path":"path", key:"key", value:0.f}
// //   -closure
// inline void FilterUpdateComposer(const char* filter_id,
//                                  const char* owner_canvas_item_id,
//                                  const std::string& path,
//                                  const std::string& key,
//                                  float value,
//                                  const Closure& closure) {
//   nlohmann::json j = {
//       {"path", path},
//       {"key", key},
//       {"value", value},
//   };
//   std::string json_param = j.dump();
//   SetVisualFilterProperty(filter_id, owner_canvas_item_id,
//   kComposerUpdaterKey,
//                           json_param.c_str(), closure);
// }

// // FilterRemoveComposer: Remove certain Composers from the Filter
// // id: filter id
// // composers: composer array (composer:<effect path>[;<key>;<value>])
// // closure: Asynchronous callback result, bool
// // Notice: You can use SetVisualFilterProperty directly
// //   -id: filter id
// //   -property_key: kComposerRemoverKey
// //   -property_value: json, e.g. ["path1;key1;value1", "path2;key2;value2"]
// //   -closure
// inline void FilterRemoveComposer(const char* filter_id,
//                                  const char* owner_canvas_item_id,
//                                  const std::vector<std::string>& composers,
//                                  const Closure& closure) {
//   nlohmann::json j = composers;
//   std::string json_param = j.dump();
//   SetVisualFilterProperty(filter_id, owner_canvas_item_id,
//   kComposerRemoverKey,
//                           json_param.c_str(), closure);
// }

// // FilterSetTextComposer: Set Text Composer for Filter
// // id: filter id
// // key: text composer key
// // value: text composer value
// // closure: Asynchronous callback result, bool
// // Notice: You can use SetVisualFilterProperty directly
// //   -id: filter id
// //   -property_key: kComposerSetTextKey
// //   -property_value: json, e.g. {"key":"key", "value": "value1"}
// //   -closure
// inline void FilterSetTextComposer(const char* filter_id,
//                                   const char* owner_canvas_item_id,
//                                   const std::string& key,
//                                   const std::string& value,
//                                   const Closure& closure) {
//   nlohmann::json j = {
//       {"key", key},
//       {"value", value},
//   };
//   std::string json_param = j.dump();
//   SetVisualFilterProperty(filter_id, owner_canvas_item_id,
//   kComposerSetTextKey,
//                           json_param.c_str(), closure);
// }

// // FilterSetBackgroundComposer: Set Background Composer for Filter
// // id: filter id
// // path: background composer path
// // key: background composer key
// // closure: Asynchronous callback result, bool
// // Notice: You can use SetVisualFilterProperty directly
// //   -id: filter id
// //   -property_key: kComposerSetBackgroundKey
// //   -property_value: json, e.g. {"path":"path1", "key": "key1"}
// //   -closure
// inline void FilterSetBackgroundComposer(const char* filter_id,
//                                         const char* owner_canvas_item_id,
//                                         const std::string& path,
//                                         const std::string& key,
//                                         const Closure& closure) {
//   nlohmann::json j = {
//       {"path", path},
//       {"key", key},
//   };
//   std::string json_param = j.dump();
//   SetVisualFilterProperty(filter_id, owner_canvas_item_id,
//                           kComposerSetBackgroundKey, json_param.c_str(),
//                           closure);
// }

// // FilterComposerGetExclusion: Get composer exclusion
// // id: filter id
// // node_path: path of node
// // node_tag: tag of node
// // closure: Asynchronous callback
// //    result, MediaSDKString:
// //  {"action":"get_composer_exclusion", "isExclusion": int}
// // Notice: You can use VisualFilterAction directly
// //   -id: filter id
// //   -action: kComposerGetExclusionKey
// //   -action_param: json, e.g. {"nodePath":"path1", "nodeTag": "tag1"}
// //   -closure
// inline void FilterComposerGetExclusion(const char* filter_id,
//                                        const char* owner_canvas_item_id,
//                                        const std::string& node_path,
//                                        const std::string& node_tag,
//                                        const Closure& closure) {
//   nlohmann::json j = {
//       {"nodePath", node_path},
//       {"nodeTag", node_tag},
//   };
//   std::string json_param = j.dump();
//   VisualFilterAction(filter_id, owner_canvas_item_id,
//   kComposerGetExclusionKey,
//                      json_param.c_str(), closure);
// }

// // FilterGetEffectFps: Get composer exclusion
// // id: filter id
// // closure: Asynchronous callback
// //    result, MediaSDKString:
// // {
// //   "action":"action_get_effct_fps",
// //   "effectFps":float,
// //   "effectAchieveRate": double
// // }
// // Notice: You can use VisualFilterAction directly
// //   -id: filter id
// //   -action: kActionGetEffectFps
// //   -action_param: nullptr or ""
// //   -closure
// inline void FilterGetEffectFps(const char* filter_id,
//                                const char* owner_canvas_item_id,
//                                const Closure& closure) {
//   VisualFilterAction(filter_id, owner_canvas_item_id, kActionGetEffectFps,
//                      nullptr, closure);
// }

#endif  //_EFFECT_VISUAL_FILTER_NO_NEED_HELPER_FUNCTIONS

}  // namespace mediasdk::effect_visual_filter