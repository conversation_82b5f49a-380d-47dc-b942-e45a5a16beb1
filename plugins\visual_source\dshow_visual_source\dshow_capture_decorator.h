#pragma once

#include "dshow_capture_task_thread.h"

namespace mediasdk {

class DShowCaptureDecorator {
 public:
  DShowCaptureDecorator(DShowVideoFrameNotify* video_notify,
                        DShowAudioFrameNotify* audio_notify,
                        DShowOperationNotify* operation_notify);

  virtual ~DShowCaptureDecorator();

  bool Create(dshow_visual_source::CaptureType type,
              const nlohmann::json& json_root);

  bool Create(const nlohmann::json& json_root);

  void Destroy();

  nlohmann::json GetCreateJson();

  void CleanVideoTransFormBuffers();

  void SetCameraConfig(const CameraConfig& camera_config);

  int32_t GetCreateIndex();

  std::vector<VideoProcAmpStruct> GetVideoProcAmp();

  bool SetVideoProcAmp(VideoProcAmpProperty pro, long value, long flag);

  std::vector<CameraControlStruct> GetCameraControl();

  bool SetCameraControl(CameraControlProperty pro, long value, long flag);

  void SetLimitCaptureFPS(int limit_fps);

  void SetRenderFPS(int render_fps);

  void StartVideoRangeDetect(const int32_t detect_interval,
                             const int32_t limited_detect_count);

  void StopVideoRangeDetect();

  VideoCaptureFormat GetCaptureVideoFormat();

  bool Continue();

  bool Pause();

  bool IsPaused();

  bool HasAudio();

  bool GetState(long time_out_ms, FILTER_STATE& state);

  bool IsRunningOrPause(FILTER_STATE& state);

  dshow_visual_source::CreateStatus GetLastCreateError();

  const char* GetSubTypeName() const;

 protected:
  int GetCreateTimeoutMS(const nlohmann::json& json_root);

  std::mutex create_json_mutex_;
  nlohmann::json create_json_root_;

  int create_time_out_ms_;
  DShowVideoFrameNotify* video_notify_;
  DShowAudioFrameNotify* audio_notify_;
  DShowOperationNotify* operation_notify_;

  CameraConfig camera_config_ = {};
  std::atomic_int32_t create_index_;

  std::unique_ptr<DShowCaptureTaskThread> thread_;
  dshow_visual_source::CaptureType capture_type_ =
      dshow_visual_source::kCaptureTypeCamera;
};

}  // namespace mediasdk
