#include "video_controller_impl.h"

#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/public/mediasdk_defines.h"

#include "base/strings/stringprintf.h"
#include "base/strings/sys_string_conversions.h"
#include "data_center/monitor_collector.h"
#include "math_helper.h"
#include "mediasdk/component_proxy.h"
#include "mediasdk/video/canvas_tracker.h"
#include "mediasdk/video/render_pump.h"
#include "mediasdk/video/video_model_manager.h"
#include "mediasdk/video/visual_canvas_item.h"
#include "mediasdk/video/visual_controller.h"
#include "mediasdk/video/win32/preview_window.h"
#include "mediasdk/video/win32/virtual_camera/virtual_camera_manager.h"
#include "preview_manager.h"

namespace {
constexpr int kDefaultFps = 60;
}

namespace mediasdk {

VideoControllerImpl::VideoControllerImpl() = default;

bool VideoControllerImpl::Initialize() {
  LOG(INFO) << "[VideoControllerImpl] Beginning Initialize.";

  graphics::CreateDeviceOption device_opt = {};
  if (auto dc = com::GetDataCenter(); dc) {
    auto adapter_info = dc->GetCurrentAdapterInfo();
    device_opt.adapter_id = adapter_info.adapter_id;
  }

  device_ = graphics::CreateDevice(device_opt);
  if (!device_) {
    LOG(ERROR) << "Failed to create device for device id[low: "
               << device_opt.adapter_id.low
               << " high: " << device_opt.adapter_id.high << "]";
    return false;
  }
  visual_controller_ = std::make_shared<VisualController>(device_, *this);
  render_pump_ = std::make_shared<RenderPump>();
  model_manager_ = std::make_shared<VideoModelManager>(this);
  render_pump_->AddObserver(model_manager_.get());
  render_pump_->Start(kDefaultFps);
  LOG(INFO) << "[VideoControllerImpl] Initialized.";
  return true;
}

void VideoControllerImpl::Uninitialize() {
  LOG(INFO) << "[VideoControllerImpl] Beginning Uninitialize.";
  if (render_pump_) {
    render_pump_->Stop();
    render_pump_->RemoveObserver(model_manager_.get());
    render_pump_.reset();
  }

  model_manager_.reset();
  visual_controller_.reset();
  device_.reset();
  LOG(INFO) << "[VideoControllerImpl] Uninitialized.";
}

bool VideoControllerImpl::AddModel(uint32_t sink_id,
                                   const ModelParams& params) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] AddModel(%d), \n\toutput_size(%d, "
      "%d),\n\tcolor_space(%d),\n\tvideo_range(%d),\n\tfps(%."
      "2f),\n\thwnd_parent(%lld), \n\tauto_top_zorder(%d), "
      "\n\tshow_window(%d), \n\twindow_rect(%d, %d, %d, %d), "
      "\n\tadapter_luid(%x, %x)",
      sink_id, params.output_size.cx, params.output_size.cy,
      (int)params.color_space, (int)params.video_range, params.fps,
      (uint64_t)params.hwnd_parent, params.auto_top_zorder ? 1 : 0,
      params.show_window ? 1 : 0, params.window_rect.x, params.window_rect.y,
      params.window_rect.cx, params.window_rect.cy, params.adapter_luid.high,
      params.adapter_luid.low);

  if (model_manager_) {
    return model_manager_->AddModel(sink_id, params);
  }
  return false;
}

bool VideoControllerImpl::RemoveModel(uint32_t sink_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] RemoveModel(%d)",
                                  sink_id);
  bool ret = false;
  if (model_manager_) {
    ret = model_manager_->RemoveModel(sink_id);
  }
  if (visual_controller_) {
    visual_controller_->OnModelRemoved(sink_id);
  }
  return ret;
}

bool VideoControllerImpl::IsCurrentMonitor(const MonitorInfo& info) {
  if (!model_manager_) {
    return false;
  }
  auto model = model_manager_->GetModel(0);
  if (!model) {
    return false;
  }
  auto window = model->GetPreviewWindow();
  if (!window) {
    return false;
  }
  auto id = window->GetId();
  if (id) {
    return monitor_collector::MonitorCollector::IsWindowCurrentMonitor(
        id, info.monitor_handle);
  }
  return false;
}

ResultBoolMSRect VideoControllerImpl::GetPreviewPosition(uint32_t sink_id) {
  if (model_manager_) {
    return {true, model_manager_->GetPreviewPosition(sink_id)};
  }
  return {false, MSRect{0, 0, 0, 0}};
}

void VideoControllerImpl::SetPreviewPosition(uint32_t sink_id,
                                             const MSRect& rect,
                                             MSCallbackBool callback) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetPreviewPosition(%d), rect(%d, %d, %d, %d)",
      sink_id, rect.x, rect.y, rect.cx, rect.cy);
  if (model_manager_ && model_manager_->SetPreviewPosition(sink_id, rect)) {
    std::move(callback).Resolve(true);
  } else {
    std::move(callback).Resolve(false);
  }
}

void VideoControllerImpl::SetAndClipPreviewPosition(uint32_t sink_id,
                                                    const MSRect& pos_rect,
                                                    const MSClip& clip_rect,
                                                    MSCallbackBool callback) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetAndClipPreviewPosition(%d), \n\tpos_rect(%d, "
      "%d, %d, %d), \n\tclip_rect(%d, %d, %d, %d)",
      sink_id, pos_rect.x, pos_rect.y, pos_rect.cx, pos_rect.cy, clip_rect.x,
      clip_rect.y, clip_rect.z, clip_rect.w);
  if (model_manager_ &&
      model_manager_->SetAndClipPreviewPosition(sink_id, pos_rect, clip_rect)) {
    std::move(callback).Resolve(true);
  } else {
    std::move(callback).Resolve(false);
  }
}

bool VideoControllerImpl::EnableAllPreview(bool enable) {
  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] EnableAllPreview(%d)",
                                  enable ? 1 : 0);
  if (model_manager_) {
    model_manager_->EnableAllPreview(enable);
    return true;
  }
  return false;
}

bool VideoControllerImpl::EnablePreviewWithSinkId(uint32_t sink_id,
                                                  bool enable) {
  LOG(INFO) << "[VideoControllerImpl] EnablePreviewWithSinkId[" << sink_id
            << (enable ? "]enable" : "]disable");
  if (model_manager_) {
    return model_manager_->EnablePreviewWithSinkId(sink_id, enable);
  }
  return false;
}

ResultBoolBool VideoControllerImpl::IsAllPreviewEnable() {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  if (model_manager_) {
    return {true, model_manager_->IsAllPreviewEnable()};
  }
  return {false, false};
}

ResultBoolBool VideoControllerImpl::IsPreviewEnableWithSinkId(
    uint32_t sink_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  if (model_manager_) {
    return {true, model_manager_->IsPreviewEnable(sink_id)};
  }
  return {false, false};
}

bool VideoControllerImpl::SetVideoModelActive(uint32_t video_model_id,
                                              bool is_active) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO) << "[VideoControllerImpl] SetVideoModelActive(video_model:"
            << video_model_id << ":" << (is_active ? "true" : "false") << ")";
  if (model_manager_) {
    return model_manager_->SetActive(video_model_id, is_active);
  }
  return false;
}

ResultBoolBool VideoControllerImpl::IsVideoModelActive(
    uint32_t video_model_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO) << "[VideoControllerImpl] IsVideoModelActive(" << video_model_id
            << ")";
  if (model_manager_) {
    return model_manager_->IsActive(video_model_id);
  }
  return {false, false};
}

bool VideoControllerImpl::SetScaleForAllItems(uint32_t sink_id, float scale) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetScaleForAllItems(%d, %.2f)", sink_id, scale);
  if (model_manager_ && visual_controller_) {
    auto canvas_manager = visual_controller_->GetCanvasManager();
    if (canvas_manager) {
      return canvas_manager->SetScaleForItemsByVideoModel(sink_id, scale);
    } else {
      LOG(ERROR) << "[VideoControllerImpl] can not find canvas manager";
    }
  } else {
    LOG(ERROR) << "[VideoControllerImpl] can not find model manager or "
                  "visual controller";
  }
  return false;
}

bool VideoControllerImpl::CreateProjector(const std::string& canvas_project_id,
                                          uint64_t hwnd_parent,
                                          uint32_t sink_id,
                                          const MSRect& rect,
                                          const WndParams& params) {
  LOG(INFO) << base::StringPrintf(
                   "[VideoControllerImpl] CreateProjector, "
                   "canvas_project_id(%s)\n\thwnd_parent(%lld), "
                   "\n\tsink_id(%d), \n\trect(%d,%d,%d,%d)",
                   canvas_project_id.c_str(), hwnd_parent, sink_id, rect.x,
                   rect.y, rect.cx, rect.cy)
            << base::StringPrintf(
                   "opacity(%f), \n\ris_popUp(%d), \n\rtop_border_radius(%f), "
                   "\n\rbottom_border_radius(%f)",
                   params.opacity, params.is_popUp, params.top_border_radius,
                   params.bottom_border_radius);

  if (model_manager_) {
    return model_manager_->CreateProjector(canvas_project_id, hwnd_parent,
                                           sink_id, rect, params);
  }
  return false;
}

bool VideoControllerImpl::CloseProjector(const std::string& canvas_project_id,
                                         uint32_t sink_id) {
  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] CloseProjector(%s)",
                                  canvas_project_id.c_str())
            << " sink_id  = " << sink_id;
  if (model_manager_) {
    return model_manager_->CloseProjector(canvas_project_id, sink_id);
  }
  return false;
}

bool VideoControllerImpl::SetProjectorWndParams(
    const std::string& canvas_project_id,
    uint32_t sink_id,
    const WndParams& params) {
  LOG(INFO) << base::StringPrintf(
                   "[VideoControllerImpl] SetProjectorWndParams(%s)",
                   canvas_project_id.c_str())
            << base::StringPrintf(
                   "opacity(%f), \n\ris_popUp(%d), \n\rtop_border_radius(%f), "
                   "\n\rbottom_border_radius(%f)",
                   params.opacity, params.is_popUp, params.top_border_radius,
                   params.bottom_border_radius);

  if (model_manager_) {
    return model_manager_->SetProjectorWndParams(canvas_project_id, sink_id,
                                                 params);
  }
  return false;
}

bool VideoControllerImpl::SetProjectorPosition(
    const std::string& canvas_project_id,
    const int32_t sink_id,
    const MSRect& rect) {
  LOG(INFO) << base::StringPrintf(
                   "[VideoControllerImpl] SetProjectorPosition(%s)",
                   canvas_project_id.c_str())
            << " sink_id = " << sink_id;
  if (model_manager_) {
    return model_manager_->SetProjectorPosition(canvas_project_id, sink_id,
                                                rect);
  }
  return false;
}

bool VideoControllerImpl::StartCanvasItemPreview(
    const std::string& canvas_item_id,
    const std::string& preview_id,
    uint64_t parent_wnd,
    MSRect region,
    int fill_type,
    bool flip_h,
    bool flip_v,
    float angle,
    int32_t bk_color,
    const WndParams& params) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] StartCanvasItemPreview(canvas_item_id:%s, "
      "preview_id:%s), "
      "\n\tparent_wnd(%lld), "
      "\n\tregion(%d, %d, %d, %d), \n\tfill_type(%d), \n\tflip_h(%d), "
      "\n\tflip_v(%d),"
      "\n\tangle(%.2f), bk_color(%d)",
      canvas_item_id.c_str(), preview_id.c_str(), parent_wnd, region.x,
      region.y, region.cx, region.cy, fill_type, flip_h ? 1 : 0, flip_v ? 1 : 0,
      angle, bk_color);

  if (!visual_controller_) {
    return false;
  }

  return visual_controller_->StartCanvasItemPreview(
      canvas_item_id, preview_id, parent_wnd, region, fill_type, flip_h, flip_v,
      angle, bk_color, params);
}

bool VideoControllerImpl::SetCanvasItemPreviewParams(
    const std::string& preview_id,
    const WndParams& params) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemPreviewParams(preview_id:%s), "
      "\n\ropacity(%f), \n\ris_popUp(%d), \n\rtop_border_radius(%f), "
      "\n\rbottom_border_radius(%f)",
      preview_id.c_str(), params.opacity, params.is_popUp,
      params.top_border_radius, params.bottom_border_radius);

  if (!visual_controller_) {
    return false;
  }

  visual_controller_->SetCanvasItemPreviewWndParams(preview_id, params);
  return true;
}

bool VideoControllerImpl::StopCanvasItemPreview(const std::string& preview_id) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] StopCanvasItemPreview(%s)", preview_id.c_str());

  if (!visual_controller_) {
    return false;
  }

  visual_controller_->StopCanvasItemPreview(preview_id);
  return true;
}

bool VideoControllerImpl::SetCanvasItemPreviewPos(const std::string& preview_id,
                                                  MSRect region) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemPreviewPos(%s), region(%d, %d, %d, "
      "%d)",
      preview_id.c_str(), region.x, region.y, region.cx, region.cy);

  if (!visual_controller_) {
    return false;
  }

  return visual_controller_->SetCanvasItemPreviewPos(preview_id, region);
}

bool VideoControllerImpl::SetCanvasItemPreviewFlipH(
    const std::string& preview_id,
    bool flip_h) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemPreviewFlipH(%s) flip_h(%d)",
      preview_id.c_str(), flip_h ? 1 : 0);

  if (!visual_controller_) {
    return false;
  }

  return visual_controller_->SetCanvasItemPreviewFlipH(preview_id, flip_h);
}

bool VideoControllerImpl::SetCanvasItemPreviewFlipV(
    const std::string& preview_id,
    bool flip_v) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemPreviewFlipV(%s) flip_v(%d)",
      preview_id.c_str(), flip_v ? 1 : 0);

  if (!visual_controller_) {
    return false;
  }

  return visual_controller_->SetCanvasItemPreviewFlipV(preview_id, flip_v);
}

bool VideoControllerImpl::SetCanvasItemPreviewRotate(
    const std::string& preview_id,
    float rotate) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemPreviewRotate(%s) rotate(%.2f)",
      preview_id.c_str(), rotate);

  if (!visual_controller_) {
    return false;
  }

  return visual_controller_->SetCanvasItemPreviewRotate(preview_id, rotate);
}

bool VideoControllerImpl::SetUIConfig(uint32_t sink_id,
                                      const PreviewUIConfig& params) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetUIConfig(%d) params(%d, %d, %d, %d)", sink_id,
      params.adsorption_color, params.edge_color, params.highlight_color,
      params.selected_color);
  if (model_manager_) {
    auto model = model_manager_->GetModel(sink_id);
    if (!model) {
      return false;
    }
    auto preview = model->GetPreviewWindow();
    if (!preview) {
      return false;
    }
    preview->SetUIConfig(params);
    return true;
  } else {
    return false;
  }
}

bool VideoControllerImpl::EnableTrack(uint32_t sink_id, bool enable) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] EnableTrack(%d) enable(%d)", sink_id,
      enable ? 1 : 0);
  if (visual_controller_) {
    visual_controller_->EnableTrack(sink_id, enable);
    return true;
  }
  return false;
}

bool VideoControllerImpl::EnableFineTuning(uint32_t sink_id, bool enable) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] EnableFineTuning(%d) enable(%d)", sink_id,
      enable ? 1 : 0);
  if (visual_controller_) {
    visual_controller_->FineTuning(sink_id, enable);
    return true;
  }
  return false;
}

bool VideoControllerImpl::MoveCanvasItemZOrder(
    const std::string& canvas_item_id,
    MovePostion pos) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] EnableFineTuning(%s) pos(%d)",
      canvas_item_id.c_str(), (int)pos);
  if (visual_controller_) {
    auto canvas = visual_controller_->GetCanvasByItemId(canvas_item_id);
    if (canvas) {
      canvas->MoveCanvasItemZOrder(canvas_item_id, pos);
      return true;
    }
  }
  return false;
}

std::shared_ptr<MediaSDKStringArray>
VideoControllerImpl::GetCanvasItemOrderIDSOnVideoModel(
    uint32_t video_model_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] GetCanvasItemOrderIDSOnVideoModel(%d)",
      video_model_id);
  std::vector<MediaSDKString> result;
  if (visual_controller_) {
    auto canvas_mgr = visual_controller_->GetCanvasManager();
    if (canvas_mgr) {
      result = canvas_mgr->GetCanvasItemOrderIDS(
          canvas_mgr->GetCurrentCanvas(video_model_id));
      return std::make_shared<MediaSDKStringArray>(result);
    }
  }
  return nullptr;
}

bool VideoControllerImpl::SetCanvasItemByOrderIDSOnVideoModel(
    uint32_t video_model_id,
    const MediaSDKStringArray& array) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemByOrderIDSOnVideoModel(%d)",
      video_model_id);

  if (visual_controller_) {
    auto canvas_mgr = visual_controller_->GetCanvasManager();
    if (canvas_mgr) {
      return canvas_mgr->SetCanvasItemByOrderIDS(
          canvas_mgr->GetCurrentCanvas(video_model_id), array.ToVector());
    }
  }
  return false;
}

std::shared_ptr<MediaSDKStringArray> VideoControllerImpl::GetCanvasItemOrderIDS(
    const std::string& canvas_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] GetCanvasItemOrderIDS(%s)", canvas_id.c_str());
  std::vector<MediaSDKString> result;
  if (visual_controller_) {
    auto canvas_mgr = visual_controller_->GetCanvasManager();
    if (canvas_mgr) {
      result = canvas_mgr->GetCanvasItemOrderIDS(canvas_id);
      return std::make_shared<MediaSDKStringArray>(result);
    }
  }
  return nullptr;
}

bool VideoControllerImpl::SetCanvasItemByOrderIDS(
    const std::string& canvas_id,
    const MediaSDKStringArray& array) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemByOrderIDS(%s)", canvas_id.c_str());
  if (visual_controller_) {
    auto canvas_mgr = visual_controller_->GetCanvasManager();
    if (canvas_mgr) {
      return canvas_mgr->SetCanvasItemByOrderIDS(canvas_id, array.ToVector());
    }
  }
  return false;
}

void VideoControllerImpl::CreateVisual(const std::string& id,
                                       const CreateVisualParams& params,
                                       MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] CreateVisual:"
            << " visual id:" << id
            << "; plugin name: " << params.plugin_name.data()
            << "; destroy flags: " << params.destroy_when_all_ref_removed
            << "; json: " << params.json_params.data()
            << "; track: " << params.audio_track_id;

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has not visual controller";
    std::move(callback).Resolve(false);
    return;
  }
  auto visual_manager = visual_controller_->GetVisualManager();
  if (!visual_manager) {
    LOG(ERROR) << "[VideoControllerImpl] has not visual_manager";
    std::move(callback).Resolve(false);
    return;
  }
  visual_manager->CreateVisual(id, params, std::move(callback));
}

void VideoControllerImpl::RecreateVisual(const std::string& id,
                                         const CreateVisualParams& params,
                                         MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] RecreateVisual:"
            << " visual id:" << id
            << "; plugin name: " << params.plugin_name.data()
            << "; destroy flags: " << params.destroy_when_all_ref_removed
            << "; json: " << params.json_params.data()
            << "; track: " << params.audio_track_id;
  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has not visual controller";
    std::move(callback).Resolve(false);
    return;
  }
  auto visual_manager = visual_controller_->GetVisualManager();
  if (!visual_manager) {
    LOG(ERROR) << "[VideoControllerImpl] has not visual_manager";
    std::move(callback).Resolve(false);
    return;
  }
  visual_manager->RecreateVisualSourceWithParams(id, params,
                                                 std::move(callback));
}

void VideoControllerImpl::CreateCanvasItemWithFilter(
    const std::string& canvas_item_id,
    const std::string& canvas_id,
    const std::string& visual_id,
    const CreateCanvasItemParams& params,
    const std::string filter_id,
    const CreateVisualFilterParams& filter_params,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO)
      << "[VideoControllerImpl] CreateCanvasItemWithFilter canvas item:"
      << canvas_item_id << ", canvas:" << canvas_id
      << ", visual_id:" << visual_id << ", filter_id:" << filter_id
      << ", filter_name:" << filter_params.filter_name.ToString()
      << ", filter_params:" << filter_params.json_params.ToString()
      << ", canvas_visible: " << params.is_visible << ", canvas_transform: "
      << graphics::Transform::FromMsTransform(params.transform).ToString();

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has not visual controller";
    std::move(callback).Resolve(false);
    return;
  }

  visual_controller_->CreateCanvasItemWithFilter(
      canvas_item_id, canvas_id, visual_id, params, filter_id, filter_params,
      std::move(callback));
}

void VideoControllerImpl::CreateVisualThenCreateCanvasItemWithFilter(
    const std::string& visual_id,
    const CreateVisualParams& visual_params,
    const std::string& canvas_item_id,
    const std::string& canvas_id,
    const CreateCanvasItemParams& item_params,
    const std::string& filter_id,
    const CreateVisualFilterParams& filter_params,
    MSCallbackBool callback) {
  if (visual_controller_) {
    visual_controller_->CreateVisualThenCreateCanvasItemWithFilter(
        visual_id, visual_params, canvas_item_id, canvas_id, item_params,
        filter_id, filter_params, std::move(callback));
  } else {
    std::move(callback).Resolve(false);
  }
}

void VideoControllerImpl::ReopenVisual(const std::string& id,
                                       const std::string& json_params,
                                       MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] ReopenVisual(%s)"
      "\n\tjson_params(%s)",
      id.c_str(), json_params.c_str());

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has not visual controller";
    std::move(callback).Resolve(false);
    return;
  }
  auto visual = visual_controller_->GetVisual(id);
  if (!visual) {
    LOG(ERROR) << "[VideoControllerImpl] can not find visual with id:" << id;
    std::move(callback).Resolve(false);
    return;
  }
  visual->Reopen(json_params, std::move(callback));
}

void VideoControllerImpl::ReopenVisualWithTransform(
    const std::string& id,
    const std::string& json_params,
    const MediaSDKArray<ReopenParam>& reopen_params,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  LOG(INFO) << "[VideoControllerImpl] ReopenVisualWithTransform, visual:" << id
            << ", param:" << json_params;

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has not visual controller";
    std::move(callback).Resolve(false);
    return;
  }

  auto visual = visual_controller_->GetVisual(id);
  if (!visual) {
    LOG(ERROR) << "[VideoControllerImpl] can not find visual with id:" << id;
    std::move(callback).Resolve(false);
    return;
  }

  std::vector<ReopenParam> reopen_params_list = reopen_params.ToVector();
  for (const auto& param : reopen_params_list) {
    const auto canvas_item =
        visual_controller_->GetCanvasItem(param.canvas_item_id.ToString());
    if (auto visual_canvas_item =
            std::dynamic_pointer_cast<VisualCanvasItem>(canvas_item)) {
      visual_canvas_item->PresetTransAfterReopen(param.target_transform);
    }
  }
  visual->Reopen(json_params, std::move(callback));
}

void VideoControllerImpl::PauseVisualCapture(const std::string& id,
                                             MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] PauseVisualCapture(%s)", id.c_str());
  if (!visual_controller_) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return;
  }
  auto visual = visual_controller_->GetVisual(id);
  if (!visual) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] can not find visual";
    return;
  }
  visual->Pause(std::move(callback));
}

void VideoControllerImpl::IsVisualCapturePause(
    const std::string& id,
    MSCallback<ResultBoolBool> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] IsVisualCapturePause(%s)", id.c_str());

  if (!visual_controller_) {
    std::move(callback).Resolve({false, false});
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return;
  }
  auto visual = visual_controller_->GetVisual(id);
  if (!visual) {
    std::move(callback).Resolve({false, false});
    LOG(ERROR) << "[VideoControllerImpl] can not find visual";
    return;
  }
  visual->IsPause(std::move(callback));
}

void VideoControllerImpl::ContinueVisualCapture(const std::string& id,
                                                MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] ContinueVisualCapture(%s)", id.c_str());

  if (!visual_controller_) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return;
  }
  auto visual = visual_controller_->GetVisual(id);
  if (!visual) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] can not find visual";
    return;
  }
  visual->Continue(std::move(callback));
}

bool VideoControllerImpl::BeginCanvasItemClip(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] BeginCanvasItemClip(%s)", id.c_str());
  if (visual_controller_) {
    return visual_controller_->BeginCanvasItemClip(id);
  }
  return false;
}

bool VideoControllerImpl::EndCanvasItemClip(uint32_t sink_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] EndCanvasItemClip");

  if (visual_controller_) {
    return visual_controller_->EndCanvasItemClip(sink_id);
  }
  return false;
}

ResultBoolBool VideoControllerImpl::IsCanvasItemClip(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      return {true, item->IsClipping()};
    }
  }
  return {false, false};
}

ResultBoolBool VideoControllerImpl::EnableCanvasItemClip(const std::string& id,
                                                         bool enable) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] EnableCanvasItemClip(%s), enable(%d)", id.c_str(),
      enable ? 1 : 0);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetClipping(enable);
      return {true, true};
    }
  }
  return {false, false};
}

void VideoControllerImpl::DestroyVisual(const std::string& id,
                                        MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] DestroyVisual(%s)",
                                  id.c_str());
  if (!visual_controller_) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return;
  }

  auto visual_manager = visual_controller_->GetVisualManager();
  if (!visual_manager) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_manager";
    return;
  }

  visual_manager->DestroyVisual(id, std::move(callback));
}

std::shared_ptr<MediaSDKString> VideoControllerImpl::GetCurrentCanvasItem(
    const std::string& canvas_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  auto ret = std::make_shared<MediaSDKString>();
  if (visual_controller_) {
    auto item = visual_controller_->GetCurrentCanvasItem(canvas_id);
    if (item) {
      *ret = item->GetId();
    }
  }
  LOG(INFO) << "Get current canvas item : " << ret->ToString()
            << ", canvas: " << canvas_id;
  return ret;
}

std::shared_ptr<MediaSDKString>
VideoControllerImpl::GetCurrentCanvasItemOnVideoModel(uint32_t sink_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  auto ret = std::make_shared<MediaSDKString>();
  if (visual_controller_) {
    auto item = visual_controller_->GetCurrentCanvasItemOfVideoModel(sink_id);
    if (item) {
      *ret = item->GetId();
    }
  }
  LOG(INFO) << "Get current canvas item : " << ret->ToString()
            << ", video model: " << sink_id;
  return ret;
}

bool VideoControllerImpl::SetCurrentCanvasItemOnVideoModel(
    uint32_t video_model_id,
    const std::string& item_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO)
      << "[VideoControllerImpl] SetCurrentCanvasItemOnVideoModel video_mode_id:"
      << video_model_id << "; item_id:" << item_id;
  if (!visual_controller_) {
    return false;
  }

  auto canvas_mgr = visual_controller_->GetCanvasManager();
  if (!canvas_mgr) {
    return false;
  }

  auto canvas = canvas_mgr->GetCurrentCanvas(video_model_id);
  if (!canvas) {
    return false;
  }

  if (!canvas_mgr->SetCurrentItemForCanvas(item_id, canvas->GetCanvasId())) {
    LOG(ERROR) << "Failed to set SetCurrentItemForCanvas with item_id:"
               << item_id;
    return false;
  }

  return true;
}

bool VideoControllerImpl::SetCurrentCanvasItem(const std::string& canvas_id,
                                               const std::string& item_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCurrentCanvasItem(canvas:%s, item:%s)",
      canvas_id.c_str(), item_id.c_str());
  if (!visual_controller_) {
    return false;
  }
  auto canvas_mgr = visual_controller_->GetCanvasManager();
  if (!canvas_mgr) {
    return false;
  }
  if (!canvas_mgr->SetCurrentItemForCanvas(item_id, canvas_id)) {
    return false;
  }
  return true;
}

bool VideoControllerImpl::SetCanvasItemHighlight(
    uint32_t sink_id,
    const std::string& canvas_item_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemHighlight(%d, %s)", sink_id,
      canvas_item_id.c_str());
  if (!visual_controller_) {
    return false;
  }
  auto canvas_mgr = visual_controller_->GetCanvasManager();
  if (!canvas_mgr) {
    return false;
  }
  auto item = canvas_mgr->GetCanvasItem(canvas_item_id);
  return canvas_mgr->SetHoveredItemForVideoModel(sink_id, item);
}

bool VideoControllerImpl::SetCanvasItemRotate(const std::string& id,
                                              float rotate) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemRotate(%s, %.2f)", id.c_str(),
      rotate);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetRotate(rotate);
      return true;
    }
  }
  return false;
}

ResultBoolFloat VideoControllerImpl::GetCanvasItemRotate(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      return {true, item->GetRotate()};
    }
  }
  return {false, 0};
}

bool VideoControllerImpl::SetCanvasItemScale(const std::string& id,
                                             const MSScaleF& scale) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemScale(%s, %.6f x %.6f)", id.c_str(),
      scale.x, scale.y);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetScale({scale.x, scale.y});
      return true;
    }
  }
  return false;
}

ResultBoolMSScaleF VideoControllerImpl::GetCanvasItemScale(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto scale = item->GetScale();
      return {true, MSScaleF{scale.x, scale.y}};
    }
  }
  return {false, MSScaleF{-1.0f, -1.0f}};
}

bool VideoControllerImpl::SetCanvasItemMinScale(const std::string& id,
                                                const MSScaleF& scale) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      const auto pre = item->GetMinScale();
      if (!IsNearEqual(pre.x, scale.x) || !IsNearEqual(pre.y, scale.y)) {
        LOG(INFO) << base::StringPrintf(
            "[VideoControllerImpl] SetCanvasItemMinScale(%s, %.6f x %.6f)",
            id.c_str(), scale.x, scale.y);
      }
      item->SetMinScale(DirectX::XMFLOAT2{scale.x, scale.y});
      return true;
    }
  }
  return false;
}

ResultBoolMSScaleF VideoControllerImpl::GetCanvasItemMinScale(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto scale = item->GetMinScale();
      return {true, MSScaleF{scale.x, scale.y}};
    }
  }
  return {false, MSScaleF{-1.0f, -1.0f}};
}

bool VideoControllerImpl::SetCanvasItemMaxScale(const std::string& id,
                                                const MSScaleF& scale) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      const auto pre = item->GetMaxScale();
      if (!IsNearEqual(pre.x, scale.x) || !IsNearEqual(pre.y, scale.y)) {
        LOG(INFO) << base::StringPrintf(
            "[VideoControllerImpl] SetCanvasItemMaxScale(%s, %.6f x %.6f)",
            id.c_str(), scale.x, scale.y);
      }
      item->SetMaxScale(DirectX::XMFLOAT2{scale.x, scale.y});
      return true;
    }
  }
  return false;
}

ResultBoolMSScaleF VideoControllerImpl::GetCanvasItemMaxScale(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto scale = item->GetMaxScale();
      return {true, MSScaleF{scale.x, scale.y}};
    }
  }
  return {false, MSScaleF{-1.0f, -1.0f}};
}

ResultBoolBool VideoControllerImpl::GetCanvasItemFlipH(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      return {true, item->IsFlipH()};
    }
  }
  return {false, false};
}

bool VideoControllerImpl::SetCanvasItemFlipH(const std::string& id, bool flip) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemFlipH(%s, %d)", id.c_str(),
      flip ? 1 : 0);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetFlipH(flip);
      return true;
    }
  }
  return false;
}

ResultBoolBool VideoControllerImpl::GetCanvasItemFlipV(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      return {true, item->IsFlipV()};
    }
  }
  return {false, false};
}

bool VideoControllerImpl::SetCanvasItemFlipV(const std::string& id, bool flip) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemFlipV(%s, %d)", id.c_str(),
      flip ? 1 : 0);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetFlipV(flip);
      return true;
    }
  }
  return false;
}

bool VideoControllerImpl::SetCanvasItemMoveRange(const std::string& id,
                                                 const MSClipF& move_range) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemMoveRange(%s) move_range(%.2f, %.2f, "
      "%.2f, %.2f)",
      id.c_str(), move_range.x, move_range.y, move_range.z, move_range.w);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetMovableRange(std::make_optional<MovableRange>(move_range));
      return true;
    }
  }
  return false;
}

ResultBoolMSClipF VideoControllerImpl::GetCanvasItemMoveRange(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] GetCanvasItemMoveRange: visual id: "
            << id;
  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto range = item->GetMovableRange();
      if (range) {
        return {true, range->GetRange()};
      }
    }
  }
  return {false, MSClipF{0.0f, 0.0f, 0.0f, 0.0f}};
}

bool VideoControllerImpl::SetCanvasItemAlwaysTop(const std::string& id,
                                                 bool always_top) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemAlwaysTop(%s) always_top(%d)",
      id.c_str(), always_top ? 1 : 0);

  if (!visual_controller_) {
    return false;
  }
  auto canvas_item = visual_controller_->GetCanvasItem(id);
  if (!canvas_item) {
    return false;
  }
  canvas_item->SetIsAlwaysTop(always_top);
  return true;
}

bool VideoControllerImpl::SetCanvasItemAvoidOutput(const std::string& id,
                                                   bool avoid_output) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemAvoidOutput(%s) avoid_output(%d)",
      id.c_str(), avoid_output ? 1 : 0);

  if (!visual_controller_) {
    return false;
  }
  auto canvas_item = visual_controller_->GetCanvasItem(id);
  if (!canvas_item) {
    return false;
  }
  canvas_item->SetAvoidOutput(avoid_output);
  return true;
}

bool VideoControllerImpl::CanvasItemSaveAsFile(
    const std::string& canvas_item_id,
    const std::string& file_path,
    ImageFileFormat format) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] CanvasItemSaveAsFile(%s) file_path(%s) format(%d)",
      canvas_item_id.c_str(), file_path.c_str(), (int)format);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(canvas_item_id);
    if (item) {
      return item->SaveAs(file_path, format);
    }
  }

  return false;
}

bool VideoControllerImpl::OutputThumbnailSaveAs(const uint32_t sink_id,
                                                const std::string& file_path,
                                                ImageFileFormat format) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] OutputThumbnailSaveAs(%d) file_path(%s) "
      "format(%d)",
      sink_id, file_path.c_str(), (int)format);

  if (model_manager_ && visual_controller_) {
    auto model = model_manager_->GetModel(sink_id);
    auto cr_ptr = visual_controller_->GetCanvasRenderOfVideoModel(sink_id);
    DCHECK(cr_ptr);
    if (model && cr_ptr) {
      return model->SaveAs(file_path, format, cr_ptr);
    }
  }

  return false;
}

bool VideoControllerImpl::LockCanvasItem(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] LockCanvasItem(%d)",
                                  id.c_str());

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetLocked(true);
      return true;
    }
  }
  return false;
}

bool VideoControllerImpl::UnLockCanvasItem(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] UnLockCanvasItem(%s)",
                                  id.c_str());

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetLocked(false);
      return true;
    }
  }
  return false;
}

ResultBoolBool VideoControllerImpl::IsCanvasItemLocked(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      bool locked = item->IsLocked();
      LOG(INFO) << "IsCanvasItemLocked, canvas item id: " << id
                << "is locked: " << locked;
      return {true, locked};
    }
  }
  return {false, false};
}

ResultBoolBool VideoControllerImpl::IsVisualHasAudio(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return {false, false};
  }
  auto visual = visual_controller_->GetVisual(id);
  if (!visual) {
    LOG(ERROR) << "[VideoControllerImpl] has no visual:" << id;
    return {false, false};
  }
  return {true, visual->HasAudio()};
}

ResultBoolFloat VideoControllerImpl::GetVisualFPS(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return {false, 0.0};
  }
  auto visual = visual_controller_->GetVisual(id);
  if (!visual) {
    LOG(ERROR) << "[VideoControllerImpl] has no visual:" << id;
    return {false, 0.0};
  }
  return {true, visual->GetFPS()};
}

ResultBoolBool VideoControllerImpl::UpdateModelFPS(uint32_t sink_id,
                                                   float fps) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] UpdateModelFPS(" << sink_id << ","
            << (int32_t)fps << ");";

  if (!model_manager_) {
    return {false, false};
  }
  auto result = model_manager_->UpdateModelFPS(sink_id, fps);
  if (result.value && result.success) {
    UpdateRenderFPS(static_cast<uint32_t>(fps));
  }
  return result;
}

ResultBoolBool VideoControllerImpl::UpdateModelOutputSize(uint32_t sink_id,
                                                          MSSize size) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] UpdateModelOutputSize(%d, %d x %d)", sink_id,
      size.cx, size.cy);

  if (!model_manager_) {
    return {false, false};
  }
  return model_manager_->UpdateModelOutputSize(sink_id, size);
}

ResultBoolBool VideoControllerImpl::UpdateModelColorSpaceAndVideoRange(
    uint32_t sink_id,
    ColorSpace cs,
    VideoRange vr) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] UpdateModelColorSpaceAndVideoRange(%d, %d x "
      "%d)",
      sink_id, (int)cs, (int)vr);

  if (!model_manager_) {
    return {false, false};
  }
  return model_manager_->UpdateModelColorSpaceAndVideoRange(sink_id, cs, vr);
}

std::shared_ptr<MediaSDKString> VideoControllerImpl::GetCurrentAdapterInfo() {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  if (!device_) {
    return nullptr;
  }
  if (auto dc = com::GetDataCenter(); dc) {
    nlohmann::json json_root;
    auto adapter_info = dc->GetCurrentAdapterInfo();
    json_root["name"] = base::SysWideToUTF8(adapter_info.adapter_name);
    json_root["vendor_id"] = adapter_info.vendor_id;
    json_root["device_id"] = adapter_info.device_id;
    return std::make_shared<MediaSDKString>(json_root.dump());
  }
  return nullptr;
}

ResultBoolBool VideoControllerImpl::GetCanvasItemVisible(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (!visual_controller_) {
    return {false, false};
  }

  auto canvas_item = visual_controller_->GetCanvasItem(id);
  if (!canvas_item) {
    return {false, false};
  }

  return {true, canvas_item->IsVisible()};
}

bool VideoControllerImpl::SetCanvasItemVisible(const std::string& id,
                                               bool visible) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemVisible(%s, %d)", id.c_str(),
      visible ? 1 : 0);

  if (!visual_controller_) {
    return false;
  }

  auto canvas_item = visual_controller_->GetCanvasItem(id);
  if (!canvas_item) {
    LOG(ERROR) << "Failed to find canvas_item with id " << id;
    return false;
  }

  canvas_item->SetVisible(visible);

  return true;
}

ResultBoolBool VideoControllerImpl::GetCanvasItemEditable(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      return {true, item->IsEditable()};
    }
  }
  return {false, false};
}

bool VideoControllerImpl::SetCanvasItemEditable(const std::string& id,
                                                bool editable) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemEditable(%s, %d)", id.c_str(),
      editable ? 1 : 0);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetEditable(editable);
      return true;
    }
  }
  return false;
}

bool VideoControllerImpl::SetCanvasItemTranslate(
    const std::string& id,
    const MSTranslateF& translate) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemTranslate(%s, %.2f x %.2f)",
      id.c_str(), translate.x, translate.y);

  bool ret = false;
  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetTranslate({translate.x, translate.y}, false);
      ret = true;
    }
  }
  return ret;
}

ResultBoolMSTranslateF VideoControllerImpl::GetCanvasItemTranslate(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto translate = item->GetTranslate();
      return {true, MSTranslateF{translate.x, translate.y}};
    }
  }
  return {false, MSTranslateF{0.0f, 0.0f}};
}

ResultBoolMSSizeF VideoControllerImpl::GetCanvasItemSizeF(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto size = item->GetSize();
      return {true, MSSizeF{size.x, size.y}};
    }
  }
  return {false, MSSizeF{0.0f, 0.0f}};
}

ResultBoolMSClipF VideoControllerImpl::GetCanvasItemClip(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto clip = item->GetClip();
      return {true, MSClipF{clip.x, clip.y, clip.z, clip.w}};
    }
  }
  return {false, MSClipF{0.0f, 0.0f, 0.0f, 0.0f}};
}

bool VideoControllerImpl::SetCanvasItemClip(const std::string& id,
                                            const MSClipF& clip) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemClip(%s, %.2f x %.2f x %.2f x %.2f)",
      id.c_str(), clip.x, clip.y, clip.z, clip.w);

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      item->SetClip({clip.x, clip.y, clip.z, clip.w});
      return true;
    }
  }
  return false;
}

bool VideoControllerImpl::SetCanvasItemTransform(const std::string& id,
                                                 const MSTransform& transform) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemTransform(%s)", id.c_str());

  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      auto graphice_transform = graphics::Transform::FromMsTransform(transform);
      LOG(INFO) << "external set[" << id << "] "
                << graphice_transform.ToString() << "]";
      item->SetTransform(graphice_transform);
      return true;
    }
  }
  return false;
}

bool VideoControllerImpl::SetCanvasItemNeedDrawBorder(const std::string& id,
                                                      bool need_border) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetCanvasItemNeedDrawBorder(%s, %d)", id.c_str(),
      need_border ? 1 : 0);
  if (!visual_controller_) {
    return false;
  }
  auto canvas_item = visual_controller_->GetCanvasItem(id);
  if (!canvas_item) {
    return false;
  }
  canvas_item->SetNeedDrawBorder(need_border);
  return true;
}

bool VideoControllerImpl::CanvasItemChangeVisual(
    const std::string& canvas_item_id,
    const std::string& visual_id,
    std::unique_ptr<graphics::Transform> new_transform) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] CanvasItemChangeVisual canvas_item:"
            << canvas_item_id << ",new visual: " << visual_id
            << ",new transform: "
            << (!new_transform ? "null" : new_transform->ToString());

  const auto visual = visual_controller_->GetVisual(visual_id);
  if (!visual) {
    LOG(ERROR) << "Cannot find visual: " << visual_id;
    return false;
  }

  const auto canvas_item = visual_controller_->GetCanvasItem(canvas_item_id);
  const auto visual_canvas_item =
      std::dynamic_pointer_cast<VisualCanvasItem>(canvas_item);
  if (!visual_canvas_item) {
    LOG(ERROR) << "Cannot change canvas item to visual canvas item. Id="
               << canvas_item_id;
    return false;
  }

  visual_canvas_item->ChangeVisual(visual);
  if (new_transform) {
    visual_canvas_item->SetTransform(*new_transform);
  }
  return true;
}

inline void ConvertToMSTransform(const graphics::Transform& transform,
                                 MSTransform& convert) {
  convert.angle = transform.GetRotate();
  convert.clip = {transform.GetClip().x, transform.GetClip().y,
                  transform.GetClip().z, transform.GetClip().w};
  convert.flip_h = transform.IsFlipH();
  convert.flip_v = transform.IsFlipV();
  convert.scale = {transform.GetScale().x, transform.GetScale().y};
  convert.translate = {transform.GetTranslate().x, transform.GetTranslate().y};
}

ResultBoolMSTransform VideoControllerImpl::GetCanvasItemTransform(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  MSTransform ret = {};
  if (visual_controller_) {
    auto item = visual_controller_->GetCanvasItem(id);
    if (item) {
      ConvertToMSTransform(item->GetTransform(), ret);
      return {true, ret};
    }
  }
  return {false, ret};
}

bool VideoControllerImpl::SetPreviewWindowVisible(uint32_t sink_id,
                                                  bool is_visible) {
  LOG(INFO) << "SetPreviewWindowVisible[" << sink_id
            << (is_visible ? "]show" : "]hide");
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  if (!model_manager_) {
    return false;
  }
  return model_manager_->SetPreviewWindowVisible(sink_id, is_visible);
}

void VideoControllerImpl::AddModelOutputObserver(
    uint32_t sink_id,
    std::shared_ptr<VideoModelOutputObserver> observer) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (model_manager_) {
    auto model = model_manager_->GetModel(sink_id);
    if (model) {
      model->AddOutputObserver(observer);
    }
  }
}

void VideoControllerImpl::RemoveModelOutputObserver(
    uint32_t sink_id,
    std::shared_ptr<VideoModelOutputObserver> observer) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (model_manager_) {
    auto model = model_manager_->GetModel(sink_id);
    if (model) {
      model->RemoveOutputObserver(observer);
    }
  }
}

std::shared_ptr<ModelParams> VideoControllerImpl::GetModelParams(
    uint32_t sink_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (model_manager_) {
    auto model = model_manager_->GetModel(sink_id);
    if (model) {
      return std::make_shared<ModelParams>(model->GetParams());
    }
  }
  return nullptr;
}

std::shared_ptr<MSRect> VideoControllerImpl::GetModelWindowPos(
    uint32_t sink_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (model_manager_) {
    auto model = model_manager_->GetModel(sink_id);
    if (model) {
      auto pre_view = model->GetPreviewWindow();
      if (pre_view) {
        return std::make_shared<MSRect>(pre_view->GetClientRect());
      }
    }
  }
  return nullptr;
}

void VideoControllerImpl::GetVisualInputProperty(const std::string& id,
                                                 const std::string& key,
                                                 MSCallbackString callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (!visual_controller_) {
    std::move(callback).Resolve("");
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return;
  }
  auto visual_manager = visual_controller_->GetVisualManager();
  if (!visual_manager) {
    std::move(callback).Resolve("");
    LOG(ERROR) << "[VideoControllerImpl] has no visual_manager";
    return;
  }
  visual_manager->GetVisualInputProperty(id, key, std::move(callback));
}

void VideoControllerImpl::DoVisualInputAction(const std::string& id,
                                              const std::string& json,
                                              MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] DoVisualInputAction(%s, %s)", id.c_str(),
      json.c_str());

  if (!visual_controller_) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return;
  }
  auto visual_manager = visual_controller_->GetVisualManager();
  if (!visual_manager) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_manager";
    return;
  }
  visual_manager->DoVisualInputAction(id, json, std::move(callback));
}

void VideoControllerImpl::SetVisualInputProperty(const std::string& id,
                                                 const std::string& key,
                                                 const std::string& json,
                                                 MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetVisualInputProperty(%s) \n\tkey: %s, "
      "\n\tvalue:%s",
      id.c_str(), key.c_str(), json.c_str());

  if (!visual_controller_) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return;
  }
  auto visual_manager = visual_controller_->GetVisualManager();
  if (!visual_manager) {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] has no visual_manager";
    return;
  }
  visual_manager->SetVisualInputProperty(id, key, json, std::move(callback));
}

void VideoControllerImpl::CreateVisualFilter(const std::string& id,
                                             const std::string& filter_name,
                                             const std::string& canvas_item_id,
                                             const std::string& json_params,
                                             MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] CreateVisualFilter(%s, %s)", id.c_str(),
      canvas_item_id.c_str());

  CanvasItemPtr item;
  if (visual_controller_) {
    auto canvas_manager = visual_controller_->GetCanvasManager();
    if (canvas_manager) {
      item = canvas_manager->GetCanvasItem(canvas_item_id);
    }
  }

  if (item) {
    item->GetFilterManagerRef().CreateFilter(id, filter_name, json_params,
                                             std::move(callback));
    return;
  }
  LOG(ERROR) << "Canvas item not found, id: " << canvas_item_id;
  std::move(callback).Resolve(false);
}

void VideoControllerImpl::DestroyVisualFilter(
    const std::string& filter_id,
    const std::string& owner_canvas_item_id,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] DestroyVisualFilter filter:" << filter_id
            << ", item:" << owner_canvas_item_id;
  CanvasItemPtr item;
  if (visual_controller_) {
    item = visual_controller_->GetCanvasItem(owner_canvas_item_id);
  }

  if (item) {
    std::move(callback).Resolve(
        item->GetFilterManagerRef().RemoveFilter(filter_id));
  } else {
    std::move(callback).Resolve(false);
    LOG(ERROR)
        << "[VideoControllerImpl] DestroyVisualFilter can not find canvas_item:"
        << owner_canvas_item_id;
  }
}

void VideoControllerImpl::SetVisualFilterProperty(
    const std::string& filter_id,
    const std::string& owner_canvas_item_id,
    const std::string& key,
    const std::string& json_params,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  // limit the frequency of log print
  static int64_t last_report_time_ms = 0;
  int64_t current_time_ms =
      (base::TimeTicks::Now() - base::TimeTicks()).InMilliseconds();
  if (current_time_ms - last_report_time_ms > 100) {
    LOG(INFO) << base::StringPrintf(
        "[VideoControllerImpl] SetVisualFilterProperty(%s), canvas_item:%s "
        "\n\tkey: %s, "
        "\n\tvalue: %s",
        filter_id.c_str(), owner_canvas_item_id.c_str(), key.c_str(),
        json_params.c_str());
    last_report_time_ms = current_time_ms;
  }

  CanvasItemPtr item;
  if (visual_controller_) {
    item = visual_controller_->GetCanvasItem(owner_canvas_item_id);
  }
  if (item) {
    item->GetFilterManagerRef().SetFilterProperty(filter_id, key, json_params,
                                                  std::move(callback));
  } else {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] canvas item:" << owner_canvas_item_id
               << " not found,  filter: " << filter_id;
  }
}

void VideoControllerImpl::GetVisualFilterProperty(
    const std::string& filter_id,
    const std::string& owner_canvas_item_id,
    const std::string& key,
    MSCallbackString callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  // Too frequent
  // LOG(INFO) << "[VideoControllerImpl] VisualFilterAction. filter_id:"
  //          << filter_id << "; owner_item_id:" << owner_canvas_item_id
  //          << "; key:" << key;

  CanvasItemPtr item;
  if (visual_controller_) {
    item = visual_controller_->GetCanvasItem(owner_canvas_item_id);
  }

  if (item) {
    item->GetFilterManagerRef().GetFilterProperty(filter_id, key,
                                                  std::move(callback));
  } else {
    std::move(callback).Resolve("");
    LOG(ERROR) << "[VideoControllerImpl] canvas item:" << owner_canvas_item_id
               << " not found,  filter: " << filter_id;
  }
}

void VideoControllerImpl::VisualFilterAction(
    const std::string& filter_id,
    const std::string& owner_canvas_item_id,
    const std::string& action,
    const std::string& param,
    MSCallbackString callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  // TOO frequent
  // LOG(INFO) << "[VideoControllerImpl] VisualFilterAction. filter_id:"
  //          << filter_id << "; owner_item_id:" << owner_canvas_item_id
  //          << "; action:" << action << "; param:" << param;

  CanvasItemPtr item;
  if (visual_controller_) {
    item = visual_controller_->GetCanvasItem(owner_canvas_item_id);
  }
  if (item) {
    item->GetFilterManagerRef().FilterAction(filter_id, action, param,
                                             std::move(callback));
  } else {
    std::move(callback).Resolve("");
    LOG(ERROR) << "[VideoControllerImpl] canvas item:" << owner_canvas_item_id
               << " not found,  filter: " << filter_id;
  }
}

void VideoControllerImpl::VisualFilterSetActive(
    const std::string& filter_id,
    const std::string& owner_canvas_item_id,
    bool active,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] VisualFilterSetActive, canvas_item: "
            << owner_canvas_item_id << ", filter: " << filter_id
            << ",active:" << (active ? "true" : "false");

  CanvasItemPtr item;
  if (visual_controller_) {
    item = visual_controller_->GetCanvasItem(owner_canvas_item_id);
  }

  if (item) {
    std::move(callback).Resolve(
        item->GetFilterManagerRef().SetFilterActive(filter_id, active));
  } else {
    std::move(callback).Resolve(false);
    LOG(ERROR) << "[VideoControllerImpl] canvas item:" << owner_canvas_item_id
               << " not found,  filter: " << filter_id;
  }
}

void VideoControllerImpl::VisualFilterGetActive(
    const std::string& filter_id,
    const std::string& owner_canvas_item_id,
    MSCallback<ResultBoolBool> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] VisualFilterGetActive, canvas item:"
            << owner_canvas_item_id << ", filter: " << filter_id;

  CanvasItemPtr item;
  if (visual_controller_) {
    item = visual_controller_->GetCanvasItem(owner_canvas_item_id);
  }

  if (item) {
    std::move(callback).Resolve(
        item->GetFilterManagerRef().GetFilterActive(filter_id));
  } else {
    std::move(callback).Resolve({false, false});
    LOG(ERROR) << "[VideoControllerImpl] canvas item:" << owner_canvas_item_id
               << " not found,  filter: " << filter_id;
  }
}

bool VideoControllerImpl::SetVisualFiltersPriority(
    const std::string& owner_canvas_item_id,
    const MediaSDKStringArray& filter_id_array) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] SetVisualFiltersPriority. owner_item_id:"
            << owner_canvas_item_id;

  CanvasItemPtr item;
  if (visual_controller_) {
    item = visual_controller_->GetCanvasItem(owner_canvas_item_id);
  }

  if (item) {
    std::vector<std::string> ids;
    for (size_t i = 0; i < filter_id_array.Size(); ++i) {
      auto filter_str = filter_id_array.At(i).ToString();
      LOG(INFO) << "[VideoControllerImpl] SetVisualFiltersPriority id="
                << filter_str;
      ids.push_back(filter_str);
    }
    return item->GetFilterManagerRef().SetFiltersPriority(ids);
  }

  LOG(ERROR) << "Canvas item not found, item id: " << owner_canvas_item_id;
  return false;
}

bool VideoControllerImpl::SetGPUThreadPriority(int32_t priority) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SetGPUThreadPriority(%d)", priority);

  if (!device_)
    return false;
  return device_->SetGPUThreadPriority(priority);
}

MSSize VideoControllerImpl::GetDisplayWindowSizeByCanvasItemId(
    const std::string& canvas_item_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (!visual_controller_ || !model_manager_) {
    return MSSize{0, 0};
  }

  const auto video_model_id =
      visual_controller_->GetVideoModelIdByCanvasItemId(canvas_item_id);
  if (!video_model_id) {
    return MSSize{0, 0};
  }

  const auto model = model_manager_->GetModel(*video_model_id);
  if (!model) {
    return MSSize{0, 0};
  }

  const auto preview = model->GetPreviewWindow();
  if (!preview) {
    return MSSize{0, 0};
  }

  return preview->GetSize();
}

void VideoControllerImpl::UpdateRenderFPS(uint32_t fps) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] UpdateRenderFPS(%d)",
                                  fps);

  if (render_pump_) {
    render_pump_->UpdateRenderFPS(fps);
  }
  auto data_center = com::GetDataCenter();
  if (data_center) {
    data_center->UpdateRenderFPS(fps);
  }
}

std::shared_ptr<VisualManager> VideoControllerImpl::GetVisualManager() {
  return visual_controller_ ? visual_controller_->GetVisualManager() : nullptr;
}

std::shared_ptr<VideoModelManager> VideoControllerImpl::GetVideoModelManager() {
  return model_manager_;
}

std::shared_ptr<VisualController> VideoControllerImpl::GetVisualController() {
  return visual_controller_;
}

bool VideoControllerImpl::StartVirtualCamera(
    const std::string& canvas_item_id,
    MSSize size,
    uint32_t bg_color,
    VirtualCameraObjectFitMode fit_mode) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] StartVirtualCamera(%s) size(%d x %d) "
      "bg_color(%d), fit_mode(%d)",
      canvas_item_id.c_str(), size.cx, size.cy, bg_color, fit_mode);

  if (visual_controller_) {
    return visual_controller_->StartVirtualCamera(canvas_item_id, size,
                                                  bg_color, fit_mode);
  }

  return false;
}

bool VideoControllerImpl::StopVirtualCamera() {
  LOG(INFO) << base::StringPrintf("[VideoControllerImpl] StopVirtualCamera");
  if (visual_controller_) {
    return visual_controller_->StopVirtualCamera();
  }

  return false;
}

bool VideoControllerImpl::SwitchVirtualCamera(const std::string& id) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] SwitchVirtualCamera(%s)", id.c_str());
  if (visual_controller_) {
    return visual_controller_->SwitchVirtualCamera(id);
  }

  return false;
}

bool VideoControllerImpl::UpdateVirtualCameraProperty(MSSize size,
                                                      uint32_t bg_color) {
  LOG(INFO) << base::StringPrintf(
      "[VideoControllerImpl] UpdateVirtualCameraProperty(%d x %d, %x)", size.cx,
      size.cy, bg_color);
  if (visual_controller_) {
    return visual_controller_->UpdateVirtualCameraProperty(size, bg_color);
  }

  return false;
}

bool VideoControllerImpl::SetVirtualCameraRotate(float rotate) {
  LOG(INFO) << "[VideoControllerImpl] SetVirtualCameraRotate: " << rotate;
  if (visual_controller_) {
    return visual_controller_->SetVirtualCameraRotate(rotate);
  }

  return false;
}

bool VideoControllerImpl::SetVirtualCameraFlipV(bool flip_v) {
  LOG(INFO) << "[VideoControllerImpl] SetVirtualCameraFlipV: " << flip_v;
  if (visual_controller_) {
    return visual_controller_->SetVirtualCameraFlipV(flip_v);
  }

  return false;
}

bool VideoControllerImpl::SetVirtualCameraFlipH(bool flip_h) {
  LOG(INFO) << "[VideoControllerImpl] SetVirtualCameraFlipH: " << flip_h;
  if (visual_controller_) {
    return visual_controller_->SetVirtualCameraFlipH(flip_h);
  }

  return false;
}

void VideoControllerImpl::MockRenderHung(int32_t ms) {
  SleepFromNow(base::Milliseconds(ms));
}

bool VideoControllerImpl::SetCanvasItemPreprocess(const std::string& id,
                                                  bool enable) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] SetCanvasItemPreprocess canvas item:"
            << id << " preprocess:" << enable;

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return false;
  }
  auto item = visual_controller_->GetCanvasItem(id);
  if (!item) {
    LOG(ERROR) << "[VideoControllerImpl] canvas item not found:" << id;
    return false;
  }
  item->EnablePreProcess(enable);
  return true;
}

bool VideoControllerImpl::SetCanvasItemPreprocessDefaultSize(
    const std::string& id,
    const MSSize& size) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO)
      << "[VideoControllerImpl] SetCanvasItemPreprocessDefaultSize canvas item:"
      << id << " size=" << size.cx << "x" << size.cy;

  auto item = visual_controller_->GetCanvasItem(id);
  if (!item) {
    LOG(ERROR) << "[VideoControllerImpl] canvas item not found:" << id;
    return false;
  }
  item->SetPreprocessDefaultSize(size);
  return true;
}

bool VideoControllerImpl::RemoveCanvasItemPreprocessDefaultSize(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] RemoveCanvasItemPreprocessDefaultSize "
               "canvas item:"
            << id;

  auto item = visual_controller_->GetCanvasItem(id);
  if (!item) {
    LOG(ERROR) << "[VideoControllerImpl] canvas item not found:" << id;
    return false;
  }
  item->RemovePreprocessDefaultSize();
  return true;
}

ResultBoolBool VideoControllerImpl::GetCanvasItemPreprocess(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] GetCanvasItemPreprocess visual:" << id;

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return {false, false};
  }
  auto item = visual_controller_->GetCanvasItem(id);
  if (!item) {
    LOG(ERROR) << "[VideoControllerImpl] canvas item not found:" << id;
    return {false, false};
  }
  return {true, item->IsPreProcessEnabled()};
}

bool VideoControllerImpl::SetCanvasItemSourceClip(const std::string& item_id,
                                                  const MSClipF& clip) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] SetCanvasItemSourceClip canvas item:"
            << item_id << ", prepare transform clip(x:" << clip.x
            << ",y:" << clip.y << ",z:" << clip.z << ",w:" << clip.w << ")";

  if (!visual_controller_) {
    LOG(ERROR) << "[VideoControllerImpl] has no visual_controller";
    return false;
  }
  auto item = visual_controller_->GetCanvasItem(item_id);
  if (!item) {
    LOG(ERROR) << "[VideoControllerImpl] canvas item not found:" << item_id;
    return false;
  }
  return item->SetSourceClip(clip);
}

void VideoControllerImpl::ForwardWindowMessage(int32_t sink_id,
                                               int32_t msg,
                                               uint64_t wparam,
                                               uint64_t lparam) {
  if (model_manager_) {
    model_manager_->ForwardWindowMessage(sink_id, msg, wparam, lparam);
  }
}

void VideoControllerImpl::GetVisualFrame(
    const std::string& id,
    int target_width,
    int target_height,
    mediasdk::MSClip clip,
    MSCallback<ResultBoolMSVisualFrame> callback) {
  if (visual_controller_) {
    auto visual = visual_controller_->GetVisual(id);
    if (visual) {
      visual->GetVisualFrame(GetDevice(), target_width, target_height, clip,
                             std::move(callback));
      return;
    }
  }
  ResultBoolMSVisualFrame ret = {};
  ret.success = false;
  std::move(callback).Resolve(ret);
}

bool VideoControllerImpl::SetVisualDestroyedWhenAllReferenceRemoved(
    const std::string& visual_id,
    bool destroyed) {
  LOG(INFO) << "[VisualController] SetVisualDestroyedWhenAllReferenceRemoved "
               "visual_id:"
            << visual_id << ", destroyed:" << destroyed;
  if (visual_controller_) {
    auto visual = visual_controller_->GetVisual(visual_id);
    if (visual) {
      visual->SetAutoDestroyWhenNoCiter(destroyed);
      return true;
    }
  }
  return false;
}

std::shared_ptr<MediaSDKStringArray>
VideoControllerImpl::GetCanvasItemIDSFromVisual(const std::string& visual_id) {
  if (visual_controller_) {
    if (auto visual = visual_controller_->GetVisual(visual_id)) {
      std::vector<MediaSDKString> result =
          visual->GetVisualAllRefs(VisualCiterType::kCanvasItem);
      return std::make_shared<MediaSDKStringArray>(result);
    }
  }

  return nullptr;
}

bool VideoControllerImpl::CreateCanvas(const std::string& canvas_id,
                                       uint32_t video_model_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[Canvas] Create canvas:" << canvas_id << " on model "
            << video_model_id;
  auto success = false;
  if (visual_controller_) {
    success = visual_controller_->AddCanvas(video_model_id, canvas_id);
  }

  return success;
}

bool VideoControllerImpl::DestroyCanvas(const std::string& canvas_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[Canvas] Destroy canvas:" << canvas_id;
  auto success = false;
  if (visual_controller_) {
    success = visual_controller_->DestroyCanvas(canvas_id);
  }

  return success;
}

MediaSDKString VideoControllerImpl::GetCurrentCanvas(uint32_t video_model_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  if (visual_controller_) {
    auto canvas_mgr = visual_controller_->GetCanvasManager();
    if (canvas_mgr) {
      auto canvas = canvas_mgr->GetCurrentCanvas(video_model_id);
      if (canvas) {
        return canvas->GetCanvasId();
      }
    }
  }

  return "";
}

bool VideoControllerImpl::SetCurrentCanvas(uint32_t video_model_id,
                                           const std::string& canvas_id,
                                           const std::string& transition_id) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  LOG(INFO) << "[VideoControllerImpl] SetCurrentCanvas:" << canvas_id
            << " on model " << video_model_id << " and using transition "
            << transition_id;

  if (visual_controller_) {
    return visual_controller_->SetCurrentCanvas(video_model_id, canvas_id,
                                                transition_id);
  }

  return false;
}

bool VideoControllerImpl::CreateCanvasItem(
    const std::string& canvas_item_id,
    const std::string& canvas_id,
    const std::string& visual_id,
    const CreateCanvasItemParams& params) {
  LOG(INFO) << "[Canvas] CreateCanvasItem item_id: " << canvas_item_id
            << "; canvas_id: " << canvas_id << "; visual_id: " << visual_id
            << "; visible: " << params.is_visible;

  if (!visual_controller_) {
    return false;
  }

  return visual_controller_->AddCanvasItem(canvas_item_id, canvas_id, visual_id,
                                           params);
}

bool VideoControllerImpl::DestroyCanvasItem(const std::string& canvas_item_id) {
  if (canvas_item_id.empty()) {
    return false;
  }

  LOG(INFO) << "[Canvas] DestroyCanvasItem item_id: " << canvas_item_id;

  if (visual_controller_) {
    return visual_controller_->EraseCanvasItem(canvas_item_id);
  }

  return false;
}

MediaSDKString VideoControllerImpl::GetVisualFromCanvasItem(
    const std::string& id) {
  if (!visual_controller_) {
    return MediaSDKString{};
  }
  auto canvas_item = visual_controller_->GetCanvasItem(id);
  if (!canvas_item) {
    return MediaSDKString{};
  }

  auto visual_ptr = canvas_item->GetVisual();
  if (!visual_ptr) {
    return MediaSDKString{};
  }
  return MediaSDKString(visual_ptr->GetId());
}

void VideoControllerImpl::CreateTransition(
    const std::string& transition_id,
    const CreateTransitionParams& transition_params,
    MSCallbackBool callback) {
  LOG(INFO) << "[VideoControllerImpl] CreateTransition: transition_id="
            << transition_id
            << "; type=" << static_cast<int>(transition_params.transition_type)
            << "; property="
            << transition_params.transition_property.ToString();

  if (!visual_controller_) {
    LOG(ERROR) << "No visual_controller_";
    std::move(callback).Resolve(false);
    return;
  }

  const auto transition_mgr = visual_controller_->GetTransitionManager();
  if (!transition_mgr) {
    LOG(ERROR) << "Cannot find transition mgr";
    std::move(callback).Resolve(false);
    return;
  }

  transition_mgr->CreateTransition(transition_id, transition_params,
                                   std::move(callback));
}

bool VideoControllerImpl::DestroyTransition(const std::string& transition_id) {
  LOG(INFO) << "[VideoControllerImpl] DestroyTransition: transition_id="
            << transition_id;

  if (!visual_controller_) {
    LOG(ERROR) << "No visual_controller_";
    return false;
  }

  const auto transition_mgr = visual_controller_->GetTransitionManager();
  if (!transition_mgr) {
    LOG(ERROR) << "Cannot find transition mgr";
    return false;
  }

  return transition_mgr->RemoveTransition(transition_id);
}

void VideoControllerImpl::SetTransitionProperty(
    const std::string& transition_id,
    const std::string& property,
    MSCallbackBool callback) {
  LOG(INFO) << "[VideoControllerImpl] SetTransitionProperty: transition_id="
            << transition_id << "; property=" << property;

  if (!visual_controller_) {
    LOG(ERROR) << "No visual_controller_";
    std::move(callback).Resolve(false);
    return;
  }

  const auto transition_mgr = visual_controller_->GetTransitionManager();
  if (!transition_mgr) {
    LOG(ERROR) << "Cannot find transition mgr";
    std::move(callback).Resolve(false);
    return;
  }

  transition_mgr->SetTransitionProperty(transition_id, property,
                                        std::move(callback));
}
}  // namespace mediasdk