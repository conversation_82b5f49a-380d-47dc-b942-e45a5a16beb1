#pragma once

#include <cstdint>
#include <ostream>
#include <string>
#include <vector>
#include <utility>

namespace mediasdk {

struct EffectSDKConfig {
  bool enable_first_frame_statistics{true};
  bool enable_stuttering_status_statistics{false};
  bool enable_hide_origin_frame{false};
  bool expect_algo{false};
  bool enable_precompiled_shader{false};
  bool use_interface_with_tag{false};
  std::string lib_effect_ab_config;
};

struct EffectContextConfig {
  bool enable_down_grade{true};
  bool enable_angle_program_cache{false};
  bool enable_angle_log{false};
  uint8_t angle_program_cache_gles_version{0};
  std::vector<std::pair<int, int>> angle_ab_configs;
};

struct EffectConfig {
  EffectSDKConfig sdk_config;
  EffectContextConfig context_config;
};

}  // namespace mediasdk