#include "canvas_manager.h"

#include "mediasdk/mediasdk_call.h"
#include "mediasdk/public/mediasdk_api_canvas.h"

CanvasManager::CanvasManager(CanvasEventObserver* observer)
    : observer_(observer) {}

CanvasManager::~CanvasManager() {
  if (!canvas_map_parent_.empty()) {
    for (auto& [canvas_id, video_model_id] : canvas_and_items_map_) {
      MediaSDKCall(base::BindOnce([](bool* result) {}),
                   &mediasdk::DestroyCanvas, canvas_id.c_str());
    }
    canvas_map_parent_.clear();
  }

  canvas_and_items_map_.clear();
  item_map_visual_.clear();
  visual_map_items_.clear();
}

bool CanvasManager::CreateCanvas(const std::string& canvas_id,
                                 uint32_t parent_id,
                                 bool is_shown) {
  MediaSDKCall(base::BindOnce(&CanvasManager::OnCanvasCreated,
                              base::Unretained(this), canvas_id, is_shown),
               &mediasdk::CreateCanvas, canvas_id.c_str(), parent_id);
  canvas_map_parent_[canvas_id] = parent_id;

  return true;
}

void CanvasManager::DestroyCanvas(const std::string& canvas_id) {
  if (canvas_map_parent_.find(canvas_id) == canvas_map_parent_.end()) {
    return;
  }
  canvas_map_parent_.erase(canvas_id);

  // remove canvas items on the canvas
  const auto iter = canvas_and_items_map_.find(canvas_id);
  if (iter != canvas_and_items_map_.end()) {
    for (const auto& item : iter->second) {
      DestroyCanvasItem(item);
    }
    canvas_and_items_map_.erase(iter);
  }

  MediaSDKCall(base::BindOnce(&CanvasManager::OnCanvasDestroyed,
                              base::Unretained(this), canvas_id),
               &mediasdk::DestroyCanvas, canvas_id.c_str());
}

bool CanvasManager::ContainCanvas(const std::string& canvas_id) {
  return canvas_map_parent_.find(canvas_id) != canvas_map_parent_.end();
}

void CanvasManager::ShowCanvas(const std::string& canvas_id,
                               const std::string& transition_id) {
  MediaSDKCall(base::BindOnce([](bool* result) {}), &mediasdk::SetCurrentCanvas,
               canvas_map_parent_[canvas_id], canvas_id.c_str(),
               transition_id.c_str());
  current_canvas_ = canvas_id;
}

void CanvasManager::SetCurrentCanvas(const std::string& canvas_id) {
  current_canvas_ = canvas_id;
}

std::vector<std::string> CanvasManager::GetItems(const std::string& canvas_id) {
  auto it = canvas_and_items_map_.find(canvas_id);
  if (it != canvas_and_items_map_.end()) {
    return it->second;
  }
  return {};
}

void CanvasManager::CreateCanvasItem(
    const std::string& canvas_id,
    const std::string& canvas_item_id,
    const std::string& visual_id,
    const mediasdk::CreateCanvasItemParams& params,
    const VisualDeviceInfo& visual_info) {
  MediaSDKCall(
      base::BindOnce(&CanvasManager::OnCanvasItemCreated,
                     base::Unretained(this), canvas_item_id, visual_info),
      &mediasdk::CreateCanvasItem, canvas_item_id.c_str(), canvas_id.c_str(),
      visual_id.c_str(), params);
  canvas_and_items_map_[canvas_id].push_back(canvas_item_id);
  item_map_visual_[canvas_item_id] = visual_id;
  visual_map_items_[visual_id].push_back(canvas_item_id);
}

void CanvasManager::CreateCanvasItemWithFilter(
    const std::string& canvas_id,
    const std::string& canvas_item_id,
    const std::string& visual_id,
    const std::string& filter_id,
    const mediasdk::CreateCanvasItemParams& params,
    const mediasdk::CreateVisualFilterParams& filter_params,
    const VisualDeviceInfo& visual_info) {
  MediaSDKCall(
      base::BindOnce(&CanvasManager::OnCanvasItemCreated,
                     base::Unretained(this), canvas_item_id, visual_info),
      &mediasdk::CreateCanvasItemWithFilter, canvas_item_id.c_str(),
      canvas_id.c_str(), visual_id.c_str(), params, filter_id.c_str(),
      filter_params);
  canvas_and_items_map_[canvas_id].push_back(canvas_item_id);
  item_map_visual_[canvas_item_id] = visual_id;
  visual_map_items_[visual_id].push_back(canvas_item_id);
}

void CanvasManager::DestroyCanvasItem(const std::string& canvas_item_id) {
  MediaSDKCall(base::BindOnce(&CanvasManager::OnCanvasItemDestroyed,
                              base::Unretained(this), canvas_item_id),
               mediasdk::DestroyCanvasItem, canvas_item_id.c_str());
}

void CanvasManager::BindVisualToCanvasItem(const std::string& visual_id,
                                           const std::string& canvas_item_id) {
  // TODO(LY): No implemented
}

void CanvasManager::OnCanvasCreated(const std::string& canvas_id,
                                    bool is_shown,
                                    bool* result) {
  if (observer_ && result && *result) {
    observer_->OnCanvasCreated(canvas_id, is_shown);
  }
}

void CanvasManager::OnCanvasDestroyed(const std::string& canvas_id,
                                      bool* result) {
  if (observer_ && result && *result) {
    observer_->OnCanvasDestroyed(canvas_id);
  }
}

void CanvasManager::OnCanvasItemCreated(const std::string& canvas_item_id,
                                        const VisualDeviceInfo& visual_info,
                                        bool* result) {
  if (observer_ && result && *result) {
    observer_->OnCanvasItemCreated(canvas_item_id, visual_info);
  }
}

void CanvasManager::OnCanvasItemDestroyed(const std::string& canvas_item_id,
                                          bool* result) {
  if (observer_ && result && *result) {
    observer_->OnCanvasItemDestroyed(canvas_item_id);
  }
}
