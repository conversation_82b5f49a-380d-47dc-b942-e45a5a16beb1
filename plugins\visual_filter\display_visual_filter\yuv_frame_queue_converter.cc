#include "yuv_frame_queue_converter.h"

#include <stdexcept>

namespace mediasdk {

bool YuvFrameQueueConverter::Prepare(const graphics::TextureFrame& frame) {
  if (need_reset_convert_) {
    convert_.reset();
    need_reset_convert_ = false;
  }

  if (!convert_) {
    convert_ = graphics::CreateYUVToBGRAGraphics(device_);
  }

  if (convert_ && convert_->ConvertMemoryToBGRAPrepare(frame)) {
    return true;
  }

  return false;
}

bool YuvFrameQueueConverter::Convert() {
  if (need_reset_convert_ || !convert_) {
    return false;
  }

  return convert_->ConvertMemoryToBGRADraw();
}

void YuvFrameQueueConverter::ResetOnNextPrepare() {
  need_reset_convert_ = true;
}

graphics::Texture* YuvFrameQueueConverter::GetTexture() {
  if (!convert_) {
    return nullptr;
  }

  return convert_->GetOutputTexture().get();
}

}  // namespace mediasdk