#include "effect_sdk_loader.h"

#include <mutex>
#include <string>

#include "base/logging.h"
#include "base/path_service.h"

#include "egl_loader_autogen.h"
#include "gles_loader_autogen.h"
#include "system_utils.h"

namespace {

#define LOAD_EFFECT_FUNCTION(func)                                          \
  do {                                                                      \
    mediasdk::bef_api::ms_##func = reinterpret_cast<decltype(::func)*>(     \
        base::GetFunctionPointerFromNativeLibrary(effect_library_, #func)); \
    if (!mediasdk::bef_api::ms_##func) {                                    \
      LOG(ERROR) << "Failed to get function: " << #func;                    \
      return FALSE;                                                         \
    }                                                                       \
  } while (0);

angle::GenericProc KHRONOS_APIENTRY GetEGLProcAddress(const char* symbol) {
  return reinterpret_cast<angle::GenericProc>(
      mediasdk::ep::EffectSdkLoader::GetInstance()->GetEGLProc(symbol));
}

angle::GenericProc KHRONOS_APIENTRY GetGLESProcAddress(const char* symbol) {
  return reinterpret_cast<angle::GenericProc>(
      mediasdk::ep::EffectSdkLoader::GetInstance()->GetGLESV2Proc(symbol));
}

}  // namespace

namespace mediasdk::bef_api {
DEFINE_EFFECT_FUNCTION(bef_effect_load_egl_library_with_func)
DEFINE_EFFECT_FUNCTION(bef_effect_load_glesv2_library_with_func)
DEFINE_EFFECT_FUNCTION(bef_effect_get_sdk_version)
DEFINE_EFFECT_FUNCTION(bef_effect_get_sdk_commit)
DEFINE_EFFECT_FUNCTION(bef_effect_create_handle)
DEFINE_EFFECT_FUNCTION(bef_effect_use_pipeline_processor)
DEFINE_EFFECT_FUNCTION(bef_effect_set_render_api)
DEFINE_EFFECT_FUNCTION(bef_effect_set_log_to_local_func)
DEFINE_EFFECT_FUNCTION(bef_effect_destroy)
DEFINE_EFFECT_FUNCTION(bef_effect_set_color_filter_v2)
DEFINE_EFFECT_FUNCTION(bef_effect_set_effect)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_set_nodes)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_set_nodes_with_tags)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_replace_nodes)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_replace_nodes_with_tags)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_update_node)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_append_nodes)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_append_nodes_with_tags)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_remove_nodes)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_check_node_exclusion)
DEFINE_EFFECT_FUNCTION(bef_effect_init_with_resource_finder_v2)
DEFINE_EFFECT_FUNCTION(bef_effect_init)
DEFINE_EFFECT_FUNCTION(bef_effect_composer_set_mode)
DEFINE_EFFECT_FUNCTION(bef_effect_set_camera_device_position)
DEFINE_EFFECT_FUNCTION(bef_effect_set_width_height)
DEFINE_EFFECT_FUNCTION(bef_effect_algorithm_texture)
DEFINE_EFFECT_FUNCTION(bef_effect_process_texture)
DEFINE_EFFECT_FUNCTION(bef_effect_get_requirment)
DEFINE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_init)
DEFINE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_add)
DEFINE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_remove)
DEFINE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_destroy)
DEFINE_EFFECT_FUNCTION(bef_effect_send_msg)
DEFINE_EFFECT_FUNCTION(bef_effect_set_orientation)
DEFINE_EFFECT_FUNCTION(bef_effect_set_render_cache_texture)
DEFINE_EFFECT_FUNCTION(bef_effect_peek_resources_needed_by_requirements)
DEFINE_EFFECT_FUNCTION(bef_effect_config_ab_value)
DEFINE_EFFECT_FUNCTION(bef_effect_set_render_cache_string_value)
DEFINE_EFFECT_FUNCTION(bef_effect_use_pipeline_3_buffer)
DEFINE_EFFECT_FUNCTION(bef_effect_enable_algorithm_syncer)
DEFINE_EFFECT_FUNCTION(bef_effect_algorithm_multi_texture)
DEFINE_EFFECT_FUNCTION(bef_effect_algorithm_multi_texture_with_params)
DEFINE_EFFECT_FUNCTION(bef_effect_get_et_data)
DEFINE_EFFECT_FUNCTION(bef_effect_free_raw_buffer);
}  // namespace mediasdk::bef_api

namespace mediasdk::ep {
namespace {
std::once_flag init_flag;
EffectSdkLoader* inst = nullptr;
}  // namespace

// static
EffectSdkLoader* EffectSdkLoader::GetInstance() {
  std::call_once(init_flag, [&]() { inst = new EffectSdkLoader(); });
  return inst;
}

bool EffectSdkLoader::Load() {
  base::FilePath base_file;
  if (!base::PathService::Get(base::FILE_MODULE, &base_file)) {
    LOG(ERROR) << "[EffectPlatform] Failed to get program path for effect";
    return false;
  }
  base::FilePath base_dir = base_file.DirName();
  base::FilePath effect_dir = base_dir.Append(FILE_PATH_LITERAL("libeffect"));

  if (!effect_load_success_) {
    LoadEffect(effect_dir.Append(FILE_PATH_LITERAL("effect.dll")));
  }
  if (!egl_load_success_) {
    LoadEGL(effect_dir.Append(FILE_PATH_LITERAL("libEGL.dll")));
  }
  if (!glesv2_load_success_) {
    LoadGLESv2(effect_dir.Append(FILE_PATH_LITERAL("libGLESv2.dll")));
  }
  if (!IsLoaded()) {
    LOG(ERROR) << "[EffectPlatform] load effect lib failed,"
               << "effect:" << effect_load_success_
               << ", egl:" << egl_load_success_
               << ", glesv2:" << glesv2_load_success_;
    Clear();
    return false;
  }
  LOG(INFO) << "[EffectPlatform] load effect lib all success";
  return true;
}

void EffectSdkLoader::Clear() {
  if (glesv2_load_success_) {
    angle::CloseSharedLibrary(glesv2_library_);
    glesv2_library_ = nullptr;
    glesv2_load_success_ = false;
  }
  if (egl_load_success_) {
    angle::CloseSharedLibrary(egl_library_);
    egl_library_ = nullptr;
    egl_load_success_ = false;
  }
  if (effect_load_success_) {
    base::UnloadNativeLibrary(effect_library_);
    effect_library_ = nullptr;
    effect_load_success_ = false;
  }
  LOG(INFO) << "[EffectPlatform] clear effect lib";
}

bool EffectSdkLoader::IsLoaded() {
  return effect_load_success_ && egl_load_success_ && glesv2_load_success_;
}

void* EffectSdkLoader::GetEGLProc(const char* symbol) {
  DCHECK(egl_library_);
  DCHECK(symbol);
  if (!egl_library_ || !symbol) {
    return nullptr;
  }
  void* func = egl_library_->getSymbol(symbol);
  return func;
}

void* EffectSdkLoader::GetGLESV2Proc(const char* symbol) {
  DCHECK(glesv2_library_);
  DCHECK(symbol);
  if (!glesv2_library_ || !symbol) {
    return nullptr;
  }
  return glesv2_library_->getSymbol(symbol);
}

std::string EffectSdkLoader::GetBEFVersion() const {
  char version[MAX_PATH] = {0};
  mediasdk::bef_api::ms_bef_effect_get_sdk_version(version, MAX_PATH);
  if (version[MAX_PATH - 1] != 0) {
    version[MAX_PATH - 1] = 0;
  }
  return std::string(version);
}

std::string EffectSdkLoader::GetBEFCommit() const {
  char commit[MAX_PATH] = {0};
  mediasdk::bef_api::ms_bef_effect_get_sdk_commit(commit, MAX_PATH);
  if (commit[MAX_PATH - 1] != 0) {
    commit[MAX_PATH - 1] = 0;
  }
  return std::string(commit);
}

std::string EffectSdkLoader::GetBEFVersionFull() const {
  return GetBEFVersion() + "-" + GetBEFCommit();
}

bool EffectSdkLoader::LoadEffect(const base::FilePath& file_path) {
  effect_load_success_ = false;
  base::NativeLibraryLoadError load_error;
  effect_library_ = base::LoadNativeLibrary(file_path, &load_error);
  if (!effect_library_) {
    LOG(ERROR) << "[EffectPlatform] Failed to load library: "
               << load_error.ToString() << ", path:" << file_path;
    return false;
  }

  LOAD_EFFECT_FUNCTION(bef_effect_load_egl_library_with_func);
  LOAD_EFFECT_FUNCTION(bef_effect_load_glesv2_library_with_func);
  LOAD_EFFECT_FUNCTION(bef_effect_get_sdk_version);
  LOAD_EFFECT_FUNCTION(bef_effect_get_sdk_commit);
  LOAD_EFFECT_FUNCTION(bef_effect_create_handle);
  LOAD_EFFECT_FUNCTION(bef_effect_use_pipeline_processor);
  LOAD_EFFECT_FUNCTION(bef_effect_set_render_api);
  LOAD_EFFECT_FUNCTION(bef_effect_set_log_to_local_func);
  LOAD_EFFECT_FUNCTION(bef_effect_destroy);
  LOAD_EFFECT_FUNCTION(bef_effect_set_color_filter_v2);
  LOAD_EFFECT_FUNCTION(bef_effect_set_effect);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_set_nodes);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_set_nodes_with_tags);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_replace_nodes);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_replace_nodes_with_tags);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_update_node);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_append_nodes);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_append_nodes_with_tags);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_remove_nodes);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_check_node_exclusion);
  LOAD_EFFECT_FUNCTION(bef_effect_init_with_resource_finder_v2);
  LOAD_EFFECT_FUNCTION(bef_effect_init);
  LOAD_EFFECT_FUNCTION(bef_effect_composer_set_mode);
  LOAD_EFFECT_FUNCTION(bef_effect_set_camera_device_position);
  LOAD_EFFECT_FUNCTION(bef_effect_set_width_height);
  LOAD_EFFECT_FUNCTION(bef_effect_algorithm_texture);
  LOAD_EFFECT_FUNCTION(bef_effect_process_texture);
  LOAD_EFFECT_FUNCTION(bef_effect_get_requirment);
  LOAD_EFFECT_FUNCTION(bef_render_msg_delegate_manager_init);
  LOAD_EFFECT_FUNCTION(bef_render_msg_delegate_manager_add);
  LOAD_EFFECT_FUNCTION(bef_render_msg_delegate_manager_remove);
  LOAD_EFFECT_FUNCTION(bef_render_msg_delegate_manager_destroy);
  LOAD_EFFECT_FUNCTION(bef_effect_send_msg);
  LOAD_EFFECT_FUNCTION(bef_effect_set_orientation);
  LOAD_EFFECT_FUNCTION(bef_effect_set_render_cache_texture);
  LOAD_EFFECT_FUNCTION(bef_effect_peek_resources_needed_by_requirements);
  LOAD_EFFECT_FUNCTION(bef_effect_config_ab_value);
  LOAD_EFFECT_FUNCTION(bef_effect_set_render_cache_string_value);
  LOAD_EFFECT_FUNCTION(bef_effect_use_pipeline_3_buffer);
  LOAD_EFFECT_FUNCTION(bef_effect_enable_algorithm_syncer);
  LOAD_EFFECT_FUNCTION(bef_effect_algorithm_multi_texture);
  LOAD_EFFECT_FUNCTION(bef_effect_algorithm_multi_texture_with_params);
  LOAD_EFFECT_FUNCTION(bef_effect_get_et_data);
  LOAD_EFFECT_FUNCTION(bef_effect_free_raw_buffer);

  LOG(INFO) << "[EffectPlatform] Load effect library success";
  effect_load_success_ = true;
  return true;
}

bool EffectSdkLoader::LoadEGL(const base::FilePath& file_path) {
  egl_load_success_ = false;

#if BUILDFLAG(IS_WIN)
  std::string egl_path = file_path.AsUTF8Unsafe();
#else
  std::string egl_path = file_path.value();
#endif

  DCHECK(!egl_path.empty());
  DCHECK(mediasdk::bef_api::ms_bef_effect_load_egl_library_with_func);

  if (!mediasdk::bef_api::ms_bef_effect_load_egl_library_with_func) {
    LOG(ERROR) << "[EffectPlatform] Can not find effect load egl function "
                  "before load egl";
    return false;
  }

  if (!egl_library_ || !egl_library_->getNative()) {
    egl_library_ = angle::OpenSharedLibrary(egl_path.c_str(),
                                            angle::SearchType::ApplicationDir);
    if (!egl_library_ || !egl_library_->getNative()) {
      LOG(ERROR) << "[EffectPlatform] Open egl shared library failed, path:"
                 << egl_path;
      return false;
    }
  }
  angle::LoadEGL(GetEGLProcAddress);
  mediasdk::bef_api::ms_bef_effect_load_egl_library_with_func(
      GetEGLProcAddress);
  egl_load_success_ = true;
  return true;
}

bool EffectSdkLoader::LoadGLESv2(const base::FilePath& file_path) {
  glesv2_load_success_ = false;

#if BUILDFLAG(IS_WIN)
  std::string glesv2_path = file_path.AsUTF8Unsafe();
#else
  std::string glesv2_path = file_path.value();
#endif

  DCHECK(!glesv2_path.empty());
  DCHECK(mediasdk::bef_api::ms_bef_effect_load_glesv2_library_with_func);
  if (!mediasdk::bef_api::ms_bef_effect_load_glesv2_library_with_func) {
    LOG(ERROR) << "[EffectPlatform] Can not find effect load gles function "
                  "before load gles";
    return false;
  }

  if (!glesv2_library_ || glesv2_library_->getNative() == NULL) {
    glesv2_library_ = angle::OpenSharedLibrary(
        glesv2_path.c_str(), angle::SearchType::ApplicationDir);
    if (!glesv2_library_ || !glesv2_library_->getNative()) {
      LOG(ERROR) << "[EffectPlatform] Open glesv2 shared library failed, path:"
                 << glesv2_path;
      return false;
    }
  }

  angle::LoadGLES(GetGLESProcAddress);
  mediasdk::bef_api::ms_bef_effect_load_glesv2_library_with_func(
      GetGLESProcAddress);
  glesv2_load_success_ = true;
  return true;
}

}  // namespace mediasdk::ep
