#include "shape_visual_filter.h"
#include "mediasdk/public/plugin/plugin_defines.h"
#include "shape_visual_filter_helper.h"

#include <cmath>  // For std::abs
#include <nlohmann/json.hpp>
#include "../visual_filter_utils/texture_size_calculator.h"
#include "base/logging.h"

namespace {
constexpr char kShapeFilterKey[] = "shape_filter";
}

namespace mediasdk {
constexpr float kAngleEpsilon = 0.001f;
ShapeVisualFilter::ShapeVisualFilter() = default;

ShapeVisualFilter::~ShapeVisualFilter() = default;

bool ShapeVisualFilter::Create(const char* json_params) {
  if (json_params == nullptr) {
    return false;
  }
  LOG(INFO) << "[ShapeVisualFilter] Create json_params is:" << json_params;
  try {
    auto ini_json = nlohmann::json::parse(std::string(json_params));
    if (ini_json.contains("strech_starting_point")) {
      strech_starting_point_ = ini_json["strech_starting_point"];
      line_fade_start_time_ = std::chrono::steady_clock::now();
      line_fading_ = false;
    } else {
      LOG(ERROR) << "[ShapeVisualFilter] Missing required params.";
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "[ShapeVisualFilter] Failed to parse exception: " << e.what()
               << " json " << json_params;
    return false;
  }
  return true;
}

void ShapeVisualFilter::Destroy() {
  // TODO: Implement destruction logic, release any allocated resources
}

MediaSDKString ShapeVisualFilter::GetProperty(const char* key) {
  // TODO: Implement property retrieval
  // Example:
  // if (strcmp(key, "scale_factor") == 0) {
  //   nlohmann::json j;
  //   // j["scale_factor"] = current_scale_factor_;
  //   return MediaSDKString(j.dump().c_str());
  // }
  return MediaSDKString("");
}

bool ShapeVisualFilter::SetProperty(const char* key, const char* json) {
  if (json == nullptr || key == nullptr) {
    return false;
  }
  float val = 1.0;
  try {
    nlohmann::json properties = nlohmann::json::parse(std::string(json));
    if (properties.contains("strech_starting_point")) {
      line_fade_start_time_ = std::chrono::steady_clock::now();
      strech_starting_point_ = properties["strech_starting_point"];
      line_fading_ = true;
    }
  } catch (const std::invalid_argument& e) {
    LOG(ERROR) << "ShapeVisualFilter SetProperty: invalid arg json " << json;
    return false;
  }
  return true;
}

MediaSDKString ShapeVisualFilter::Action(const char* action,
                                         const char* json_params) {
  // TODO: Implement custom actions if any
  return MediaSDKString("");
}

void ShapeVisualFilter::InitGraphicsResource(VisualFilterProxy* proxy) {
  proxy_ = proxy;
  if (proxy_) {
    auto device = proxy_->GetDevice();
    if (device) {
      graphics_ = graphics::CreateGraphics2D(*device);
    }
  }
  draw_gradual_ = GetDrawGradualRectangle();
  // TODO: Initialize any graphics resources needed for scaling
}

void ShapeVisualFilter::ReleaseGraphicsResource() {
  // TODO: Release any graphics resources
  graphics_.reset();
  proxy_ = nullptr;
}

void ShapeVisualFilter::ClearVideoCache() {
  graphics_.reset();
}

bool ShapeVisualFilter::CreateTextureIfNecessary(
    graphics::Texture& texture,
    const VisualFilterTransform& texture_data) {
  if (!graphics_ ||
      TextureSizeCalc::IsTargetSizeChange(graphics_->GetSize(),
                                          texture.GetSize(), texture_data)) {
    graphics_.reset();
    graphics_ = graphics::CreateGraphics2D(texture.GetDevice());
    if (!graphics_) {
      LOG(ERROR) << "[ShapeVisualFilter] Failed to create Graphics2D.";
      return false;
    }
    auto size =
        TextureSizeCalc::GetTargetOutputSize(texture.GetSize(), texture_data);
    if (!graphics_->CreateNewBGRAGraphics(size.x, size.y)) {
      LOG(ERROR) << "[ShapeVisualFilter] Failed to create new BGRA graphics "
                    "with size: "
                 << size.x << "x" << size.y;
      graphics_.reset();
    }
  }
  return (graphics_ != nullptr);
}

void ShapeVisualFilter::CalcShapeTrans(
    int stretch_direction,
    graphics::Rectangle::RectangleConfig& line_config,
    graphics::Rectangle::RectangleConfig& line_config_w,
    graphics::GradualRectangle::GradualRectangleConfig& rec_config,
    graphics::GradualRectangle::GradualRectangleConfig& rec_config_w) {
  // stretch_direction: 1 (Bottom to Top) -> Horizontal line, position from top
  // stretch_direction: 3 (Top to Bottom) -> Horizontal line, position from
  // bottom (symmetric to 1) stretch_direction: 2 (Left to Right) -> Vertical
  // line, position from right stretch_direction: 4 (Right to Left) -> Vertical
  // line, position from left (symmetric to 2)
  float line_position;
  float line_thickness = 2.0f;
  if (stretch_direction == 1) {  // Horizontal line, from top
    line_position = graphics_->GetSize().y * strech_starting_point_;
    line_config.top_left =
        DirectX::XMFLOAT2{0.0f, line_position - line_thickness / 2.0f};
    line_config.bottom_right = DirectX::XMFLOAT2{
        graphics_->GetSize().x, line_position + line_thickness / 2.0f};

    line_config_w.top_left =
        DirectX::XMFLOAT2{0.0f, graphics_->GetSize().y - line_thickness};
    line_config_w.bottom_right =
        DirectX::XMFLOAT2{graphics_->GetSize().x, graphics_->GetSize().y};

    float space = rec_config.vp_size.y - line_config.bottom_right.y;
    float rect_height = space / 3.0f;
    rec_config.top_left =
        DirectX::XMFLOAT2{0.0f, rec_config.vp_size.y - rect_height};
    rec_config.bottom_right =
        DirectX::XMFLOAT2{rec_config.vp_size.x, rec_config.vp_size.y};
    rec_config.direction = graphics::GradientDirection::TOP_TO_BOTTOM;

    rec_config_w.top_left =
        DirectX::XMFLOAT2{0.0f, line_position + line_thickness / 2.0f};
    rec_config_w.bottom_right =
        DirectX::XMFLOAT2{rec_config_w.vp_size.x, line_position + rect_height};
    rec_config_w.direction = graphics::GradientDirection::BOTTOM_TO_TOP;
  } else if (stretch_direction == 3) {  // Horizontal line, from bottom
    line_position = graphics_->GetSize().y * (1.0f - strech_starting_point_);
    line_config.top_left =
        DirectX::XMFLOAT2{0.0f, line_position - line_thickness / 2.0f};
    line_config.bottom_right = DirectX::XMFLOAT2{
        graphics_->GetSize().x, line_position + line_thickness / 2.0f};

    line_config_w.top_left = DirectX::XMFLOAT2{0.0f, 0.0f};
    line_config_w.bottom_right =
        DirectX::XMFLOAT2{graphics_->GetSize().x, line_thickness};

    // rotation = 180 degree
    float space = line_config.bottom_right.y;
    float rect_height = space / 3.0f;
    rec_config.top_left = DirectX::XMFLOAT2{0.0f, 0.0f};
    rec_config.bottom_right =
        DirectX::XMFLOAT2{rec_config.vp_size.x, rect_height};
    rec_config.direction = graphics::GradientDirection::BOTTOM_TO_TOP;

    // rotation = 180 degree
    rec_config_w.top_left =
        DirectX::XMFLOAT2{0.0f, line_position - rect_height};
    rec_config_w.bottom_right = DirectX::XMFLOAT2{
        rec_config_w.vp_size.x, line_position - line_thickness / 2.0f};
    rec_config_w.direction = graphics::GradientDirection::TOP_TO_BOTTOM;
  } else if (stretch_direction == 2) {  // Vertical line, from right
    line_position = graphics_->GetSize().x * strech_starting_point_;
    line_config.top_left =
        DirectX::XMFLOAT2{line_position - line_thickness / 2.0f, 0.0f};
    line_config.bottom_right = DirectX::XMFLOAT2{
        line_position + line_thickness / 2.0f, graphics_->GetSize().y};

    line_config_w.top_left =
        DirectX::XMFLOAT2{graphics_->GetSize().x - line_thickness, 0.0f};
    line_config_w.bottom_right =
        DirectX::XMFLOAT2{graphics_->GetSize().x, graphics_->GetSize().y};

    // rotation = 90 degree
    float space = rec_config.vp_size.x - line_config.top_left.x;
    float rect_width = space / 3.0f;
    rec_config.top_left =
        DirectX::XMFLOAT2{rec_config.vp_size.x - rect_width, 0.0f};
    rec_config.bottom_right =
        DirectX::XMFLOAT2{rec_config.vp_size.x, rec_config.vp_size.y};
    rec_config.direction = graphics::GradientDirection::RIGHT_TO_LEFT;

    // rotation = 90 degree
    rec_config_w.top_left =
        DirectX::XMFLOAT2{line_position + line_thickness / 2.0f, 0.0f};
    rec_config_w.bottom_right =
        DirectX::XMFLOAT2{line_position + rect_width, rec_config_w.vp_size.y};
    rec_config_w.direction = graphics::GradientDirection::LEFT_TO_RIGHT;
  } else if (stretch_direction == 4) {  // Vertical line, from left
    line_position = graphics_->GetSize().x * (1.0f - strech_starting_point_);
    line_config.top_left =
        DirectX::XMFLOAT2{line_position - line_thickness / 2.0f, 0.0f};
    line_config.bottom_right = DirectX::XMFLOAT2{
        line_position + line_thickness / 2.0f, graphics_->GetSize().y};

    line_config_w.top_left = DirectX::XMFLOAT2{0.0f, 0.0f};
    line_config_w.bottom_right =
        DirectX::XMFLOAT2{line_thickness, graphics_->GetSize().y};

    // rotation = 270 degree
    float space = line_config.top_left.x;
    float rect_width = space / 3.0f;
    rec_config.top_left = DirectX::XMFLOAT2{0.0f, 0.0f};
    rec_config.bottom_right =
        DirectX::XMFLOAT2{rect_width, rec_config.vp_size.y};
    rec_config.direction = graphics::GradientDirection::LEFT_TO_RIGHT;

    // rotation = 270 degree
    rec_config_w.top_left = DirectX::XMFLOAT2{line_position - rect_width, 0.0f};
    rec_config_w.bottom_right = DirectX::XMFLOAT2{
        line_position - line_thickness / 2.0f, rec_config_w.vp_size.y};
    rec_config_w.direction = graphics::GradientDirection::RIGHT_TO_LEFT;
  } else {  // Default to horizontal line from top (case 1)
    line_position = graphics_->GetSize().y * strech_starting_point_;
    line_config.top_left =
        DirectX::XMFLOAT2{0.0f, line_position - line_thickness / 2.0f};
    line_config.bottom_right = DirectX::XMFLOAT2{
        graphics_->GetSize().x, line_position + line_thickness / 2.0f};

    line_config_w.top_left =
        DirectX::XMFLOAT2{0.0f, graphics_->GetSize().y - line_thickness};
    line_config_w.bottom_right =
        DirectX::XMFLOAT2{graphics_->GetSize().x, graphics_->GetSize().y};

    float space = rec_config.vp_size.y - line_config.bottom_right.y;
    float rect_height = space / 3.0f;
    rec_config.top_left =
        DirectX::XMFLOAT2{0.0f, rec_config.vp_size.y - rect_height};
    rec_config.bottom_right =
        DirectX::XMFLOAT2{rec_config.vp_size.x, rec_config.vp_size.y};
    rec_config.direction = graphics::GradientDirection::TOP_TO_BOTTOM;

    rec_config_w.top_left = DirectX::XMFLOAT2{0.0f, line_position};
    rec_config_w.bottom_right =
        DirectX::XMFLOAT2{rec_config_w.vp_size.x, line_position + rect_height};
    rec_config_w.direction = graphics::GradientDirection::BOTTOM_TO_TOP;
  }
}

int ShapeVisualFilter::GetStretchingDirection(
    const VisualFilterTransform& texture_trans) {
  bool is_flipH = texture_trans.hflip;
  bool is_flipV = texture_trans.vflip;
  auto ori_angle = texture_trans.angle;
  int strech_direction;

  if (std::abs(ori_angle - 0.0f) < kAngleEpsilon) {
    strech_direction = 1;
    if (is_flipV) {
      strech_direction = 3;
    }
  } else if (std::abs(ori_angle - 90.0f) < kAngleEpsilon) {
    strech_direction = 2;
    if (is_flipH) {
      strech_direction = 4;
    }
  } else if (std::abs(ori_angle - 180.0f) < kAngleEpsilon) {
    strech_direction = 3;
    if (is_flipV) {
      strech_direction = 1;
    }
  } else if (std::abs(ori_angle - 270.0f) < kAngleEpsilon) {
    strech_direction = 4;
    if (is_flipH) {
      strech_direction = 2;
    }
  } else {
    strech_direction = 1;  // Default direction
  }
  return strech_direction;
}

bool ShapeVisualFilter::GetDrawGradualRectangle() {
  if (!proxy_) {
    LOG(ERROR) << "[ShapeVisualFilter] proxy null";
    return true;
  }

  bool enable_gradual = false;
  std::string ab_info = proxy_->GetABConfig(kShapeFilterKey).ToString();
  DCHECK(!ab_info.empty());

  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);
    enable_gradual = mediasdk_json.value("draw_gradual", false);
  } catch (const std::exception& e) {
    LOG(INFO) << "[ShapeVisualFilter] found draw_gradual failed:" << e.what()
              << ", use default value";
  }
  LOG(INFO) << "[ShapeVisualFilter] draw gradual rectangle:" << enable_gradual;
  return enable_gradual;
}

graphics::Texture* ShapeVisualFilter::Process(
    graphics::Texture* texture,
    const VisualFilterTransform& texture_trans,
    int64_t timestamp) {
  if (!texture || !proxy_) {
    LOG(WARNING) << "[ShapeVisualFilter] Input texture or proxy is null.";
    return nullptr;
  }

  if (!CreateTextureIfNecessary(*texture, texture_trans)) {
    LOG(ERROR) << "[ShapeVisualFilter] Failed to create or update texture.";
    return texture;
  }

  if (!graphics_) {
    LOG(ERROR) << "[ShapeVisualFilter] Graphics context is not initialized.";
    return texture;
  }

  graphics::Transform trans;
  float width_scale = graphics_->GetSize().x / texture->GetSize().x;
  float height_scale = graphics_->GetSize().y / texture->GetSize().y;
  trans.SetScale({width_scale, height_scale});
  graphics_->BeginDraw(true);
  graphics_->DrawBGRATexture(*texture, trans);
  graphics_->EndDraw();
  graphics::Rectangle::RectangleConfig line_config;
  graphics_->BeginDraw(false);
  if (line_fading_) {
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                            current_time - line_fade_start_time_)
                            .count();
    float fade_duration_ms = 2300.0f;  // 2.3 seconds
    if (elapsed_time < fade_duration_ms) {
      float alpha =
          elapsed_time < 2000 ? 1.0f : 1.0f - ((elapsed_time - 2000) / 300.0f);
      // canvas_size is already defined in this function scope from GetSize()
      // device_ is a member of DisplayWindow, passed in constructor
      auto line_primitive = graphics::CreateRectangle(graphics_->GetDevice());
      auto stretch_direction = GetStretchingDirection(texture_trans);

      line_config.vp_size = graphics_->GetSize();
      line_config.color =
          DirectX::XMFLOAT4{0.0f, 0.729f, 0.815f,
                            alpha};  // Red color (R, G, B, A) with fading alpha
      line_config.fill = true;       // Fill the rectangle to draw a solid line

      auto line_primitive_w = graphics::CreateRectangle(graphics_->GetDevice());
      graphics::Rectangle::RectangleConfig line_config_w;
      line_config_w.vp_size = graphics_->GetSize();
      line_config_w.color =
          DirectX::XMFLOAT4{0.0f, 0.729f, 0.815f,
                            alpha};  // Red color (R, G, B, A) with fading alpha
      line_config_w.fill = true;     // Fill the rectangle to draw a solid line

      auto rec_primitive =
          graphics::CreateGradualRectangle(graphics_->GetDevice());
      graphics::GradualRectangle::GradualRectangleConfig rec_config;
      rec_config.vp_size = graphics_->GetSize();
      rec_config.color = DirectX::XMFLOAT4{0.0f, 0.729f, 0.815f, alpha * 0.3f};
      rec_config.fill = true;

      auto rec_primitive_w =
          graphics::CreateGradualRectangle(graphics_->GetDevice());
      graphics::GradualRectangle::GradualRectangleConfig rec_config_w;
      rec_config_w.vp_size = graphics_->GetSize();
      rec_config_w.color =
          DirectX::XMFLOAT4{0.0f, 0.729f, 0.815f, alpha * 0.3f};
      rec_config_w.fill = true;

      CalcShapeTrans(stretch_direction, line_config, line_config_w, rec_config,
                     rec_config_w);
      line_primitive->UpdateRectangleConf(&line_config);
      graphics_->DrawRectangle(*line_primitive);

      line_primitive_w->UpdateRectangleConf(&line_config_w);
      if (draw_gradual_) {
        graphics_->DrawRectangle(*line_primitive_w);
      }

      rec_primitive->UpdateRectangleConf(&rec_config);
      if (draw_gradual_) {
        graphics_->DrawGradualRectangle(*rec_primitive);
      }

      rec_primitive_w->UpdateRectangleConf(&rec_config_w);
      if (draw_gradual_) {
        graphics_->DrawGradualRectangle(*rec_primitive_w);
      }

    } else {
      line_fading_ = false;  // Stop fading after 5 seconds
    }
  }

  graphics_->EndDraw();
  return graphics_->GetOutputTexture().get();
}

int64_t ShapeVisualFilter::GetProcessDelayNS() {
  // TODO: Return the processing delay if any, relevant for synchronization
  return 0;
}

//////////////////////////////////////////////////////////////////////////

const PluginInfo* GetPluginInfo() {
  static PluginInfo info;
  info.id = shape_visual_filter::GetPluginID();
  info.type = PluginType::kVisualFilter;
  info.name = shape_visual_filter::GetPluginName();
  info.desc = shape_visual_filter::GetPluginDesc();

  return &info;
}

VisualFilter* CreateVisualFilter(const char* json_params) {
  auto filter = new ShapeVisualFilter();
  if (filter->Create(json_params)) {
    return filter;
  }
  delete filter;
  return nullptr;
}

void DestroyVisualFilter(VisualFilter* filter) {
  auto shape_visual_filter = dynamic_cast<ShapeVisualFilter*>(filter);
  if (shape_visual_filter) {
    shape_visual_filter->Destroy();
    delete shape_visual_filter;
  }
}
}  // namespace mediasdk