#include "composer_task.h"

#include "base/check.h"
#include "base/logging.h"
#include "effect_sdk/effect_sdk.h"
#include "nlohmann/json.hpp"

namespace mediasdk {

namespace {
class AdderTask : public ComposerTask {
 public:
  explicit AdderTask(const std::vector<std::string>& composers,
                     const std::vector<std::string>& tags)
      : composers_(composers), tags_(tags) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.AddComposer(composers_, tags_);
  }

 private:
  const std::vector<std::string> composers_;
  const std::vector<std::string> tags_;
};

class SetTask : public ComposerTask {
 public:
  explicit SetTask(const std::vector<std::string>& composers,
                   const std::vector<std::string>& tags)
      : composers_(composers), tags_(tags) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.SetComposer(composers_, tags_);
  }

 private:
  const std::vector<std::string> composers_;
  const std::vector<std::string> tags_;
};

class UpdateTask : public ComposerTask {
 public:
  explicit UpdateTask(const std::string& path, std::string& key, float value, const std::string& tag)
      : path_(path), key_(key), value_(value), tag_(tag) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.UpdateComposer(path_, key_, value_, tag_);
  }

 private:
  const std::string path_;
  const std::string key_;
  const float value_ = 0.f;
  std::string tag_;
};

class RemoveTask : public ComposerTask {
 public:
  explicit RemoveTask(const std::vector<std::string>& composers)
      : composers_(composers) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.RemoveComposer(composers_);
  }

 private:
  const std::vector<std::string> composers_;
};

class SetTextTask : public ComposerTask {
 public:
  explicit SetTextTask(const std::string& key, const std::string& value)
      : key_(key), value_(value) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.SetTextComposer(key_, value_);
  }

 private:
  const std::string key_;
  const std::string value_;
};

class BackgroundTask : public ComposerTask {
 public:
  explicit BackgroundTask(const std::string& path, const std::string& key)
      : path_(path), key_(key) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.SetBackgroundComposer(key_, path_);
  }

 private:
  const std::string path_;
  const std::string key_;
};

class ReplaceTask : public ComposerTask {
 public:
  explicit ReplaceTask(const std::vector<std::string>& old_paths,
                       const std::vector<std::string>& new_paths,
                       const std::vector<std::string>& tags)
      : old_paths_(old_paths), new_paths_(new_paths), tags_(tags) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.ReplaceComposer(old_paths_, new_paths_, tags_);
  }

 private:
  const std::vector<std::string> old_paths_;
  const std::vector<std::string> new_paths_;
  const std::vector<std::string> tags_;
};

class MsgTask : public ComposerTask {
 public:
  MsgTask(unsigned int msg_id, long arg1, long arg2, const std::string arg3)
      : msg_id_(msg_id), arg1_(arg1), arg2_(arg2), arg3_(arg3) {}

  bool Exec(const EffectSDK& effect_sdk) const override {
    return effect_sdk.SendMsg(msg_id_, arg1_, arg2_, arg3_);
  }

 private:
  unsigned int msg_id_;
  long arg1_;
  long arg2_;
  std::string arg3_;
};
}  // namespace

////////////////////////////////////////////////////////////////////

// static
std::unique_ptr<ComposerTask> ComposerTask::CreateAddTask(
    const std::string& json_data) {
  DCHECK(!json_data.empty());
  // parse json
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto paths = json_task.at("paths").get<std::vector<std::string>>();
    auto tags = json_task.at("tags").get<std::vector<std::string>>();
    return std::unique_ptr<ComposerTask>(new AdderTask(paths, tags));

  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}

std::unique_ptr<ComposerTask> ComposerTask::CreateSetTask(
    const std::string& json_data) {
  DCHECK(!json_data.empty());
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto paths = json_task.at("paths").get<std::vector<std::string>>();
    auto tags = json_task.at("tags").get<std::vector<std::string>>();
    return std::unique_ptr<ComposerTask>(new SetTask(paths, tags));
  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}

std::unique_ptr<ComposerTask> ComposerTask::CreateUpdateTask(
    const std::string& json_data) {
  DCHECK(!json_data.empty());
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto path = json_task.at("path").get<std::string>();
    auto key = json_task.at("key").get<std::string>();
    auto value = json_task.at("value").get<float>();
    auto tag = json_task.at("tag").get<std::string>();
    return std::unique_ptr<ComposerTask>(new UpdateTask(path, key, value, tag));
  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}

std::unique_ptr<ComposerTask> ComposerTask::CreateRemoveTask(
    const std::string& json_data) {
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto composers = json_task.get<std::vector<std::string>>();
    return std::unique_ptr<ComposerTask>(new RemoveTask(composers));
  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}

std::unique_ptr<ComposerTask> ComposerTask::CreateTextTask(
    const std::string& json_data) {
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto key = json_task.at("key").get<std::string>();
    auto value = json_task.at("value").get<std::string>();
    return std::unique_ptr<ComposerTask>(new SetTextTask(key, value));
  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}

std::unique_ptr<ComposerTask> ComposerTask::CreateBackgroundTask(
    const std::string& json_data) {
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto path = json_task.at("path").get<std::string>();
    auto key = json_task.at("key").get<std::string>();
    return std::unique_ptr<ComposerTask>(new BackgroundTask(path, key));
  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}

std::unique_ptr<ComposerTask> ComposerTask::CreateReplaceTask(
    const std::string& json_data) {
  DCHECK(!json_data.empty());
  // parse json
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto oldPaths = json_task.at("oldPaths").get<std::vector<std::string>>();
    auto newPaths = json_task.at("newPaths").get<std::vector<std::string>>();
    auto tags = json_task.at("tags").get<std::vector<std::string>>();
    return std::unique_ptr<ComposerTask>(new ReplaceTask(oldPaths, newPaths, tags));

  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}

std::unique_ptr<ComposerTask> ComposerTask::CreateMessageTask(
    const std::string& json_data) {
  DCHECK(!json_data.empty());
  // parse json
  try {
    auto json_task = nlohmann::json::parse(json_data);
    auto msg_id = json_task.at("msgID").get<unsigned int>();
    auto arg1 = json_task.at("arg1").get<long>();
    auto arg2 = json_task.at("arg2").get<long>();
    auto arg3 = json_task.at("arg3").get<std::string>();
    return std::unique_ptr<ComposerTask>(new MsgTask(msg_id, arg1, arg2, arg3));

  } catch (const std::exception& e) {
    LOG(ERROR) << "Parse task json error:" << e.what()
               << ", data:" << json_data;
  }
  return nullptr;
}
}  // namespace mediasdk