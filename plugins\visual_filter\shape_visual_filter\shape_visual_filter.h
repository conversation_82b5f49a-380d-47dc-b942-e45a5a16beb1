#pragma once

#include <graphics/graphics.h>
#include <graphics/texture.h>
#include "mediasdk/public/plugin/visual_filter.h"

namespace mediasdk {

class ShapeVisualFilter : public VisualFilter {
 public:
  explicit ShapeVisualFilter();

  ~ShapeVisualFilter() override;

  // VisualFilter
  bool Create(const char* json_params) override;

  void Destroy() override;

  MediaSDKString GetProperty(const char* key) override;

  bool SetProperty(const char* key, const char* json) override;

  MediaSDKString Action(const char* action, const char* json_params) override;


  VisualFilterUsePhase GetUsePhase() override {
    return VisualFilterUsePhase::kVisualFilterUsePhaseDraw;
  }

  virtual VisualFilterEffectiveScenario GetEffectiveScenario() override {
    return kVisualFilterEffectiveScenarioPreview;
  }

  void InitGraphicsResource(VisualFilterProxy* proxy) override;

  void ReleaseGraphicsResource() override;

  void ClearVideoCache() override;

  graphics::Texture* Process(graphics::Texture* texture,
                             const VisualFilterTransform& texture_trans,
                             int64_t timestamp) override;

  int64_t GetProcessDelayNS() override;

  virtual bool IsTopmost() { return true; }
  
protected:
  bool CreateTextureIfNecessary(graphics::Texture& texture,
                                const VisualFilterTransform& texture_data);
private:
  void CalcShapeTrans(int stretch_direction,
                     graphics::Rectangle::RectangleConfig& line_config,
                     graphics::Rectangle::RectangleConfig& line_config_w,
     graphics::GradualRectangle::GradualRectangleConfig& rec_config,
     graphics::GradualRectangle::GradualRectangleConfig& rec_config_w);
  int GetStretchingDirection(const VisualFilterTransform& texture_trans);

  bool GetDrawGradualRectangle();

 private:
  VisualFilterProxy* proxy_{nullptr};
  std::shared_ptr<graphics::Graphics> graphics_;
  bool line_fading_ = false;
  std::chrono::steady_clock::time_point line_fade_start_time_;
  float strech_starting_point_ = 0.5f;
  bool draw_gradual_ = false;
};

}  // namespace mediasdk