﻿#pragma once

#include <atomic>
#include <memory>
#include <optional>
#include <string>
#include <unordered_set>
#include <list>

#include "bef_framework_public_base_define.h"
#include "bef_msg_delegate_manager.h"
#include "effect_config.h"
#include "effect_notify_delegate.h"
#include "effect_statistics_notify_delegate.h"
#include "effectsdk_processor.h"
#include "gles_loader_autogen.h"
#include "mediasdk/public/effect_platform/mediasdk_effect_platform.h"

namespace BEF {
class BEFColorConverter;
}

namespace mediasdk {

class EffectContext;
class EffectStatistics;

class EffectSDK : public ep::Finder, public EffectStatisticsNotifyDelegate {
 public:
  static std::unique_ptr<EffectSDK> Create(
      int init_width,
      int init_height,
      std::shared_ptr<EffectContext> context,
      EffectNotifyDelegate* notify_delegate,
      const EffectSDKConfig& sdk_config);

  explicit EffectSDK(bef_effect_handle_t effect_handle,
                     int init_width,
                     int init_height,
                     std::shared_ptr<EffectContext> context,
                     EffectNotifyDelegate* notify_delegate,
                     const EffectSDKConfig& sdk_config);

  ~EffectSDK() override;

  bool Process(GLuint src_tex,
               EGLSurface surface,
               int src_width,
               int src_height,
               int dst_width,
               int dst_height,
               int64_t pts_ns,
               std::unique_ptr<MSTransform> gl_trans);

  void ProcessStatistics(int64_t begin_time);

  bool AddComposer(const std::vector<std::string>& composers,
                   const std::vector<std::string>& tags) const;
  bool SetComposer(const std::vector<std::string>& composers,
                   const std::vector<std::string>& tags) const;
  void SetNecessaryComposer(const std::vector<std::string>& composers) const;
  void RemoveNecessaryComposers(const std::unordered_set<std::string>& composers);
  void RemoveNecessaryComposer(const std::string& composer);
  bool UpdateComposer(const std::string& path,
                      const std::string& key,
                      float value,
                      const std::string& tag) const;
  bool RemoveComposer(const std::vector<std::string>& composers) const;
  bool SetTextComposer(const std::string& key, const std::string& value) const;
  bool SetBackgroundComposer(const std::string& key,
                             const std::string& path) const;
  bool ReplaceComposer(const std::vector<std::string>& old_paths,
                       const std::vector<std::string>& new_paths,
                       const std::vector<std::string>& tags) const;
  bool SendMsg(unsigned int msg_id, long arg1, long arg2, const std::string& arg3) const;
  int GetComposerExclusion(const std::string& path,
                           const std::string& key) const;
  bool GetAlgoUsed() const;

 public:
  // override EffectStatisticsNotifyDelegate
  void Notify(const std::string& key, const std::string& message_json) override;

  std::string GetBaseName(const std::string& full_path) const;

  void* GetEffectHandle() const;

 private:
  bool SetupSDK();
  bool ProcessPrepare(int width, int height);
  bool NeedUpdateTransform(MSTransform* gl_trans);
  void UpdateTransform(std::unique_ptr<MSTransform> gl_trans);
  bool EffectSetSize(int width, int height);
  bool EffectAlgorithm(GLuint src_texture, double bef_time, bool need_sync);
  bool EffectProcess(GLuint src_texture, GLuint dst_texture, double bef_time);
  bool RenderToSurface(GLuint dst_tex, int width, int height);
  bool FinishRender(int wait_ms);
  bool GLSyncWait(int wait_ms);
  std::string GetEffectVersion();
  bool SetRenderApi();
  void EffectConfigAB();
  bool ProcessRelayStatistics(const std::string& json_dump);

  // ep::Finder
  bool SetFinder(void* finder, void (*finder_release)(void*)) const override;

  static bool BEFDelegete(void* userdata,
                          unsigned int msgID,
                          int arg1,
                          int arg2,
                          const char* arg3);

 private:
  bool necessary_composer_finished = false;
  bef_effect_handle_t effect_handle_;
  std::shared_ptr<EffectSDKProcessor> sdk_processor_;
  std::unique_ptr<BEF::BEFColorConverter> render_;
  bef_render_msg_delegate_manager manager_ = nullptr;
  GLuint dst_tex_ = 0;
  int tex_width_ = 0;
  int tex_height_ = 0;
  std::unique_ptr<MSTransform> gl_trans_;
  std::shared_ptr<EffectContext> context_;
  EffectNotifyDelegate* notify_delegate_;
  EffectSDKConfig sdk_config_;

  uint64_t algorithm_err_count_ = 0;
  uint64_t process_err_count_ = 0;

  std::unique_ptr<EffectStatistics> effect_statistics_{nullptr};
  mutable std::unordered_set<std::string> composers_;
  mutable std::unordered_set<std::string> necessary_composers_;
  std::list<int> time_costs_;
  std::atomic_bool use_algo_{false};
};

}  // namespace mediasdk
