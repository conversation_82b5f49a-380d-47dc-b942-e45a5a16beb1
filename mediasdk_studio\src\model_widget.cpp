#include "model_widget.h"

#include <mmdeviceapi.h>
#include <qdebug.h>
#include <QFormLayout>
#include <QMainWindow>
#include <QMenu>
#include <QMessageBox>
#include <QMimeData>
#include <QMimeDatabase>
#include <QUuid>
#include <QVariant>
#include "mediasdk/mediasdk_call.h"
#include "mediasdk/public/mediasdk_api.h"
#include "mediasdk/public/mediasdk_defines.h"

#include <base/strings/stringprintf.h>
#include <base/time/time.h>

#include <QColorDialog>
#include <QFileDialog>
#include <QSpinBox>
#include <base/base64.hpp>
#include <nlohmann/json.hpp>
#include <sstream>
#include "config.h"
#include "effect_platform.h"
#include "linkmic_dialog.h"
#include "linkmic_menu.h"
#include "main_window.h"
#include "mediasdk/public/mediasdk_api_audio.h"
#include "mediasdk/public/mediasdk_api_rtc.h"
#include "mediasdk/public/mediasdk_api_visual.h"
#include "mediasdk/utils/time_helper.h"
#include "mediasdk_api_canvas.h"
#include "model_menu.h"
#include "plugins/visual_source/dshow_visual_source/dshow_visual_source_helper.h"
#include "rtc_events.h"
#include "settings_dialog.h"
#include "transition_helper_func.h"
#include "visual_menu.h"

namespace {

constexpr float kTargetRatio = 16.0f / 9.0f;
constexpr int32_t kModelFps = 30;
constexpr mediasdk::MSSize kOutputSize = {1920, 1080};

QRect CalcRatioFill(const QRect& region, float ratio) {
  float region_ratio = static_cast<float>(region.width()) / region.height();

  int new_width, new_height;
  if (region_ratio > ratio) {
    new_height = region.height();
    new_width = static_cast<int>(new_height * ratio);
  } else {
    new_width = region.width();
    new_height = static_cast<int>(new_width / ratio);
  }

  int new_x = region.x() + (region.width() - new_width) / 2;
  int new_y = region.y() + (region.height() - new_height) / 2;

  return QRect(new_x, new_y, new_width, new_height);
}

mediasdk::TransitionDirection DirectionString2Type(
    const std::string& direction) {
  if (direction == "up") {
    return mediasdk::TransitionDirection::kTransitionDirectionUp;
  }

  if (direction == "down") {
    return mediasdk::TransitionDirection::kTransitionDirectionDown;
  }

  if (direction == "right") {
    return mediasdk::TransitionDirection::kTransitionDirectionRight;
  }

  if (direction == "left") {
    return mediasdk::TransitionDirection::kTransitionDirectionLeft;
  }

  return mediasdk::TransitionDirection::kTransitionDirectionLeft;
}

mediasdk::TransitionSwipeType SwipeTypeString2Type(
    const std::string& direction) {
  if (direction == "swipe_in") {
    return mediasdk::TransitionSwipeType::TransitionSwipeTypeSwipeIn;
  }

  if (direction == "swipe_out") {
    return mediasdk::TransitionSwipeType::TransitionSwipeTypeSwipeOut;
  }

  return mediasdk::TransitionSwipeType::TransitionSwipeTypeSwipeOut;
}

mediasdk::TransitionProgressFunctionType FunctionString2Type(
    const std::string& function_type) {
  if (function_type == "linear") {
    return mediasdk::kTransitionProgressFunctionTypeLinear;
  }

  if (function_type == "sine") {
    return mediasdk::kTransitionProgressFunctionTypeEaseInOutSine;
  }

  if (function_type == "cubic") {
    return mediasdk::kTransitionProgressFunctionTypeEaseInOutCubic;
  }

  if (function_type == "quad") {
    return mediasdk::kTransitionProgressFunctionTypeEaseInOutQuad;
  }

  return mediasdk::kTransitionProgressFunctionTypeLinear;
}
}  // namespace

ListWidgetItemData::ListWidgetItemData(QListWidgetItem* item) {
  if (item) {
    item_name = item->text().toStdString();
    canvas_item_id =
        item->data(STR_KEY_CANVAS_ITEM_ID).toString().toStdString();
    visual_id = item->data(STR_KEY_VISUAL_ID).toString().toStdString();
    plugin_name = item->data(STR_KEY_PLUGIN_NAME_ID).toString().toStdString();
    device_id = item->data(STR_KEY_DEVICE_ID).toString().toStdString();
    video_model_id = item->data(STR_KEY_SINK_ID).toString().toStdString();
    type = item->data(STR_KEY_TYPE_ID).toString().toStdString();
    custom_data_id =
        item->data(STR_KEY_CUSTOM_DATA_ID).toString().toStdString();
  }
}

ModelWidget::ModelWidget(QWidget* parent, uint32_t sink_id, ModelProxy* proxy)
    : QWidget(parent),
      sink_id_(sink_id),
      canvas_mgr_(std::make_unique<CanvasManager>(this)),
      transition_manager_(std::make_unique<TransitionManager>()),
      proxy_(proxy) {
  ui_.setupUi(this);
  ui_.startLinkMicButton->setContextMenuPolicy(Qt::CustomContextMenu);
  connect(ui_.startLinkMicButton,
          SIGNAL(customContextMenuRequested(const QPoint&)), this,
          SLOT(ShowLinkMicMenu(const QPoint&)));

  connect(ui_.audioListWidget, &QListWidget::itemDoubleClicked, this,
          &ModelWidget::OnAudioInputItemAddClick);

  connect(this, &ModelWidget::startStreamResultSignal, this,
          &ModelWidget::OnStartStreamResultSlot, Qt::QueuedConnection);

  connect(this, &ModelWidget::streamStoppedSignal, this,
          &ModelWidget::OnStreamStoppedSlot, Qt::QueuedConnection);

  connect(&g_mediasdk_global_event_obs,
          &MediaSDKGlobalEventObserverImpl::onPluginGlobalEvent, this,
          [this](mediasdk::PluginInfo info, mediasdk::MediaSDKString event) {
            OnPluginGlobalEventHappened(info, event);
          });

  connect(this, &ModelWidget::RTCEventSignal, this, &ModelWidget::OnRTCEvent,
          Qt::QueuedConnection);

  connect(ui_.visualListWidget, &VisualListWidget::ReopenDShowInput, this,
          &ModelWidget::ReopenDShowInput);

  connect(ui_.visualListWidget, &VisualListWidget::SendSignalToParent, this,
          &ModelWidget::OnVisualMenuSlot);

  connect(ui_.visualListWidget, &QListWidget::customContextMenuRequested, this,
          &ModelWidget::OnShowContextMenu);

  connect(ui_.visualListWidget, &VisualListWidget::ShowVisualMenu, this,
          &ModelWidget::ShowVisualMenu);

  connect(ui_.canvasListWidget, &CanvasListWidget::ChangeCanvas, this,
          &ModelWidget::ShowCanvas);

  connect(EffectPlatform::Get(), &EffectPlatform::NoitfyEffectFps,
          ui_.effect_info, &QLabel::setText);

  timer_statics_ = new QTimer(this);
  connect(timer_statics_, SIGNAL(timeout()), this,
          SLOT(OnStaticsTimerTimeout()));
  timer_statics_->start(1000);

  // connect(ui_.settingsButtonTransition, &QPushButton::clicked, this, )

  ui_.visualListWidget->SetCurrentSink(sink_id_);
  UpdateVideoParamsPushButton(true);
  MediaSDKSyncCall(mediasdk::RegisterAudioObserver, this);
}

ModelWidget::~ModelWidget() {
  CloseOtherWindow();
  MediaSDKSyncCall(mediasdk::UnregisterRTCEventObserver, this);
  MediaSDKSyncCall(mediasdk::UnregisterWindowEventObserver, this);
  MediaSDKCall(base::BindOnce([](bool*) {}), &mediasdk::RemoveModel, sink_id_);
  MediaSDKSyncCall(mediasdk::UnregisterAudioObserver, this);
}

bool ModelWidget::ContainCanvas(const std::string& canvas_id) {
  return canvas_mgr_->ContainCanvas(canvas_id);
}

std::string ModelWidget::CreateCanvas(bool is_shown) {
  std::string canvas_id =
      QUuid::createUuid().toString().toStdString().substr(0, 4);

  return canvas_mgr_->CreateCanvas(canvas_id, sink_id_, is_shown)
             ? canvas_id
             : std::string{};
}

void ModelWidget::DestroyCanvas(const std::string& canvas_id) {
  return canvas_mgr_->DestroyCanvas(canvas_id);
}

void ModelWidget::ShowCanvas(const std::string& canvas_id,
                             const std::string& transition_id) {
  if (transition_manager_->GetTransitionTypeFromId(transition_id) == "Action") {
    auto compare_items = GetComparedCanvasItems(canvas_mgr_->CurrentCanvas(),
                                                canvas_id, "same_type");
    nlohmann::json compare_json;
    if (compare_items.empty()) {
      compare_json["compared_items"] = nlohmann::json();
    } else {
      nlohmann::json items_json = nlohmann::json::array();
      for (auto& [k, v] : compare_items) {
        items_json.push_back({{"source", k}, {"target", v}});
      }
      compare_json["compared_items"] = items_json;
    }
    transition_manager_->UpdateTransition(transition_id, compare_json.dump());
  }
  canvas_mgr_->ShowCanvas(canvas_id, transition_id);
  ui_.visualListWidget->SetCurrentCanvas(canvas_mgr_->CurrentCanvas());
  ui_.canvasListWidget->SetCurrentCanvas(
      QString::fromStdString(canvas_mgr_->CurrentCanvas()));
}

std::string ModelWidget::CurrentCanvas() {
  if (canvas_mgr_) {
    return canvas_mgr_->CurrentCanvas();
  }

  return std::string{};
}

void ModelWidget::OnAddToLayout() {
  {
    // Listen main window move event
    MainWindow* mainWindow = qobject_cast<MainWindow*>(this->window());
    if (mainWindow) {
      connect(mainWindow, &MainWindow::windowMoved, this,
              &ModelWidget::OnMainWindowMoved);
    }
  }

  mediasdk::ModelParams params = {};
  params.output_size = kOutputSize;
  params.fps = kModelFps;
  params.color_space = mediasdk::kColorSpaceBT709;
  params.video_range = mediasdk::kVideoRangeFull;
  params.show_window = true;

  // init position and parent window
  QMainWindow* main_window = dynamic_cast<QMainWindow*>(this->window());
  auto hwnd = (HWND)main_window->winId();
  QRect rect = ui_.previewRegion->geometry();
  params.hwnd_parent = hwnd;
  params.window_rect.x = rect.x();
  params.window_rect.y = rect.y();
  params.window_rect.cx = rect.width();
  params.window_rect.cy = rect.height();

  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnModelCreated, base::Unretained(this)),
      &mediasdk::CreateModel, sink_id_, params);
}

std::string ModelWidget::CreateTransition() {
  std::string current_type =
      ui_.transitionOptionWidget->currentText().toStdString();
  if (!transition_manager_) {
    return std::string{};
  }

  return transition_manager_->CreateTransition(current_type,
                                               transition_property_);
}

void ModelWidget::OnTransitionFinished(const std::string& transition_id) {
  // TODO(LY):
}

void ModelWidget::OnCurrentCanvasChanged(mediasdk::MediaSDKString id) {
  auto list_widget = ui_.visualListWidget;
  while (list_widget->count()) {
    auto item = list_widget->item(0);
    QString item_id = item->data(STR_KEY_CANVAS_ITEM_ID).toString();
    if (!item_id.toStdString().empty()) {
      list_widget->removeItemWidget(item);
      delete item;
    }
  }

  std::string current_canvas_id = id.ToString();
  if (canvas_map_data_.find(current_canvas_id) != canvas_map_data_.end()) {
    for (const auto& items_data : canvas_map_data_[current_canvas_id]) {
      QListWidgetItem* item = new QListWidgetItem(
          QString::fromStdString(items_data.item_name), ui_.visualListWidget);
      item->setData(STR_KEY_VISUAL_ID,
                    QString::fromStdString(items_data.visual_id));
      item->setData(STR_KEY_PLUGIN_NAME_ID,
                    QString::fromStdString(items_data.plugin_name));
      item->setData(STR_KEY_DEVICE_ID,
                    QString::fromStdString(items_data.device_id));
      item->setData(STR_KEY_SINK_ID,
                    QString::fromStdString(items_data.video_model_id));
      item->setData(STR_KEY_TYPE_ID, QString::fromStdString(items_data.type));
      item->setData(STR_KEY_CUSTOM_DATA_ID,
                    QString::fromStdString(items_data.custom_data_id));
      item->setData(STR_KEY_CANVAS_ITEM_ID,
                    QString::fromStdString(items_data.canvas_item_id));
    }
  }

  if (canvas_mgr_) {
    canvas_mgr_->SetCurrentCanvas(current_canvas_id);
  }
  current_canvas_ = current_canvas_id;
}

void ModelWidget::OnStartStreamResult(mediasdk::MediaSDKString id,
                                      mediasdk::StreamErrorCode error_code) {
  emit startStreamResultSignal(QString::fromStdString(id.ToString()),
                               (int)error_code);
};

void ModelWidget::OnReconnecting(mediasdk::MediaSDKString id) {}

void ModelWidget::OnConnected(mediasdk::MediaSDKString id) {
  connected_time_ = mediasdk::milli_now();
}

void ModelWidget::OnFirstFrame(mediasdk::MediaSDKString id) {
  auto now = mediasdk::milli_now();
  auto diff = now - connected_time_;
  qDebug() << "first frame cost: " << diff;
}

void ModelWidget::OnStartFallback(mediasdk::MediaSDKString id) {}

void ModelWidget::OnFallbackResult(mediasdk::MediaSDKString id, bool success) {}

void ModelWidget::OnStreamStopped(mediasdk::MediaSDKString id,
                                  mediasdk::StreamErrorCode error_code) {
  emit streamStoppedSignal(QString::fromStdString(id.ToString()),
                           (int)error_code);
}

void ModelWidget::OnBitrateChange(mediasdk::MediaSDKString id,
                                  uint32_t pre_bitrate_kbps,
                                  uint32_t bitrate_kbps) {
  std::string meta_bitrate = base::StringPrintf("[stream %s] meta bitrate: %d",
                                                id.data(), bitrate_kbps);
  QMetaObject::invokeMethod(
      QApplication::instance(),
      [this, meta_bitrate]() {
        ui_.meta_stream_info->setText(meta_bitrate.c_str());
      },
      Qt::QueuedConnection);
}

void ModelWidget::OnEncodeError(mediasdk::MediaSDKString id) {}

void ModelWidget::OnEncodeEvent(mediasdk::MediaSDKString id,
                                mediasdk::MediaSDKString json_param) {
  qDebug() << "OnEncodeEvent, id: " << id.data()
           << " param: " << json_param.data();
}

void ModelWidget::OnSpeedTestResult(mediasdk::MediaSDKString id,
                                    mediasdk::MediaSDKString result) {}

void ModelWidget::OnAudioEvent(mediasdk::MediaSDKString id,
                               mediasdk::MediaSDKString event) {
  nlohmann::json json_root;
  try {
    json_root = nlohmann::json::parse(event.ToString());
    std::string event_name =
        json_root.contains("event_name") ? json_root["event_name"] : "";
    int code = -1;
    if (json_root.contains("code")) {
      code = json_root["code"];
    } else if (json_root.contains("error_code")) {
      code = json_root["error_code"];
    } else if (json_root.contains("warning_code")) {
      code = json_root["warning_code"];
    }
    std::string message;
    if (json_root.contains("message")) {
      message = json_root["message"];
    } else if (json_root.contains("error_msg")) {
      message = json_root["error_msg"];
    } else if (json_root.contains("warning_msg")) {
      message = json_root["warning_msg"];
    }
#ifdef _DEBUG
    qDebug() << "[AudioEvent: " << QString::fromStdString(event_name)
             << "] [Code: " << code
             << "] [Message: " << QString::fromStdString(message) << "]"
             << " event [" << QString::fromStdString(event.ToString()) << "]";
#endif  // _DEBUG
  } catch (...) {
  }
}

void ModelWidget::OnPluginGlobalEventHappened(mediasdk::PluginInfo info,
                                              mediasdk::MediaSDKString event) {
  nlohmann::json json_root;
  try {
    json_root = nlohmann::json::parse(event.ToString());
    std::string event_name =
        json_root.contains("event_name") ? json_root["event_name"] : "";
    // deal with app audio state change events
    if (event_name == "APP_AUDIO_CAPTURE_SESSION_LIST_CHANGED") {
      MediaSDKCall(base::BindOnce(
                       [](ModelWidget* self, mediasdk::MediaSDKString* result) {
                         if (result) {
                           nlohmann::json json_root =
                               nlohmann::json::parse(result->ToString());
                           nlohmann::json& json_app_session =
                               json_root["app_session"];
                           self->UpdateSharedAppAudioItem(json_app_session);
                         }
                       },
                       base::Unretained(this)),
                   &wasapi_audio_source::EnumAudioInput, "AppAudioSource");
      return;
    }

    // deal with audio input/out device state change events
    if (event_name != "AUDIO_DEVICE_STATE_CHANGED" ||
        !json_root.contains("event_type")) {
      return;
    }
    int event_type = json_root["event_type"];
    std::string device_id = json_root["device_id"];
    int new_state = json_root["new_state"];
    int flow = json_root["flow"];
    int role = json_root["role"];

    // only deal with [on device state changed] & [on device removed] & [on
    // device added] events
    if ((event_type == 0 && new_state != DEVICE_STATE_ACTIVE) ||
        event_type == 2) {
      for (int qlist_item_index = 0;
           qlist_item_index < ui_.audioListWidget->count();
           ++qlist_item_index) {
        QListInputWidgetItem* item =
            (QListInputWidgetItem*)ui_.audioListWidget->item(qlist_item_index);
        if (device_id != item->GetCustomDeviceId() ||
            item->text().left(15) == "DEVICE_REMOVED:") {
          continue;
        }
        item->setText("DEVICE_REMOVED: " + item->text());
        item->setTextColor(Qt::red);
      }
    } else if ((event_type == 0 && new_state == DEVICE_STATE_ACTIVE) ||
               event_type == 1) {
      for (int qlist_item_index = 0;
           qlist_item_index < ui_.audioListWidget->count();
           ++qlist_item_index) {
        QListInputWidgetItem* item =
            (QListInputWidgetItem*)ui_.audioListWidget->item(qlist_item_index);
        if (device_id != item->GetCustomDeviceId()) {
          continue;
        }

        // deal with UI
        if (item->text().startsWith("DEVICE_REMOVED:")) {
          item->setText(item->text().right(item->text().length() - 16));
          item->setTextColor(Qt::black);
        }

        // delete and recreate & restart audio input stream
        std::string audio_input_id = item->GetCustomId();
        MediaSDKCall(base::BindOnce(
                         [](ModelWidget* self,
                            const std::string& audio_input_id, bool* result) {
                           // do nothing now
                         },
                         base::Unretained(this), audio_input_id),
                     &mediasdk::DestroyAudioInput, audio_input_id.c_str());

        mediasdk::CreateAudioParams params;
        params.track_id = sink_id_;
        params.plugin_name = item->GetCustomPluginName();
        nlohmann::json json_root;
        json_root["device_id"] = item->GetCustomDeviceId();
        json_root["device_name"] = item->GetCustomDeviceName();
        if (item->GetCustomType() == kAudioInputTypeMicrophone) {
          json_root["audio_input_type"] = mediasdk::kAudioInputMicrophone;
        } else if (item->GetCustomType() == kAudioInputTypeLoopback) {
          json_root["audio_input_type"] = mediasdk::kAudioInputLoopback;
        }
        params.json_params = json_root.dump();
        MediaSDKCall(
            base::BindOnce(
                [](ModelWidget* self, const std::string& audio_input_id,
                   bool* result) {
                  // do nothing now
                },
                base::Unretained(this), audio_input_id),
            &mediasdk::CreateAudioInput,
            audio_input_id.c_str(),  // after device unplugin / replugin, will
                                     // lose 3A if it has
            params);
      }
    }
  } catch (...) {
  }
}

void ModelWidget::AutoAddAudio(const std::string& plugin_name) {
  if (sink_id_ != 0) {
    return;
  }

  MediaSDKCall(base::BindOnce(
                   [](ModelWidget* self, std::string plugin_name,
                      mediasdk::MediaSDKString* result) {
                     if (result) {
                       std::vector<AudioDeviceInfo> vec_temp_info;
                       nlohmann::json json_root;
                       try {
                         json_root = nlohmann::json::parse(result->ToString());
                       } catch (...) {
                       }
                       if (!json_root.empty()) {
                         AudioDeviceInfo tmp;
                         tmp.id = json_root["device_id"];
                         tmp.name = json_root["device_name"];
                         self->OnAudioOutputItemClick(plugin_name, tmp.id,
                                                      tmp.name);
                       }
                     }
                   },
                   base::Unretained(this), plugin_name),
               &wasapi_audio_source::GetDefaultRenderAudio, plugin_name);
  MediaSDKCall(base::BindOnce(
                   [](ModelWidget* self, std::string plugin_name,
                      mediasdk::MediaSDKString* result) {
                     if (result) {
                       std::vector<AudioDeviceInfo> vec_temp_info;
                       nlohmann::json json_root;
                       try {
                         json_root = nlohmann::json::parse(result->ToString());
                       } catch (...) {
                       }
                       if (!json_root.empty()) {
                         AudioDeviceInfo tmp;
                         tmp.id = json_root["device_id"];
                         tmp.name = json_root["device_name"];
                         self->OnAudioInputItemClick(plugin_name, tmp.id,
                                                     tmp.name);
                       }
                     }
                   },
                   base::Unretained(this), plugin_name),
               &wasapi_audio_source::GetDefaultCaptureAudio, plugin_name);
}

void ModelWidget::OnEngineStart(int error_code) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventEngineStart, error_code,
                      QString::number(error_code));
}

void ModelWidget::OnEngineStop() {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventEngineStop, 0, QString());
}

void ModelWidget::OnJoinChannel(mediasdk::MediaSDKString room_id,
                                mediasdk::MediaSDKString user_id,
                                int state,
                                mediasdk::MediaSDKString extra_info) {
  nlohmann::json json_root;

  json_root["room_id"] = room_id.ToString();
  json_root["user_id"] = user_id.ToString();
  json_root["state"] = state;
  json_root["extra_info"] = extra_info.ToString();

  emit RTCEventSignal(mediasdk::kRTCNotifyEventJoinChannel, state,
                      QString::fromStdString(json_root.dump()));
}

void ModelWidget::OnLeaveChannel() {}

void ModelWidget::OnUserJoined(mediasdk::MediaSDKString user_id, int elapsed) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventUserJoined, elapsed,
                      QString::fromStdString(user_id.ToString()));
}

void ModelWidget::OnUserLeave(mediasdk::MediaSDKString user_id, int reason) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventUserLeave, reason,
                      QString::fromStdString(user_id.ToString()));
}

void ModelWidget::OnLocalStreamStats(mediasdk::MediaSDKString stats) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventLocalStreamStats, 0,
                      QString::fromStdString(stats.ToString()));
}

void ModelWidget::OnRemoteStreamStats(mediasdk::MediaSDKString user_id,
                                      mediasdk::MediaSDKString stats) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventRemoteStreamStats, 0,
                      QString::fromStdString(stats.ToString()));
}

void ModelWidget::OnUserPublishStream(mediasdk::MediaSDKString user_id,
                                      int stream_index,
                                      bool is_screen,
                                      int media_stream_type) {}

void ModelWidget::OnUserUnpublishStream(mediasdk::MediaSDKString user_id,
                                        int stream_index,
                                        int media_stream_type,
                                        int reason) {}

void ModelWidget::OnNetworkQuality(mediasdk::MediaSDKString quality) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventNetworkQuality, 0,
                      QString::fromStdString(quality.ToString()));
}

void ModelWidget::OnConnectionStateChanged(int state) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventConnectionStateChanged, state,
                      QString());
}

void ModelWidget::OnWarning(int warn) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventWarning, warn, QString());
}

void ModelWidget::OnError(int error) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventError, error, QString());
}

void ModelWidget::OnForwardStreamStateChanged(
    mediasdk::MediaSDKString stream_state_infos) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventForwardStreamStateChanged, 0,
                      QString::fromStdString(stream_state_infos.ToString()));
}

void ModelWidget::OnLocalAudioPropertiesReport(
    mediasdk::MediaSDKString audio_properties_infos) {
  emit RTCEventSignal(
      mediasdk::kRTCNotifyEventLocalAudioProperties, 0,
      QString::fromStdString(audio_properties_infos.ToString()));
}

void ModelWidget::OnRemoteAudioPropertiesReport(
    mediasdk::MediaSDKString audio_properties_infos) {
  emit RTCEventSignal(
      mediasdk::kRTCNotifyEventRemoteAudioProperties, 0,
      QString::fromStdString(audio_properties_infos.ToString()));
}

void ModelWidget::OnActiveSpeaker(mediasdk::MediaSDKString room_id,
                                  mediasdk::MediaSDKString uid) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventOnActiveSpeaker, 0,
                      QString::fromStdString(uid.ToString()));
}

void ModelWidget::OnAudioDeviceStateChanged(
    mediasdk::MediaSDKString state_info) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventAudioDeviceStateChanged, 0,
                      QString::fromStdString(state_info.ToString()));
}

void ModelWidget::OnFirstRemoteAudioFrame(mediasdk::MediaSDKString user_id,
                                          int stream_index) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventFirstRemoteAudioFrame,
                      stream_index, QString::fromStdString(user_id.ToString()));
}

void ModelWidget::OnFirstRemoteVideoFrameDecoded(
    mediasdk::MediaSDKString user_id,
    int stream_index,
    mediasdk::MediaSDKString frame_info) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventFirstRemoteVideoFrameDecoded,
                      stream_index,
                      QString::fromStdString(frame_info.ToString()));
}

void ModelWidget::OnFirstRemoteVideoFrameRendered(
    mediasdk::MediaSDKString user_id,
    int stream_index,
    mediasdk::MediaSDKString frame_info) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventFirstRemoteVideoFrameRendered,
                      stream_index,
                      QString::fromStdString(frame_info.ToString()));
}

void ModelWidget::OnRemoteVideoSizeChanged(
    mediasdk::MediaSDKString user_id,
    int stream_index,
    mediasdk::MediaSDKString frame_info) {
  emit RTCEventSignal(mediasdk::kRTCNotifyEventRemoteVideoSizeChanged,
                      stream_index,
                      QString::fromStdString(frame_info.ToString()));
}

void ModelWidget::OnSEIMessageReceived(mediasdk::MediaSDKString room_id,
                                       mediasdk::MediaSDKString user_id,
                                       int stream_index,
                                       mediasdk::MediaSDKString message) {
  nlohmann::json json_root;

  json_root["room_id"] = room_id.ToString();
  json_root["user_id"] = user_id.ToString();
  json_root["stream_index"] = stream_index;
  json_root["message"] = message.ToString();

  emit RTCEventSignal(mediasdk::kRTCNotifyEventSEIMessage, stream_index,
                      QString::fromStdString(json_root.dump()));
}

void ModelWidget::OnRoomMessageReceived(mediasdk::MediaSDKString user_id,
                                        mediasdk::MediaSDKString message) {
  nlohmann::json json_root;

  json_root["user_id"] = user_id.ToString();
  json_root["message"] = message.ToString();

  emit RTCEventSignal(mediasdk::kRTCNotifyEventRoomMessage, 0,
                      QString::fromStdString(json_root.dump()));
}

void ModelWidget::OnUserMessageReceived(mediasdk::MediaSDKString user_id,
                                        mediasdk::MediaSDKString message) {
  nlohmann::json json_root;

  json_root["user_id"] = user_id.ToString();
  json_root["message"] = message.ToString();

  emit RTCEventSignal(mediasdk::kRTCNotifyEventUserMessage, 0,
                      QString::fromStdString(json_root.dump()));
}

void ModelWidget::OnStreamMixingEvent(mediasdk::MediaSDKString task_id,
                                      int event_type,
                                      int error,
                                      int mixed_stream_type) {
  nlohmann::json json_root;

  json_root["task_id"] = task_id.ToString();
  json_root["event_type"] = event_type;
  json_root["error"] = error;
  json_root["mixed_stream_type"] = mixed_stream_type;

  emit RTCEventSignal(mediasdk::kRTCNotifyEventStreamMixingState, 0,
                      QString::fromStdString(json_root.dump()));
}

void ModelWidget::OnCanvasCreated(const std::string& canvas_id, bool is_shown) {
  canvas_map_data_.insert_or_assign(canvas_id,
                                    std::vector<ListWidgetItemData>());
  const std::string current_transition_id =
      transition_manager_->GetCurrentTransition();
  const std::string current_transition_type =
      transition_manager_->GetTransitionTypeFromId(current_transition_id);
  ui_.canvasListWidget->SetCurrentTransition(
      QString::fromStdString(current_transition_id), current_transition_type);

  static int s_canvas_cnt = 0;
  s_canvas_cnt++;
  QString item_name = "Canvas_" + QString::number(s_canvas_cnt);
  QListWidgetItem* item = new QListWidgetItem(item_name, ui_.canvasListWidget);
  item->setData(STR_KEY_CANVAS_ID, QString::fromStdString(canvas_id));
  item->setData(STR_KEY_SINK_ID,
                QString::fromStdString(std::to_string(sink_id_)));

  if (is_shown) {
    ShowCanvas(canvas_id, current_transition_id);
  }
}

void ModelWidget::OnCanvasDestroyed(const std::string& canvas_id) {
  auto list_widget = ui_.canvasListWidget;

  int row = 0;
  while (list_widget->count()) {
    auto item = list_widget->item(row++);
    QString item_name = item->data(STR_KEY_CANVAS_ID).toString();
    if (item_name.toStdString() == canvas_id) {
      list_widget->removeItemWidget(item);
      delete item;
    }

    if (!list_widget->count() || row >= list_widget->count()) {
      break;
    }
  }
}

void ModelWidget::OnCanvasItemCreated(const std::string& canvas_item_id,
                                      const VisualDeviceInfo& visual_info) {
  if (visual_info.plugin_name == graffiti_visual_source::GetPluginName()) {
    MediaSDKSyncCall(mediasdk::SetCanvasItemEditable, canvas_item_id.c_str(),
                     true);
  }
}

void ModelWidget::OnCanvasItemDestroyed(const std::string& canvas_item_id) {
  auto list_widget = ui_.visualListWidget;

  int row = 0;
  while (list_widget->count()) {
    auto item = list_widget->item(row++);
    QString item_id = item->data(STR_KEY_CANVAS_ITEM_ID).toString();

    if (item_id.toStdString() == canvas_item_id) {
      list_widget->removeItemWidget(item);

      const auto iter = canvas_map_data_.find(canvas_mgr_->CurrentCanvas());
      if (iter != canvas_map_data_.end()) {
        for (auto iter_item = iter->second.begin();
             iter_item != iter->second.end();) {
          if (iter_item->canvas_item_id == canvas_item_id) {
            iter_item = iter->second.erase(iter_item);
          } else {
            ++iter_item;
          }
        }
      }

      delete item;
    }

    if (!list_widget->count() || row >= list_widget->count()) {
      break;
    }
  }
}

void ModelWidget::CreateCanvasItem(
    const std::string& visual_id,
    const mediasdk::CreateCanvasItemParams& params) {
  if (proxy_) {
    proxy_->CreateCanvasItemOnModelId(1 - sink_id_, visual_id, params);
  }
}

void ModelWidget::CreateCanvasItemOnCanvas(
    const std::string& visual_id,
    const std::string& canvas_id,
    const std::string& canvas_item_name,
    const mediasdk::CreateCanvasItemParams& params) {
  if (!canvas_mgr_) {
    return;
  }
  auto canvas_it = canvas_map_data_.find(canvas_id);
  if (canvas_it == canvas_map_data_.end()) {
    return;
  }

  auto visual_it = visual_device_info_.find(visual_id);
  VisualDeviceInfo visual_info = {};
  if (visual_it != visual_device_info_.end()) {
    visual_info = visual_it->second;
  }
  std::string id = QUuid::createUuid().toString().toStdString().substr(0, 4);
  canvas_mgr_->CreateCanvasItem(canvas_id, id, visual_id, params, visual_info);

  QListWidgetItem* item =
      new QListWidgetItem(QString::fromStdString(canvas_item_name), nullptr);
  item->setData(STR_KEY_CANVAS_ITEM_ID, QString::fromStdString(id));
  item->setData(STR_KEY_VISUAL_ID, QString::fromStdString(visual_id));
  item->setData(STR_KEY_PLUGIN_NAME_ID,
                QString::fromStdString(visual_info.plugin_name));
  item->setData(STR_KEY_DEVICE_ID,
                QString::fromStdString(visual_info.device_id));
  item->setData(STR_KEY_SINK_ID,
                QString::fromStdString(std::to_string(sink_id_)));
  item->setData(STR_KEY_TYPE_ID,
                QString::fromStdString(std::to_string(visual_info.type)));
  item->setData(STR_KEY_CUSTOM_DATA_ID,
                QString::fromStdString(visual_info.filters_json));
  canvas_it->second.push_back(ListWidgetItemData(item));

  if (canvas_id == current_canvas_) {
    ui_.visualListWidget->addItem(item);
  } else {
    delete item;
  }
}

std::map<std::string, std::string> ModelWidget::GetCanvasesOfNotCurrent() {
  std::vector<std::pair<std::string, std::string>> canvas_list;
  return ui_.canvasListWidget->GetCanvasIdMapName();
}

void ModelWidget::keyReleaseEvent(QKeyEvent* event) {
  QWidget::keyReleaseEvent(event);
}

void ModelWidget::OnRButtonDown(int sink_id) {
  if (sink_id != sink_id_) {
    return;
  }

  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCurrentCanvasItem, base::Unretained(this)),
      mediasdk::GetCurrentCanvasItemOnVideoModel, sink_id);
}

void ModelWidget::OnRButtonUp(int sink_id) {
  if (sink_id != sink_id_) {
    return;
  }
}

void ModelWidget::OnLButtonDblClk(int sink_id) {
  if (sink_id != sink_id_) {
    return;
  }
#ifdef _DEBUG
  qDebug() << "OnLButtonDblClk " << sink_id;
#endif  // _DEBUG
}

void ModelWidget::OnLButtonDown(int sink_id) {
  if (sink_id != sink_id_) {
    return;
  }
#ifdef _DEBUG
  qDebug() << "OnLButtonDown " << sink_id;
#endif  // _DEBUG
}

void ModelWidget::OnLButtonUp(int sink_id) {
  if (sink_id != sink_id_) {
    return;
  }
#ifdef _DEBUG
  qDebug() << "[test][ModelWidget]OnLButtonUp " << sink_id;
#endif  // _DEBUG
}

void ModelWidget::OnMouseLeave(int sink_id) {
  if (sink_id != sink_id_) {
    return;
  }
}

void ModelWidget::OnMouseHover(int sink_id) {
  if (sink_id != sink_id_) {
    return;
  }
}

void ModelWidget::resizeEvent(QResizeEvent* event) {
  ResizePreview();

  QWidget::resizeEvent(event);
}

void ModelWidget::dragEnterEvent(QDragEnterEvent* event) {
  if (event->mimeData()->hasUrls()) {
    event->acceptProposedAction();
  }
}

void ModelWidget::dropEvent(QDropEvent* event) {
  if (!event->mimeData()->hasUrls()) {
    return;
  }

  QList<QUrl> url_list = event->mimeData()->urls();
  for (auto& item : url_list) {
    std::string file_path = item.toLocalFile().toStdString();
    auto visual_type = GetDragFileVisualSourceType(file_path);
    switch (visual_type) {
      case kVisualSourceTypeFav: {
        OnFAVInputItemCreate(
            CurrentFavVisualId(), fav_visual_source::GetPluginName().data(),
            fav_visual_source::CreateParam{file_path, true, true});
        break;
      }
      case kVisualSourceTypeImage: {
        OnImageVisualSourceInputWidgetClick(
            image_visual_source::GetPluginName().data(), file_path);
        break;
      }
      default:
        break;
    }
  }
}

void ModelWidget::keyPressEvent(QKeyEvent* event) {
  if (event->key() == Qt::Key_Delete) {
    mediasdk::MediaSDKString result =
        MediaSDKSyncCall<mediasdk::MediaSDKString>(
            mediasdk::GetCurrentCanvasItemOnVideoModel, sink_id_);

    std::string canvas_item_id = result.ToString();
    if (canvas_mgr_) {
      canvas_mgr_->DestroyCanvasItem(canvas_item_id);
    }
  }
}

void ModelWidget::OnCurrentCanvasItemChanged(mediasdk::MediaSDKString id) {
  ui_.visualListWidget->SetCurrentItem(QString::fromStdString(id.ToString()));
}

void ModelWidget::OnHittestVisualChanged(mediasdk::MediaSDKString id) {
  if (id.empty()) {
    ui_.visualListWidget->SetCurrentItem("");
  }
}

void ModelWidget::OnVisualTransformChanged(
    mediasdk::MediaSDKString id,
    const mediasdk::MSTransform& transform) {}

void ModelWidget::OnVisualSizeChanged(mediasdk::MediaSDKString id,
                                      const mediasdk::MSSize& new_size) {
  auto iter = visual_device_info_.find(id.ToString());
  if (iter != visual_device_info_.end()) {
    if (iter->second.is_bytelink) {
      // MediaSDKCall<bool>(base::BindOnce([](ModelWidget* self, bool* result)
      // {},
      //                                   base::Unretained(this)),
      //                    &mediasdk::SetCanvasItemClip, id.ToString().c_str(),
      //                    mediasdk::MSClipF{});
      // MediaSDKCall<bool>(base::BindOnce([](ModelWidget* self, bool* result)
      // {},
      //                                   base::Unretained(this)),
      //                    &mediasdk::SetCanvasItemFlipH,
      //                    id.ToString().c_str(), false);
      // MediaSDKCall<bool>(base::BindOnce([](ModelWidget* self, bool* result)
      // {},
      //                                   base::Unretained(this)),
      //                    &mediasdk::SetCanvasItemFlipV,
      //                    id.ToString().c_str(), false);
      // MediaSDKCall<bool>(base::BindOnce([](ModelWidget* self, bool* result)
      // {},
      //                                   base::Unretained(this)),
      //                    &mediasdk::SetCanvasItemRotate,
      //                    id.ToString().c_str(), 0.0);
    }
  }
}

void ModelWidget::OnBeginTrack(mediasdk::MediaSDKString id) {}

void ModelWidget::OnEndTrack(mediasdk::MediaSDKString id) {}

void ModelWidget::OnVisualInvalidArea(mediasdk::MediaSDKString id) {}

void ModelWidget::OnVisualValidArea(mediasdk::MediaSDKString id) {}

void ModelWidget::OnVisualStateChange(const std::string& id, bool state) {
  for (int i = 0; i < ui_.visualListWidget->count(); ++i) {
    QListWidgetItem* item = ui_.visualListWidget->item(i);
    if (item->data(STR_KEY_VISUAL_ID) == QString::fromStdString(id)) {
      item->setTextColor(state ? QColor(Qt::GlobalColor::black)
                               : QColor(Qt::GlobalColor::red));
    }
  }
}

void ModelWidget::OnCurrentCanvasItem(
    mediasdk::MediaSDKString* canvas_item_id) {
  std::string canvas_item_id_str{};
  if (canvas_item_id && !canvas_item_id->empty()) {
    canvas_item_id_str = canvas_item_id->ToString();
  }

  auto visual_id = MediaSDKSyncCall<mediasdk::MediaSDKString>(
      mediasdk::GetVisualFromCanvasItem, canvas_item_id_str.c_str());
  std::string visual_id_str = visual_id.ToString();

  QMetaObject::invokeMethod(
      qApp,
      [this, canvas_item_id_str, visual_id_str]() {
        ShowCurrentVisualMenu(canvas_item_id_str, visual_id_str);
      },
      Qt::QueuedConnection);
}

void ModelWidget::SetFixInfo(int32_t& w, int32_t& h) {
  fix_height_ = h;
  fix_width_ = w;
  ResizePreview();
}

void ModelWidget::OnModelCreated(bool* result) {
  if (result && *result) {
    ResizePreview();
  }

  MediaSDKSyncCall(mediasdk::RegisterWindowEventObserver, this);

  // Create default canvas when model created
  current_canvas_ = CreateCanvas(true);

  QTimer::singleShot(0, [this]() {
    AutoAddAudio(wasapi_audio_source::GetPluginName().data());
  });
}

void ModelWidget::OnModelDestroyed(bool* result) {}

void ModelWidget::ResizePreview() {
  QRect rect = ui_.previewRegion->geometry();
  QMainWindow* mainWindow = dynamic_cast<QMainWindow*>(this->window());
  QPoint pos = ui_.previewRegion->mapTo(mainWindow, rect.topLeft());
  if (fix_width_ > 0 && fix_height_ > 0) {
    rect.setWidth(fix_width_);
    rect.setHeight(fix_height_);
  }
  rect.moveTopLeft(pos);
  rect = CalcRatioFill(rect, kTargetRatio);

  mediasdk::MSRect window_rect;
  window_rect.x = rect.x();
  window_rect.y = rect.y();
  window_rect.cx = rect.width();
  window_rect.cy = rect.height();

  MediaSDKCall(base::BindOnce([](bool* result) {}),
               &mediasdk::SetPreviewPosition, sink_id_, window_rect);

  if (pre_window_width_ != 0) {
    MediaSDKCall(base::BindOnce([](bool* result) {}),
                 &mediasdk::SetScaleForVideoModel, sink_id_,
                 (float)rect.width() / (float)pre_window_width_);
  }
  pre_window_width_ = rect.width();
  pre_window_height_ = rect.height();
}

void ModelWidget::ShowLinkMicDialog() {
  LinkMicDialog linkmic_dlg(this);
  if (linkmic_dlg.exec() == QDialog::Accepted) {
    std::string uid, rid;
    auto& linkmic_config = Config::GetInstance().config()["linkmic_config"];
    if (linkmic_config.contains("user_id")) {
      uid = linkmic_config["user_id"].get<std::string>();
    }

    if (linkmic_config.contains("room_id")) {
      rid = linkmic_config["room_id"].get<std::string>();
    }

    if (!uid.empty() && !rid.empty()) {
      MediaSDKSyncCall(mediasdk::JoinRoom, rid.c_str(), uid.c_str(), "", "");
    } else {
      QMessageBox::critical(nullptr, "LinkMic",
                            QString("Invalid room id or user id"));
    }
  }
}

void ModelWidget::ShowLinkMicMenu(const QPoint& pos) {
  if (LoadingButton::kBack == ui_.startLinkMicButton->GetState()) {
    LinkMicMenu menu(this, sink_id_);
    menu.UpdateSettings(linkmic_settings_);
    menu.exec(QCursor::pos());
    linkmic_settings_ = menu.GetSettings();
  }
}

void ModelWidget::ShowVisualSourceMenu(
    const std::vector<std::string>& source_name) {
  auto source_menu = new QMenu(this);
  for (const auto& it : source_name) {
    source_menu->addAction(QString::fromStdString(it), [this, it]() {
      if (it == dshow_visual_source::GetPluginName()) {
        OnDShowEnumInputs(it, "", dshow_visual_source::kCaptureTypeCamera);
      } else if (it == fav_visual_source::GetPluginName()) {
        ShowFAVWidget();
      } else if (it == desktop_capture_visual_source::GetPluginName()) {
        OnDesktopCaptureEnumInputs(it);
      } else if (it == image_visual_source::GetPluginName()) {
        OnShowImageVisualWidget(image_visual_source::GetPluginName().data());
      } else if (it == browser_visual_source::GetPluginName()) {
        ShowBrowserVisualWidget(browser_visual_source::GetPluginName().data());
      } else if (it == bytelink_visual_source::GetPluginName()) {
        ShowBytelinkVisualWidget(
            bytelink_visual_source::GetPluginName().data());
      } else if (it == game_visual_source::GetPluginName()) {
        OnGameEnumInputs(game_visual_source::GetPluginName().data());
      } else {
        CreateVisual(it, "");
      }
    });
  }

  source_menu->popup(QCursor::pos());
}

void ModelWidget::ShowAudioInputSourceMenu(
    const std::vector<std::string>& source_name) {
  auto source_menu = new QMenu(this);
  for (const auto& it : source_name) {
    if (std::string(it.c_str()) == "PCMAudioSource") {
      source_menu->addAction(QString::fromStdString(it), [this, it]() {
        if (!has_pcm_audio_source_) {
          int64_t create_time = mediasdk::low_precision_milli_now();
          std::string pcm_file_name("victory.pcm");
          QDir dir(QCoreApplication::applicationDirPath());
          QString pcm_file_path = dir.filePath(pcm_file_name.c_str());
          std::string file_path = pcm_file_path.toStdString();
          QFile pcm_file(pcm_file_path);
          if (!pcm_file.exists() || !pcm_file.open(QIODevice::ReadOnly)) {
            QMessageBox::critical(nullptr, "error",
                                  "Open PCM audio file failed!");
            return;
          }

          const QByteArray pcm_binary_datas = pcm_file.readAll();
          std::vector<uint8_t> pcm_binary_data_vec;
          pcm_binary_data_vec.reserve(pcm_binary_datas.size());
          for (int index = 0; index < pcm_binary_datas.length(); index++) {
            pcm_binary_data_vec.push_back(
                (unsigned char)pcm_binary_datas.at(index));
          }
          pcm_file.close();

          // create pcm audio data source
          mediasdk::CreateAudioParams params;
          params.track_id = sink_id_;
          params.plugin_name = std::string("PCMAudioSource");
          {
            nlohmann::json json_root;
            json_root["exe_name"] = "PCM";
            json_root["audio_input_type"] = mediasdk::kAudioInputPCM;
            json_root["sample_rate"] = 44100;
            json_root["channel_num"] = 2;
            json_root["layout"] = 2;  // CHANNEL_STEREO
            json_root["format"] = 8;  // AUDIO_FORMAT_FLOAT_PLANAR
            std::string_view pcm_binary_data_sv(
                reinterpret_cast<char*>(pcm_binary_data_vec.data()),
                pcm_binary_data_vec.size());
            json_root["left_channel_datas"] =
                base64::to_base64(pcm_binary_data_sv);
            json_root["right_channel_datas"] =
                base64::to_base64(pcm_binary_data_sv);
            params.json_params = json_root.dump();
          }
          std::string id =
              QUuid::createUuid().toString().toStdString().substr(0, 4);

          MediaSDKCall(base::BindOnce(&ModelWidget::OnCreateAudioResult,
                                      base::Unretained(this), id, "PCM",
                                      "PCMAudioSource", "PCMAudioSource", false,
                                      sink_id_, kAudioInputTypeApp),
                       &mediasdk::CreateAudioInput, id.c_str(), params);
        }
      });
    } else {
      source_menu->addAction(QString::fromStdString(it), [this, it]() {
        MediaSDKCall(base::BindOnce(
                         [](ModelWidget* self, const std::string& title,
                            mediasdk::MediaSDKString* result) {
                           if (result) {
                             self->ShowAudioInputWidget(title,
                                                        result->ToString());
                           }
                         },
                         base::Unretained(this), it.c_str()),
                     &wasapi_audio_source::EnumAudioInput, it.c_str());
      });
      if (it == "WASAPIAudioSource") {
        // add LyraxAudioInput
        source_menu->addAction(
            QString::fromStdString("LyraxAudioInput"), [this, it]() {
              MediaSDKCall(base::BindOnce(
                               [](ModelWidget* self, const std::string& title,
                                  mediasdk::MediaSDKString* result) {
                                 if (result) {
                                   self->ShowAudioInputWidget(
                                       title, result->ToString());
                                 }
                               },
                               base::Unretained(this), "LyraxAudioInput"),
                           &wasapi_audio_source::EnumAudioInput, it.c_str());
            });
      }
    }
  }
  source_menu->popup(QCursor::pos());
}

void ModelWidget::ShowAudioInputWidget(const std::string& title,
                                       const std::string& json_info) {
  if (!audio_input_ui_) {
    audio_input_ui_ = new AudioInputWidget(window());
    QObject::connect(audio_input_ui_, &AudioInputWidget::AudioInputItemClick,
                     this, &ModelWidget::OnAudioInputItemClick);
    QObject::connect(audio_input_ui_, &AudioInputWidget::AudioOutputItemClick,
                     this, &ModelWidget::OnAudioOutputItemClick);
    QObject::connect(audio_input_ui_, &AudioInputWidget::AudioAppItemClick,
                     this, &ModelWidget::OnAudioAppItemClick);
  }
  audio_input_ui_->UpdateUI(title, json_info);
}

void ModelWidget::ShowDShowInputWidget(const std::string& title,
                                       const std::string& json_info,
                                       int32_t enum_type,
                                       const std::string& visual_id) {
  if (dshow_input_ui_) {
    dshow_input_ui_->close();
    dshow_input_ui_->deleteLater();
    dshow_input_ui_ = nullptr;
  }

  dshow_input_ui_ = new DShowVisualInputWidget(window(), canvas_mgr_.get());

  connect(dshow_input_ui_, &DShowVisualInputWidget::DShowVisualInputItemClick,
          this, &ModelWidget::OnDShowVisualInputItemClick);

  connect(dshow_input_ui_,
          &DShowVisualInputWidget::DShowVisualInputItemClickedWithFilter, this,
          &ModelWidget::OnDShowVisualInputItemClickWithFilter);

  dshow_input_ui_->UpdateUI(title, json_info, visual_id);
}

void ModelWidget::ShowDesktopCaptureWidget(
    const std::string& title,
    const std::string& json_info,
    desktop_capture_visual_source::EnumMethod method) {
  if (!desktop_capture_input_ui_) {
    desktop_capture_input_ui_ = new DesktopCaptureVisualInputWidget(window());
    QObject::connect(
        desktop_capture_input_ui_,
        &DesktopCaptureVisualInputWidget::DesktopCaptureVisualInputItemClick,
        this, &ModelWidget::OnDesktopCaptureVisualInputItemClick);
  }
  desktop_capture_input_ui_->UpdateUI(title, json_info, method);
}

void ModelWidget::OnShowImageVisualWidget(const std::string& plugin_name) {
  MediaSDKCall(base::BindOnce(
                   [](ModelWidget* self, const std::string& title,
                      mediasdk::MediaSDKString* result) {
                     if (result) {
                       self->ShowImageVisualWidget(title, result->ToString());
                     }
                   },
                   base::Unretained(this), plugin_name),
               &image_visual_source::EnumVisualInput);
}

void ModelWidget::ShowImageVisualWidget(const std::string& title,
                                        const std::string& json) {
  if (!image_visual_input_ui_) {
    image_visual_input_ui_ = new ImageVisualSourceInputWidget(window());
    QObject::connect(
        image_visual_input_ui_,
        &ImageVisualSourceInputWidget::ImageVisualSourceInputWidgetClick, this,
        &ModelWidget::OnImageVisualSourceInputWidgetClick);
  }
  image_visual_input_ui_->UpdateUI(title, json);
}

void ModelWidget::ShowBrowserVisualWidget(const std::string& title) {
  if (!browser_visual_input_ui_) {
    browser_visual_input_ui_ = new BrowserVisualInputWidget(window());
    QObject::connect(
        browser_visual_input_ui_,
        &BrowserVisualInputWidget::BrowserVisualInputWidgetInputItemClick, this,
        &ModelWidget::OnBrowserVisualInputWidgetInputItemClick);
  }
  browser_visual_input_ui_->UpdateUI(title, "");
}

void ModelWidget::ShowBytelinkVisualWidget(const std::string& title) {
  if (!bytelink_visual_input_ui_) {
    bytelink_visual_input_ui_ = new ByteLinkVisualInputWidget(window());
    QObject::connect(
        bytelink_visual_input_ui_,
        &ByteLinkVisualInputWidget::ByteLinkVisualInputWidgetItemClick, this,
        &ModelWidget::OnByteLinkVisualInputWidgetItemClick);
    QObject::connect(
        bytelink_visual_input_ui_,
        &ByteLinkVisualInputWidget::ByteLinkVisualInputWidgetItemClose, this,
        &ModelWidget::OnByteLinkVisualInputWidgetItemClose);
  }
  bytelink_visual_input_ui_->UpdateUI(title, "");
}

void ModelWidget::ShowGameVisualWidget(const std::string& title,
                                       const std::string& json) {
  if (!game_visual_params_ui_) {
    game_visual_params_ui_ = new GameVisualInputWidget(window());
    QObject::connect(game_visual_params_ui_,
                     &GameVisualInputWidget::GameVisualInputItemClick, this,
                     &ModelWidget::OnGameVisualInputItemClick);
  }
  game_visual_params_ui_->UpdateUI(title, json);
}

void ModelWidget::ShowAudioAECRefWidget(const std::string& audio_id) {
  if (!audio_aec_ref_widget_) {
    audio_aec_ref_widget_ = new AudioAECRefWidget(window());
    QObject::connect(audio_aec_ref_widget_, &AudioAECRefWidget::ItemSelected,
                     this, &ModelWidget::OnAudioAECRefItemSelected);
  }
  std::vector<QListInputWidgetItem*> items;
  for (int i = 0; i < ui_.audioListWidget->count(); ++i) {
    QListWidgetItem* item = ui_.audioListWidget->item(i);
    QListInputWidgetItem* input_item =
        dynamic_cast<QListInputWidgetItem*>(item);
    if (input_item) {
      items.push_back(input_item);
    }
  }
  audio_aec_ref_widget_->UpdateUI(audio_id, items);
}

void ModelWidget::OnAudioAECRefItemSelected(const std::string& audio_input_id,
                                            const std::string& ref_audio_id) {
  MediaSDKCall(base::BindOnce(
                   [](ModelWidget* self, bool* result) {
                     // do nothing
                   },
                   base::Unretained(this)),
               &mediasdk::SetAudioInputReferenceId, audio_input_id.c_str(),
               ref_audio_id.c_str());
}

void ModelWidget::ShowCurrentVisualMenu(const std::string& canvas_item_id,
                                        const std::string& visual_id) {
  if (!visual_id.empty()) {
    auto it = canvas_map_data_.find(current_canvas_);
    if (it == canvas_map_data_.end()) {
      return;
    }
    for (auto& item : it->second) {
      if (item.canvas_item_id == canvas_item_id) {
        ShowVisualMenu(canvas_item_id, item.item_name, visual_id);
      }
    }
  } else {
    QRect rect = ui_.previewRegion->geometry();
    QMainWindow* mainWindow = dynamic_cast<QMainWindow*>(this->window());
    QPoint rect_pos = ui_.previewRegion->mapTo(mainWindow, rect.topLeft());
    if (fix_width_ > 0 && fix_height_ > 0) {
      rect.setWidth(fix_width_);
      rect.setHeight(fix_height_);
    }
    rect.moveTopLeft(rect_pos);
    rect = CalcRatioFill(rect, kTargetRatio);

    ModelMenu menu(this, sink_id_, rect);
    menu.exec(QCursor::pos());
  }
}

void ModelWidget::CreateFAVInput(const std::string& visual_id,
                                 const std::string& plugin_name,
                                 const fav_visual_source::CreateParam& param) {
  mediasdk::CreateVisualParams params = {};
  params.plugin_name = plugin_name;
  params.audio_track_id = 0;
  params.json_params = fav_visual_source::Create(param);

  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCreateFAVResult, base::Unretained(this),
                     visual_id, params.plugin_name.ToString(), sink_id_),
      &mediasdk::CreateVisual, visual_id.c_str(), params);
}

std::string ModelWidget::CreateVisual(const std::string& plugin_name,
                                      const std::string& json_params) {
  mediasdk::CreateVisualParams params = {};
  params.plugin_name = plugin_name;
  params.audio_track_id = 0;
  params.json_params = json_params;
  params.destroy_when_all_ref_removed = true;
  std::string visual_id =
      QUuid::createUuid().toString().toStdString().substr(0, 4);

  VisualDeviceInfo info = {};
  info.plugin_name = plugin_name;
  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCreateVisualResult, base::Unretained(this),
                     visual_id, info, sink_id_),
      &mediasdk::CreateVisual, visual_id.c_str(), params);
  return visual_id;
}

void ModelWidget::DShowCreateVisual(
    const std::string& plugin_name,
    const std::string& visual_id,
    int32_t capture_type,
    const dshow_visual_source::CaptureParams& vparams,
    const dshow_visual_source::AudioCaptureParams& aparams) {
  if (visual_id.empty()) {
    std::string visual_id_new = QUuid::createUuid().toString().toStdString();
    VisualDeviceInfo info = {};
    info.device_id = vparams.device_id;
    info.plugin_name = plugin_name;
    info.type = capture_type;
    if (capture_type == dshow_visual_source::kCaptureTypeCamera) {
      MediaSDKCall(
          base::BindOnce(&ModelWidget::OnCreateVisualResult,
                         base::Unretained(this), visual_id_new, info, sink_id_),
          &dshow_visual_source::CreateCameraCapture, visual_id_new.c_str(),
          vparams, sink_id_);
    } else {
      MediaSDKCall(
          base::BindOnce(&ModelWidget::OnCreateVisualResultAudio,
                         base::Unretained(this), visual_id_new, info, sink_id_,
                         (aparams.render_type ==
                          dshow_visual_source::kAnalogRenderTypeNone)
                             ? mediasdk::kAudioMonitorRender
                             : 0),
          &dshow_visual_source::CreateAnalogCapture, visual_id_new.c_str(),
          vparams, aparams, sink_id_);
    }
  } else {
    MediaSDKCall(base::BindOnce([](ModelWidget* self, bool* result) {},
                                base::Unretained(this)),
                 &dshow_visual_source::Reopen, visual_id.c_str(), vparams,
                 aparams);
  }
}

void ModelWidget::CreateDesktopCaptureVisual(
    const std::string& plugin_name,
    const std::string& device_id,
    int32_t method,
    uint64_t item_id,
    int32_t capture_method,
    bool show_cursor,
    bool gdi_compatible,
    desktop_capture_visual_source::WindowCaptureParams wparams) {
  std::string visual_id =
      QUuid::createUuid().toString().toStdString().substr(0, 4);
  VisualDeviceInfo info = {};
  info.plugin_name = plugin_name;
  switch (method) {
    case desktop_capture_visual_source::kEnumMethodWindows: {
      desktop_capture_visual_source::WindowCaptureParams params = {};
      params.id = item_id;
      params.show_cursor = show_cursor;
      params.capture_method =
          (desktop_capture_visual_source::WindowCaptureMethod)capture_method;
      params.class_name = wparams.class_name;
      params.exe_name = wparams.exe_name;
      MediaSDKCall(
          base::BindOnce(&ModelWidget::OnCreateVisualResultDesktopCapture,
                         base::Unretained(this), visual_id, info, sink_id_,
                         gdi_compatible),
          &desktop_capture_visual_source::CreateWindowCapture,
          visual_id.c_str(), params, sink_id_);
    } break;
    case desktop_capture_visual_source::kEnumMethodScreens: {
      desktop_capture_visual_source::ScreenCaptureParams params = {};
      params.device_id = device_id;
      params.show_cursor = show_cursor;
      params.capture_method =
          (desktop_capture_visual_source::WindowCaptureMethod)capture_method;
      MediaSDKCall(
          base::BindOnce(&ModelWidget::OnCreateVisualResultDesktopCapture,
                         base::Unretained(this), visual_id, info, sink_id_,
                         gdi_compatible),
          &desktop_capture_visual_source::CreateScreenCapture,
          visual_id.c_str(), params, sink_id_);
    } break;
    default:
      break;
  }
}

void ModelWidget::OnCreateFAVResult(const std::string& visual_id,
                                    const std::string& plugin_name,
                                    uint32_t sink_id,
                                    bool* result) {
  static int s_visual_cnt = 0;

  if (result && *result) {
    s_visual_cnt++;

    QString item_name = QString::fromStdString(plugin_name) + "_" +
                        QString::number(s_visual_cnt);
    QListWidgetItem* item =
        new QListWidgetItem(item_name, ui_.visualListWidget);
    item->setData(STR_KEY_VISUAL_ID, QString::fromStdString(visual_id));
    item->setData(STR_KEY_PLUGIN_NAME_ID, QString::fromStdString(plugin_name));
    item->setData(STR_KEY_SINK_ID,
                  QString::fromStdString(std::to_string(sink_id)));

    MediaSDKCall(base::BindOnce(&ModelWidget::OnCreateAudioResultByVisual,
                                base::Unretained(this), visual_id, "", "",
                                plugin_name, true, sink_id, kAudioInputTypeFav),
                 &mediasdk::IsVisualHasAudio, visual_id.c_str());

    mediasdk::CreateCanvasItemParams canvas_item_params{};
    canvas_item_params.is_visible = true;
    canvas_item_params.transform.scale.x = 1.0f;
    canvas_item_params.transform.scale.y = 1.0f;

    std::string canvas_item_id = CreateCanvasItemOnVisual(
        visual_id, canvas_item_params, VisualDeviceInfo{});
    if (!canvas_item_id.empty()) {
      item->setData(STR_KEY_CANVAS_ITEM_ID,
                    QString::fromStdString(canvas_item_id));
    }

    canvas_map_data_[canvas_mgr_->CurrentCanvas()].push_back(
        ListWidgetItemData{item});
  }
}

void ModelWidget::OnCreateVisualResult(const std::string& visual_id,
                                       const VisualDeviceInfo& visual_info,
                                       uint32_t sink_id,
                                       bool* result) {
  static int s_canvas_item_cnt = 0;

  if (result && *result) {
    s_canvas_item_cnt++;

    visual_device_info_.insert(std::make_pair(visual_id, visual_info));

    QString item_name = QString::fromStdString(visual_info.plugin_name) + "_" +
                        QString::number(s_canvas_item_cnt);
    if (visual_info.plugin_name == rtc_visual_source::GetPluginName()) {
      item_name = QString("%1_%2_#%3")
                      .arg(visual_info.plugin_name.c_str())
                      .arg(rtc_visual_info_[visual_id].user_id.c_str())
                      .arg(rtc_visual_info_[visual_id].stream_index);
    }
    QListWidgetItem* item =
        new QListWidgetItem(item_name, ui_.visualListWidget);
    auto aa = ui_.visualListWidget->count();
    item->setData(STR_KEY_VISUAL_ID, QString::fromStdString(visual_id));
    item->setData(STR_KEY_PLUGIN_NAME_ID,
                  QString::fromStdString(visual_info.plugin_name));
    item->setData(STR_KEY_DEVICE_ID,
                  QString::fromStdString(visual_info.device_id));
    item->setData(STR_KEY_SINK_ID,
                  QString::fromStdString(std::to_string(sink_id)));
    item->setData(STR_KEY_TYPE_ID,
                  QString::fromStdString(std::to_string(visual_info.type)));
    item->setData(STR_KEY_CUSTOM_DATA_ID,
                  QString::fromStdString(visual_info.filters_json));
    MediaSDKCall(
        base::BindOnce(&ModelWidget::OnCreateAudioResultByVisual,
                       base::Unretained(this), visual_id, visual_info.device_id,
                       visual_info.plugin_name, visual_info.plugin_name, true,
                       sink_id, KAudioInputTypeVisual),
        &mediasdk::IsVisualHasAudio, visual_id.c_str());

    mediasdk::CreateCanvasItemParams params{};
    params.is_visible = true;
    params.transform.scale.x = 1.0f;
    params.transform.scale.y = 1.0f;
    std::string canvas_item_id =
        CreateCanvasItemOnVisual(visual_id, params, visual_info);
    if (!canvas_item_id.empty()) {
      item->setData(STR_KEY_CANVAS_ITEM_ID,
                    QString::fromStdString(canvas_item_id));
    }

    canvas_map_data_[canvas_mgr_->CurrentCanvas()].push_back(
        ListWidgetItemData{item});
  }
}

void ModelWidget::OnCreateFilteredVisualResult(
    const std::string& visual_id,
    const VisualDeviceInfo& visual_info,
    uint32_t sink_id,
    const std::string& filter_id,
    const mediasdk::CreateVisualFilterParams& filter_params,
    bool* result) {
  static int s_visual_cnt = 0;

  if (result && *result) {
    s_visual_cnt++;

    visual_device_info_.insert(std::make_pair(visual_id, visual_info));

    QString item_name = QString::fromStdString(visual_info.plugin_name) + "_" +
                        QString::number(s_visual_cnt);
    if (visual_info.plugin_name == rtc_visual_source::GetPluginName()) {
      item_name = QString("%1_%2_#%3")
                      .arg(visual_info.plugin_name.c_str())
                      .arg(rtc_visual_info_[visual_id].user_id.c_str())
                      .arg(rtc_visual_info_[visual_id].stream_index);
    }
    QListWidgetItem* item =
        new QListWidgetItem(item_name, ui_.visualListWidget);
    item->setData(STR_KEY_VISUAL_ID, QString::fromStdString(visual_id));
    item->setData(STR_KEY_PLUGIN_NAME_ID,
                  QString::fromStdString(visual_info.plugin_name));
    item->setData(STR_KEY_DEVICE_ID,
                  QString::fromStdString(visual_info.device_id));
    item->setData(STR_KEY_SINK_ID,
                  QString::fromStdString(std::to_string(sink_id)));
    item->setData(STR_KEY_TYPE_ID,
                  QString::fromStdString(std::to_string(visual_info.type)));
    item->setData(STR_KEY_CUSTOM_DATA_ID,
                  QString::fromStdString(visual_info.filters_json));
    MediaSDKCall(
        base::BindOnce(&ModelWidget::OnCreateAudioResultByVisual,
                       base::Unretained(this), visual_id, visual_info.device_id,
                       visual_info.plugin_name, visual_info.plugin_name, true,
                       sink_id, KAudioInputTypeVisual),
        &mediasdk::IsVisualHasAudio, visual_id.c_str());

    mediasdk::CreateCanvasItemParams params{};
    params.is_visible = true;
    params.transform.scale.x = 1.0f;
    params.transform.scale.y = 1.0f;
    std::string canvas_item_id = CreateCanvasItemOnVisualWithFilter(
        visual_id, filter_id, params, filter_params, visual_info);
    if (!canvas_item_id.empty()) {
      item->setData(STR_KEY_CANVAS_ITEM_ID,
                    QString::fromStdString(canvas_item_id));
    }

    canvas_map_data_[canvas_mgr_->CurrentCanvas()].push_back(
        ListWidgetItemData{item});
  }
}

void ModelWidget::OnCreateVisualResultDesktopCapture(
    const std::string& visual_id,
    const VisualDeviceInfo& visual_info,
    uint32_t sink_id,
    bool gdi_compatible,
    bool* result) {
  OnCreateVisualResult(visual_id, visual_info, sink_id, result);
  MediaSDKCall<bool>(base::BindOnce([](ModelWidget* self, bool* result) {},
                                    base::Unretained(this)),
                     &desktop_capture_visual_source::SetGdiCompatible,
                     visual_id.c_str(), gdi_compatible);
}

void ModelWidget::OnCreateVisualResultAudio(const std::string& visual_id,
                                            const VisualDeviceInfo& visual_info,
                                            uint32_t sink_id,
                                            int32_t monitor_type,
                                            bool* result) {
  OnCreateVisualResult(visual_id, visual_info, sink_id, result);
  if (monitor_type) {
    MediaSDKCall<bool>(base::BindOnce([](ModelWidget* self, bool* result) {},
                                      base::Unretained(this)),
                       &mediasdk::SetAudioMonitorType, visual_id.c_str(),
                       monitor_type);
  }
}

void ModelWidget::OnCreateAudioResult(const std::string& id,
                                      const std::string& device_id,
                                      const std::string& device_name,
                                      const std::string& plugin_name,
                                      bool from_video,
                                      uint32_t sink_id,
                                      AudioInputType type,
                                      bool* result) {
  if (result && *result) {
    std::string item_text = QListInputWidgetItem::GetFormatString(
        false, device_id, device_name, id);
    QListInputWidgetItem* item = new QListInputWidgetItem(item_text);
    item->SetCustomDeviceId(device_id);
    item->SetCustomDeviceName(device_name);
    item->SetCustomPluginName(plugin_name);
    item->SetCustomSinkID(sink_id);
    item->SetCustomId(id);
    item->SetFromVideo(from_video);
    item->SetCustomType(type);
    ui_.audioListWidget->addItem(item);

    if (plugin_name == "PCMAudioSource") {
      // PCMAudioSource should set monitor type to kAudioMonitorRender
      // automatically
      MediaSDKCall<bool>(base::BindOnce([](ModelWidget* self, bool* result) {},
                                        base::Unretained(this)),
                         &mediasdk::SetAudioMonitorType, id.c_str(),
                         mediasdk::kAudioMonitorRender);
    }
  }
}

void ModelWidget::OnCreateAudioResultByVisual(
    const std::string& id,
    const std::string& device_id,
    const std::string& device_name,
    const std::string& plugin_name,
    bool from_video,
    uint32_t sink_id,
    AudioInputType type,
    mediasdk::ResultBoolBool* result) {
  if (result && result->success && result->value) {
    bool ret = true;
    OnCreateAudioResult(id, device_id, device_name, plugin_name, from_video,
                        sink_id, type, &ret);
  }
}

void ModelWidget::OnImageVisualSourceInputWidgetClick(
    const std::string& plugin_name,
    const std::string& image_path) {
  std::string visual_id =
      QUuid::createUuid().toString().toStdString().substr(0, 4);
  VisualDeviceInfo info = {};
  info.plugin_name = plugin_name;
  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCreateVisualResult, base::Unretained(this),
                     visual_id, info, sink_id_),
      &image_visual_source::Create, visual_id.c_str(), image_path);
}

void ModelWidget::OnBrowserVisualInputWidgetInputItemClick(
    const std::string& plugin_name,
    const std::string& token,
    const std::string& dll_path,
    int32_t device_id) {
  std::string visual_id =
      QUuid::createUuid().toString().toStdString().substr(0, 4);
  VisualDeviceInfo info = {};
  info.plugin_name = plugin_name;
  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCreateVisualResult, base::Unretained(this),
                     visual_id, info, sink_id_),
      &browser_visual_source::Create, visual_id, token, dll_path, device_id,
      sink_id_);
}

void ModelWidget::OnByteLinkVisualInputWidgetItemClick(
    const std::string& plugin_name,
    const bytelink_visual_source::CastmateSDKParams& param) {
  std::string visual_id = QUuid::createUuid().toString().toStdString();
  VisualDeviceInfo info = {};
  info.plugin_name = plugin_name;
  info.is_bytelink = true;
  if (bytelink_visual_input_ui_) {
    bytelink_visual_input_ui_->SetVisualID(visual_id);
  }
  MediaSDKCall(base::BindOnce(&ModelWidget::OnCreateVisualResultAudio,
                              base::Unretained(this), visual_id, info, sink_id_,
                              mediasdk::kAudioMonitorRender),
               &bytelink_visual_source::Create, visual_id, param, sink_id_);
}

void ModelWidget::OnByteLinkVisualInputWidgetItemClose(const std::string& id) {
  DestroyVisual(id, false);
}

void ModelWidget::OnGameVisualInputItemClick(
    const std::string& plugin_name,
    game_visual_source::GameCaptureParams params) {
  std::string visual_id = QUuid::createUuid().toString().toStdString();
  VisualDeviceInfo info = {};
  info.plugin_name = plugin_name;
  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCreateVisualResult, base::Unretained(this),
                     visual_id, info, sink_id_),
      &game_visual_source::Create, visual_id, params, sink_id_);
}

void ModelWidget::OnDestroyVisualResult(const std::string& id, bool* result) {
  if (result && *result) {
    auto iter = visual_device_info_.find(id);
    if (iter != visual_device_info_.end()) {
      visual_device_info_.erase(iter);
    }

    auto it = rtc_visual_info_.find(id);
    if (it != rtc_visual_info_.end()) {
      rtc_visual_info_.erase(it);
    }
  }
  OnDestroyAudioResult(id, result);
}

void ModelWidget::OnDestroyAudioResult(const std::string& id, bool* result) {
  if (result && *result) {
    for (int i = ui_.audioListWidget->count() - 1; i >= 0; --i) {
      QListInputWidgetItem* item =
          dynamic_cast<QListInputWidgetItem*>(ui_.audioListWidget->item(i));
      if (item && item->GetCustomId() == id) {
        delete ui_.audioListWidget->takeItem(i);
        break;
      }
    }
  }
  if (audio_input_property_ui_) {
    audio_input_property_ui_->hide();
  }
  if (audio_input_ui_) {
    audio_input_ui_->hide();
  }
}

void ModelWidget::OnAudioInputItemAddClick(QListWidgetItem* item) {
  QListInputWidgetItem* item_tmp = dynamic_cast<QListInputWidgetItem*>(item);
  if (item_tmp) {
    if (!audio_input_property_ui_) {
      audio_input_property_ui_ = new AudioInputPropertyWidget(window());
      QObject::connect(audio_input_property_ui_,
                       &AudioInputPropertyWidget::AudioAECRefItemClick, this,
                       &ModelWidget::ShowAudioAECRefWidget);
    }
    if (item_tmp->GetCustomPluginName() == "PCMAudioSource") {
      static int play_pcm_index = 0;
      std::string pcm_file_name(play_pcm_index % 2 == 0 ? "clap.pcm"
                                                        : "victory.pcm");
      QDir dir(QCoreApplication::applicationDirPath());
      QString pcm_file_path = dir.filePath(pcm_file_name.c_str());
      std::string file_path = pcm_file_path.toStdString();
      QFile pcm_file(pcm_file_path);
      if (!pcm_file.exists() || !pcm_file.open(QIODevice::ReadOnly)) {
        QMessageBox::critical(nullptr, "error", "Open PCM audio file failed!");
        return;
      }

      const QByteArray pcm_binary_datas = pcm_file.readAll();
      std::vector<uint8_t> pcm_binary_data_vec;
      pcm_binary_data_vec.reserve(pcm_binary_datas.size());
      for (int index = 0; index < pcm_binary_datas.length(); index++) {
        pcm_binary_data_vec.push_back(
            (unsigned char)pcm_binary_datas.at(index));
      }
      pcm_file.close();

      std::string_view pcm_binary_data_sv(
          reinterpret_cast<char*>(pcm_binary_data_vec.data()),
          pcm_binary_data_vec.size());
      nlohmann::json pcm_audio_data_json;
      pcm_audio_data_json["left_channel_datas"] =
          base64::to_base64(pcm_binary_data_sv);
      pcm_audio_data_json["right_channel_datas"] =
          base64::to_base64(pcm_binary_data_sv);
      nlohmann::json json_root;
      json_root["update_pcm_play_datas"] = pcm_audio_data_json;

      MediaSDKCall(base::BindOnce([](bool* v) {}),
                   &mediasdk::UpdatePCMAudioDatas,
                   item_tmp->GetCustomId().c_str(), json_root.dump().c_str());
      play_pcm_index++;
    } else {
      // return and not show audio input property widge when device has been
      // removed
      if (item_tmp->text().startsWith("DEVICE_REMOVED:")) {
        QMessageBox::critical(nullptr, "error",
                              "This audio device has been removed!!");
        return;
      }
      audio_input_property_ui_->UpdateUI(
          item_tmp->GetCustomDeviceId(), item_tmp->GetCustomId(),
          item_tmp->GetCustomPluginName(), item_tmp->GetCustomDeviceName(),
          item_tmp);
    }
  }
}

void ModelWidget::CloseOtherWindow() {
  if (audio_input_ui_) {
    audio_input_ui_->close();
    audio_input_ui_->deleteLater();
    audio_input_ui_ = nullptr;
  }
  if (audio_input_property_ui_) {
    audio_input_property_ui_->close();
    audio_input_property_ui_->deleteLater();
    audio_input_property_ui_ = nullptr;
  }
  if (dshow_input_ui_) {
    dshow_input_ui_->close();
    dshow_input_ui_->deleteLater();
    dshow_input_ui_ = nullptr;
  }
  if (desktop_capture_input_ui_) {
    desktop_capture_input_ui_->close();
    desktop_capture_input_ui_->deleteLater();
    desktop_capture_input_ui_ = nullptr;
  }
  if (image_visual_input_ui_) {
    image_visual_input_ui_->close();
    image_visual_input_ui_->deleteLater();
    image_visual_input_ui_ = nullptr;
  }
  if (browser_visual_input_ui_) {
    browser_visual_input_ui_->close();
    browser_visual_input_ui_->deleteLater();
    browser_visual_input_ui_ = nullptr;
  }
  if (model_video_params_ui_) {
    model_video_params_ui_->close();
    model_video_params_ui_->deleteLater();
    model_video_params_ui_ = nullptr;
  }
  if (bytelink_visual_input_ui_) {
    bytelink_visual_input_ui_->close();
    bytelink_visual_input_ui_->deleteLater();
    bytelink_visual_input_ui_ = nullptr;
  }
  if (game_visual_params_ui_) {
    game_visual_params_ui_->close();
    game_visual_params_ui_->deleteLater();
    game_visual_params_ui_ = nullptr;
  }
  if (audio_aec_ref_widget_) {
    audio_aec_ref_widget_->close();
    audio_aec_ref_widget_->deleteLater();
    audio_aec_ref_widget_ = nullptr;
  }
}

std::string ModelWidget::CurrentFavVisualId() {
  static int32_t cnt = 0;
  return std::string("fav ") + std::to_string(cnt++);
}

void ModelWidget::ShowFAVWidget() {
  if (!fav_input_ui_) {
    fav_input_ui_ = new FAVInputWidget(window());
    QObject::connect(fav_input_ui_, &FAVInputWidget::FAVInputItemClick, this,
                     &ModelWidget::OnFAVInputItemCreate);

    QObject::connect(fav_input_ui_, &FAVInputWidget::FAVReopenClick, this,
                     &ModelWidget::OnFAVReopen);

    QObject::connect(fav_input_ui_, &FAVInputWidget::FAVInputPause, this,
                     &ModelWidget::OnVisualPause);

    QObject::connect(fav_input_ui_, &FAVInputWidget::FAVInputSignalSeek, this,
                     &ModelWidget::OnFAVSeek);
  }
  fav_input_ui_->UpdateUI(CurrentFavVisualId(), sink_id_);
}

void ModelWidget::OnDumpCurrentVisualInfo(mediasdk::MediaSDKString* id) {
  if (!id) {
    return;
  }

  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnGetTransform, base::Unretained(this)),
      &mediasdk::GetCanvasItemTransform, id->data());

  float fps = 0;
  if (mediasdk::GetVisualFPS(id->data(), fps)) {
    OnGetFps(fps);
  }
}

void ModelWidget::DestroyVisual(const std::string& id, bool is_duplicate) {
  if (!id.empty()) {
    if (!is_duplicate) {
      MediaSDKCall(base::BindOnce(&ModelWidget::OnDestroyVisualResult,
                                  base::Unretained(this), id),
                   &mediasdk::DestroyVisual, id.c_str());
    } else {
    }
  }
}

void ModelWidget::UpdateVideoParamsPushButton(bool enable) {
  ui_.videoParamsPushButton->setEnabled(enable);
}

void ModelWidget::UpdateSharedAppAudioItem(const nlohmann::json& json_root) {
  for (int qlist_item_index = 0;
       qlist_item_index < ui_.audioListWidget->count(); ++qlist_item_index) {
    QListInputWidgetItem* item =
        (QListInputWidgetItem*)ui_.audioListWidget->item(qlist_item_index);
    if (AudioInputType::kAudioInputTypeApp != item->GetCustomType()) {
      continue;
    }
    bool process_exists = false;
    for (const auto& element : json_root) {
      if (element["process_id"] == item->GetCustomDeviceId()) {
        process_exists = true;
        break;  // continue when shared app audio item exists
      }
    }

    // deal with UI
    if (!process_exists) {
      item->setTextColor(Qt::red);
    } else {
      item->setTextColor(Qt::black);
    }
  }
}

void ModelWidget::OnUpdateStreamStatistic(const std::string& info) {
  if (info.size() > 0) {
    ui_.stream_info->setText(info.c_str());
  }
}

void ModelWidget::OnUpdateEncoderStatistic(const std::string& info) {
  if (info.size() > 0) {
    ui_.encoder_info->setText(info.c_str());
  }
}

void ModelWidget::OnGetTransform(mediasdk::ResultBoolMSTransform* transform) {
  if (transform && transform->success) {
    visual_transform_ = transform->value;
  }
}

void ModelWidget::OnGetFps(float fps) {
  visual_fps_ = fps;
}

std::string ModelWidget::CreateCanvasItemOnVisual(
    const std::string& visual_id,
    const mediasdk::CreateCanvasItemParams& params,
    const VisualDeviceInfo& visual_info) {
  if (canvas_mgr_) {
    std::string id = QUuid::createUuid().toString().toStdString().substr(0, 4);
    canvas_mgr_->CreateCanvasItem(canvas_mgr_->CurrentCanvas(), id, visual_id,
                                  params, visual_info);
    return id;
  }

  return std::string{};
}

std::string ModelWidget::CreateCanvasItemOnVisualToCopyCanvas(
    const std::string& visual_id,
    const mediasdk::CreateCanvasItemParams& params,
    const VisualDeviceInfo& visual_info) {
  if (canvas_mgr_) {
    std::string id = QUuid::createUuid().toString().toStdString().substr(0, 4);
    canvas_mgr_->CreateCanvasItem(canvas_mgr_->GetCopyCanvas(), id, visual_id,
                                  params, visual_info);
    return id;
  }

  return std::string{};
}

std::string ModelWidget::CreateCanvasItemOnVisualWithFilter(
    const std::string& visual_id,
    const std::string& filter_id,
    const mediasdk::CreateCanvasItemParams& params,
    const mediasdk::CreateVisualFilterParams& filter_params,
    const VisualDeviceInfo& visual_info) {
  if (canvas_mgr_) {
    std::string id = QUuid::createUuid().toString().toStdString().substr(0, 4);
    canvas_mgr_->CreateCanvasItemWithFilter(canvas_mgr_->CurrentCanvas(), id,
                                            visual_id, filter_id, params,
                                            filter_params, visual_info);
    return id;
  }

  return std::string{};
}

void ModelWidget::on_addButtonCanvasList_clicked() {
  current_canvas_ = CreateCanvas(true);
}

void ModelWidget::on_delButtonCanvasList_clicked() {
  mediasdk::MediaSDKString result = MediaSDKSyncCall<mediasdk::MediaSDKString>(
      mediasdk::GetCurrentCanvas, sink_id_);

  std::string canvas_id = result.ToString();

  if (canvas_mgr_) {
    canvas_mgr_->DestroyCanvas(canvas_id);
  }
}

void ModelWidget::ReopenDShowInput(const VisualDeviceInfo& info,
                                   const std::string& visual_id) {
  OnDShowEnumInputs(info.plugin_name, visual_id, info.type);
}

void ModelWidget::
    OnDShowEnumInputs(const std::string& plugin_name, const std::string& visual_id, int32_t capture_type, int32_t enum_method /* dshow_visual_source::kCameraEnumMethodCameraAndFormats */) {
  MediaSDKCall(
      base::BindOnce(
          [](ModelWidget* self, const std::string& title,
             const std::string& visual_id, int32_t capture_type,
             mediasdk::MediaSDKString* result) {
            if (result) {
              self->ShowDShowInputWidget(title, result->ToString(),
                                         capture_type, visual_id);
            }
          },
          base::Unretained(this), plugin_name, visual_id, capture_type),
      &dshow_visual_source::EnumVisualInput,
      (dshow_visual_source::CaptureType)capture_type,
      (dshow_visual_source::CameraEnumMethod)enum_method);
}

void ModelWidget::OnDesktopCaptureEnumInputs(const std::string& plugin_name) {
  auto method = desktop_capture_visual_source::kEnumMethodWindows;
  std::vector<std::string> ingore_exe;
  ingore_exe.push_back("TikTok LIVE Studio.exe");
  MediaSDKCall(
      base::BindOnce(
          [](ModelWidget* self, const std::string& title,
             desktop_capture_visual_source::EnumMethod method,
             mediasdk::MediaSDKString* result) {
            if (result) {
              self->ShowDesktopCaptureWidget(title, result->ToString(), method);
            }
          },
          base::Unretained(this), plugin_name, method),
      &desktop_capture_visual_source::EnumWindowsInput, ingore_exe, false,
      true);
}

void ModelWidget::OnGameEnumInputs(const std::string& plugin_name) {
  std::vector<std::string> ingore_exe;
  ingore_exe.push_back("TikTok LIVE Studio.exe");
  MediaSDKCall(base::BindOnce(
                   [](ModelWidget* self, const std::string& title,
                      mediasdk::MediaSDKString* result) {
                     if (result) {
                       self->ShowGameVisualWidget(title, result->ToString());
                     }
                   },
                   base::Unretained(this), plugin_name),
               &game_visual_source::EnumGameInput, ingore_exe, true, true);
}

void ModelWidget::on_addAudioSource_clicked() {
  MediaSDKCall<mediasdk::PluginInfoArray>(
      base::BindOnce(
          [](ModelWidget* self, mediasdk::PluginInfoArray* result) {
            if (!result) {
              return;
            }
            std::vector<std::string> source_names;
            auto infos = result->ToVector();

            for (uint32_t i = 0; i < infos.size(); ++i) {
              source_names.push_back(infos[i].name.ToString());
            }
            self->ShowAudioInputSourceMenu(source_names);
          },
          base::Unretained(this)),
      &mediasdk::EnumAudioInputSource);
}

void ModelWidget::on_delAudioSource_clicked() {
  QList<QListWidgetItem*> selectedItems = ui_.audioListWidget->selectedItems();
  for (QListWidgetItem* item : selectedItems) {
    QListInputWidgetItem* item_tmp = dynamic_cast<QListInputWidgetItem*>(item);
    if (item_tmp && !item_tmp->FromVideo()) {
      std::string id = item_tmp->GetCustomId();
      MediaSDKCall(
          base::BindOnce(
              [](ModelWidget* self, const std::string& id, bool* result) {
                self->OnDestroyAudioResult(id, result);
              },
              base::Unretained(this), id),
          &mediasdk::DestroyAudioInput, id.c_str());
    }
  }
}

void ModelWidget::on_settingsButton_clicked() {
  SettingsDialog settings_dialog(this, sink_id_);
  settings_dialog.exec();
}

void ModelWidget::on_startStreamButton_clicked() {
  ui_.startStreamButton->SwitchState();

  if (LoadingButton::kFront == ui_.startStreamButton->GetState()) {
    mediasdk::StartPushStream(
        Config::GetInstance().GetPushStreamContext(sink_id_));
    auto stream_config = Config::GetInstance().GetStreamConfig(sink_id_);
    mediasdk::StartStream(stream_config, this);
  } else {
    auto stream_config = Config::GetInstance().GetStreamConfig(sink_id_);
    mediasdk::StopStream(current_push_stream_id_.c_str());
    current_push_stream_id_ = "";
  }
}

void ModelWidget::on_videoParamsPushButton_clicked() {
  if (!model_video_params_ui_) {
    model_video_params_ui_ = new ModelVideoParamsWidget(window());
  }
  model_video_params_ui_->UpdateUI(sink_id_);
}

void ModelWidget::on_AudioRenderSettings_clicked() {
  if (!audio_render_params_widget_ui_) {
    audio_render_params_widget_ui_ = new AudioRenderParamsWidget(window());
  }
  audio_render_params_widget_ui_->UpdateUI();
}

void ModelWidget::on_startLinkMicButton_clicked() {
  ui_.startLinkMicButton->SwitchState();

  if (LoadingButton::kFront == ui_.startLinkMicButton->GetState()) {
    // read init_config.json
    QString file_path = QDir(QCoreApplication::applicationDirPath())
                            .filePath("init_config.json");
    std::string log_sdk_websocket_url =
        "wss://rtc-log-report.bytedance.com/report";
    std::vector<std::string> access_hosts = {"rtcqa-va-test.bytevcloud.com",
                                             "rtcqa-sg-test.bytevcloud.com",
                                             "rtcqa-fr-test.bytevcloud.com"};
    std::string app_id = "64e307f2e5233f0120790af9";
    std::string ab_label = "";
    if (QFile::exists(file_path)) {
      QFile f(file_path);
      f.open(QIODevice::ReadOnly);
      QByteArray array = f.readAll();
      try {
        nlohmann::json json_root = nlohmann::json::parse(array.toStdString());
        // get config in lyrax_audio_config
        if (json_root.contains("lyrax_audio_config")) {
          nlohmann::json lyrax_audio_config = json_root["lyrax_audio_config"];
          if (lyrax_audio_config.contains("logSdkWebsocketUrl")) {
            log_sdk_websocket_url =
                lyrax_audio_config["logSdkWebsocketUrl"].get<std::string>();
          }
          if (lyrax_audio_config.contains("accessHost")) {
            // access host is json array
            nlohmann::json access_host = lyrax_audio_config["accessHost"];
            if (access_host.is_array()) {
              access_hosts.clear();
              for (auto& host : access_host) {
                access_hosts.push_back(host.get<std::string>());
              }
            }
          }
          if (lyrax_audio_config.contains("appId")) {
            app_id = lyrax_audio_config["appId"].get<std::string>();
          }
          // abLabel is string
          if (lyrax_audio_config.contains("abLabel")) {
            ab_label = lyrax_audio_config["abLabel"].get<std::string>();
          }
        }
      } catch (const std::exception& e) {
        LOG(ERROR) << "Failed to parse init_config.json: " << e.what();
      }
    }
    nlohmann::json engine_params;
    engine_params["rtc.log_sdk_websocket_url"] = log_sdk_websocket_url;
    engine_params["access_hosts"] = access_hosts;
    engine_params["abLabel"] = ab_label;

    MediaSDKSyncCall(mediasdk::RegisterRTCEventObserver, this);
    MediaSDKSyncCall(mediasdk::CreateEngine, app_id.c_str(),
                     engine_params.dump().c_str());
  } else {
    // remove mix frame observer from Mixer when leave room success
    MediaSDKSyncCall(mediasdk::LeaveRoom);
    MediaSDKSyncCall(mediasdk::DestroyEngine);
    MediaSDKSyncCall(mediasdk::UnregisterRTCEventObserver, this);
    ui_.startLinkMicButton->EndLoading(true);
    // remove all rtc visuals
    for (auto visual : rtc_visual_info_) {
      std::string visual_id = visual.first;
      MediaSDKCall(base::BindOnce(&ModelWidget::OnDestroyVisualResult,
                                  base::Unretained(this), visual_id),
                   &mediasdk::DestroyVisual, visual_id.c_str());
    }
  }
}

void ModelWidget::on_addButtonTransition_clicked() {
  auto current_transition = transition_manager_->GetCurrentTransition();
  if (!current_transition.empty()) {
    transition_manager_->DestroyTransition(current_transition);
  }
  const std::string current_transition_id = CreateTransition();

  ui_.canvasListWidget->SetCurrentTransition(
      QString::fromStdString(current_transition_id),
      ui_.transitionOptionWidget->currentText().toStdString());
}

void ModelWidget::on_modifyButtonTransition_clicked() {
  auto current_transition = transition_manager_->GetCurrentTransition();
  if (current_transition.empty()) {
    return;
  }
  transition_manager_->UpdateTransition(current_transition,
                                        transition_property_);
}

void ModelWidget::on_settingsButtonTransition_clicked() {
  QDialog* settings_dialog = new QDialog(this);
  settings_dialog->setWindowTitle("Transition Settings");

  QVBoxLayout* dialog_layout = new QVBoxLayout(settings_dialog);
  QHBoxLayout* duration_layout = new QHBoxLayout();

  // common property
  duration_layout->addWidget(new QLabel("duration(ms):", settings_dialog));
  QSpinBox* duration_ms_spin_box = new QSpinBox(settings_dialog);
  duration_ms_spin_box->setRange(0, 10000);
  duration_ms_spin_box->setValue(3000);
  duration_layout->addWidget(duration_ms_spin_box);
  dialog_layout->addLayout(duration_layout);

  QString current_text = ui_.transitionOptionWidget->currentText();

  QComboBox* function_type_combo_box = nullptr;
  QComboBox* direction_combo_box = nullptr;
  QComboBox* swipe_type_combo_box = nullptr;
  QSpinBox* middle_progress_spin_box = nullptr;
  QLineEdit* file_path_line_edit = nullptr;
  QLineEdit* color_value_line_edit = nullptr;
  QCheckBox* invert_check_box = nullptr;
  QDoubleSpinBox* softness_spin_box = nullptr;
  ActionSettingWidget* action_setting_widget = nullptr;

  if (current_text == "Slide" || current_text == "Swipe") {
    QHBoxLayout* direction_layout = new QHBoxLayout();
    direction_layout->addWidget(new QLabel("direction:", settings_dialog));
    direction_combo_box = new QComboBox(settings_dialog);
    direction_combo_box->addItems({"up", "down", "left", "right"});
    direction_layout->addWidget(direction_combo_box);
    dialog_layout->addLayout(direction_layout);
    QHBoxLayout* swipe_type_layout = new QHBoxLayout();
    swipe_type_layout->addWidget(new QLabel("swipe_type:", settings_dialog));
    swipe_type_combo_box = new QComboBox(settings_dialog);
    swipe_type_combo_box->addItems({"swipe_in", "swipe_out"});
    swipe_type_layout->addWidget(swipe_type_combo_box);
    dialog_layout->addLayout(swipe_type_layout);

    QHBoxLayout* function_type_layout = new QHBoxLayout();
    function_type_layout->addWidget(new QLabel("function:", settings_dialog));
    function_type_combo_box = new QComboBox(settings_dialog);
    function_type_combo_box->addItems({"linear", "sine", "quad", "cubic"});
    function_type_layout->addWidget(function_type_combo_box);
    dialog_layout->addLayout(function_type_layout);
  } else if (current_text == "Cartoon") {
    QHBoxLayout* middle_progress_layout = new QHBoxLayout();
    middle_progress_layout->addWidget(
        new QLabel("transition time(%):", settings_dialog));
    middle_progress_spin_box = new QSpinBox(settings_dialog);
    middle_progress_spin_box->setRange(0, 100);
    middle_progress_spin_box->setValue(50);
    middle_progress_layout->addWidget(middle_progress_spin_box);
    dialog_layout->addLayout(middle_progress_layout);

    QHBoxLayout* file_path_layout = new QHBoxLayout();
    file_path_layout->addWidget(new QLabel("file:", settings_dialog));
    file_path_line_edit = new QLineEdit(settings_dialog);
    QPushButton* browse_button = new QPushButton("Browse", settings_dialog);
    file_path_layout->addWidget(file_path_line_edit);
    file_path_layout->addWidget(browse_button);
    dialog_layout->addLayout(file_path_layout);

    connect(browse_button, &QPushButton::clicked, [file_path_line_edit]() {
      QString file_path = QFileDialog::getOpenFileName(nullptr, "Choose a file",
                                                       "", "All Files (*)");
      if (!file_path.isEmpty()) {
        file_path_line_edit->setText(file_path);
      }
    });
  } else if (current_text == "Fade2Color") {
    QHBoxLayout* middle_progress_layout = new QHBoxLayout();
    middle_progress_layout->addWidget(
        new QLabel("transition time(%):", settings_dialog));
    middle_progress_spin_box = new QSpinBox(settings_dialog);
    middle_progress_spin_box->setRange(0, 100);
    middle_progress_spin_box->setValue(50);
    middle_progress_layout->addWidget(middle_progress_spin_box);
    dialog_layout->addLayout(middle_progress_layout);

    QHBoxLayout* function_type_layout = new QHBoxLayout();
    function_type_layout->addWidget(new QLabel("function:", settings_dialog));
    function_type_combo_box = new QComboBox(settings_dialog);
    function_type_combo_box->addItems({"linear", "sine", "quad", "cubic"});
    function_type_layout->addWidget(function_type_combo_box);
    dialog_layout->addLayout(function_type_layout);

    QHBoxLayout* colorLayout = new QHBoxLayout();
    colorLayout->addWidget(new QLabel("Choose Color:", settings_dialog));
    QPushButton* colorButton = new QPushButton("Colors", settings_dialog);
    color_value_line_edit = new QLineEdit(settings_dialog);
    color_value_line_edit->setReadOnly(true);
    colorLayout->addWidget(color_value_line_edit);
    colorLayout->addWidget(colorButton);
    dialog_layout->addLayout(colorLayout);

    connect(colorButton, &QPushButton::clicked, [=]() {
      QColor color =
          QColorDialog::getColor(Qt::white, settings_dialog, "Choose Color");
      if (color.isValid()) {
        // 0xRRGGBB
        int rgb = (color.red() << 16) | (color.green() << 8) | color.blue();
        QString color_text =
            QString("0x%1").arg(rgb, 6, 16, QChar('0')).toUpper();
        color_value_line_edit->setText(color_text);
      }
    });
  } else if (current_text == "Luminance") {
    QHBoxLayout* invert_check_box_layout = new QHBoxLayout();
    invert_check_box = new QCheckBox("invert", settings_dialog);
    invert_check_box_layout->addWidget(invert_check_box);
    dialog_layout->addLayout(invert_check_box_layout);

    QHBoxLayout* softness_layout = new QHBoxLayout();
    softness_layout->addWidget(new QLabel("softness", settings_dialog));
    softness_spin_box = new QDoubleSpinBox(settings_dialog);
    softness_spin_box->setValue(0.35f);
    softness_layout->addWidget(softness_spin_box);
    dialog_layout->addLayout(softness_layout);

    QHBoxLayout* file_path_layout = new QHBoxLayout();
    file_path_layout->addWidget(new QLabel("file:", settings_dialog));
    file_path_line_edit = new QLineEdit(settings_dialog);
    QPushButton* browse_button = new QPushButton("Browse", settings_dialog);
    file_path_layout->addWidget(file_path_line_edit);
    file_path_layout->addWidget(browse_button);
    dialog_layout->addLayout(file_path_layout);

    connect(browse_button, &QPushButton::clicked, [file_path_line_edit]() {
      QString file_path = QFileDialog::getOpenFileName(nullptr, "Choose a file",
                                                       "", "All Files (*)");
      if (!file_path.isEmpty()) {
        file_path_line_edit->setText(file_path);
      }
    });
  } else if (current_text == "Action") {
    dialog_layout->takeAt(0);
    action_setting_widget = new ActionSettingWidget(settings_dialog);
    dialog_layout->addWidget(action_setting_widget);
  }

  QPushButton* confirm_button = new QPushButton("Confirm", settings_dialog);
  dialog_layout->addWidget(confirm_button);
  connect(confirm_button, &QPushButton::clicked, [=]() {
    nlohmann::json json_property;
    transition_duration_ms_ = duration_ms_spin_box->value();
    json_property["duration_ms"] = transition_duration_ms_;
    if ((current_text == "Slide" || current_text == "Swipe") &&
        direction_combo_box) {
      json_property["direction"] = DirectionString2Type(
          direction_combo_box->currentText().toStdString());
      json_property["swipe_type"] = SwipeTypeString2Type(
          swipe_type_combo_box->currentText().toStdString());
      json_property["function_type"] = FunctionString2Type(
          function_type_combo_box->currentText().toStdString());
    } else if (current_text == "Cartoon") {
      json_property["middle_progress"] =
          static_cast<float>(middle_progress_spin_box->value()) / 100.0f;
      json_property["file_path"] =
          file_path_line_edit->text().toUtf8().toStdString();
    } else if (current_text == "Fade2Color") {
      unsigned int color_value =
          std::stoul(color_value_line_edit->text().toStdString(), nullptr, 16);
      json_property["middle_progress"] =
          static_cast<float>(middle_progress_spin_box->value()) / 100.0f;
      json_property["color"] = color_value;
    } else if (current_text == "Luminance") {
      json_property["file_path"] = file_path_line_edit->text().toStdString();
      json_property["invert"] = invert_check_box->isChecked();
      json_property["softness"] =
          static_cast<float>(softness_spin_box->value());
    }
    if (current_text == "Action") {
      transition_property_ = action_setting_widget->GetProperty();
    } else {
      transition_property_ = json_property.dump();
    }

    settings_dialog->accept();
  });

  settings_dialog->adjustSize();
  settings_dialog->setLayout(dialog_layout);
  settings_dialog->exec();
}

void ModelWidget::on_startRecordButton_clicked() {
  ui_.startRecordButton->SwitchState();

  if (LoadingButton::kFront == ui_.startRecordButton->GetState()) {
    auto record_stream_config = Config::GetInstance().GetRecordConfig(
        sink_id_, BuildRecordFileNameUseCurrentTime());
    mediasdk::StartStream(record_stream_config, this);
  } else {
    mediasdk::StopStream(current_record_stream_id_.c_str());
  }
}

void ModelWidget::OnAudioInputItemClick(const std::string& plugin_name,
                                        const std::string& device_id,
                                        const std::string& device_name) {
  mediasdk::CreateAudioParams params;
  params.track_id = sink_id_;
  params.plugin_name = plugin_name;
  {
    nlohmann::json json_root;
    json_root["device_id"] = device_id;
    json_root["device_name"] = device_name;
    json_root["audio_input_type"] = mediasdk::kAudioInputMicrophone;
    params.json_params = json_root.dump();
  }

  std::string id = QUuid::createUuid().toString().toStdString().substr(0, 4);
  if (plugin_name == "LyraxAudioInput") {
    params.plugin_name = "WASAPIAudioSource";
    MediaSDKCall(
        base::BindOnce(&ModelWidget::OnCreateAudioResult,
                       base::Unretained(this), id, device_id, device_name,
                       plugin_name, false, sink_id_, kAudioInputTypeMicrophone),
        &mediasdk::CreateLyraxAudioInput, id.c_str(), params);
  } else {
    MediaSDKCall(
        base::BindOnce(&ModelWidget::OnCreateAudioResult,
                       base::Unretained(this), id, device_id, device_name,
                       plugin_name, false, sink_id_, kAudioInputTypeMicrophone),
        &mediasdk::CreateAudioInput, id.c_str(), params);
  }
}

void ModelWidget::OnAudioOutputItemClick(const std::string& plugin_name,
                                         const std::string& device_id,
                                         const std::string& device_name) {
  mediasdk::CreateAudioParams params;
  params.track_id = sink_id_;
  params.plugin_name = plugin_name;
  {
    nlohmann::json json_root;
    json_root["device_id"] = device_id;
    json_root["device_name"] = device_name;
    json_root["audio_input_type"] = mediasdk::kAudioInputLoopback;
    params.json_params = json_root.dump();
  }
  std::string id = QUuid::createUuid().toString().toStdString().substr(0, 4);
  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCreateAudioResult, base::Unretained(this),
                     id, device_id, device_name, plugin_name, false, sink_id_,
                     kAudioInputTypeLoopback),
      &mediasdk::CreateAudioInput, id.c_str(), params);
}

void ModelWidget::OnAudioAppItemClick(const std::string& plugin_name,
                                      uint32_t pid,
                                      const std::string& device_name) {
  // prompt user to select single or exclude mode
  bool use_exclude_mode = false;
  int select = QMessageBox::information(this, tr("APP Audio Model"),
                                        tr("Do You Want To Use Exclude Mode?"),
                                        QMessageBox::Yes, QMessageBox::No);
  if (select == QMessageBox::Yes) {
    use_exclude_mode = true;
  }

  mediasdk::CreateAudioParams params;
  params.track_id = sink_id_;
  params.plugin_name = plugin_name;
  {
    nlohmann::json json_root;
    json_root["pid"] = pid;
    json_root["exe_name"] = device_name.c_str();
    json_root["exclude_mode"] = use_exclude_mode;
    json_root["audio_input_type"] = mediasdk::kAudioInputAPP;
    params.json_params = json_root.dump();
  }
  std::string id = QUuid::createUuid().toString().toStdString().substr(0, 4);
  MediaSDKCall(
      base::BindOnce(&ModelWidget::OnCreateAudioResult, base::Unretained(this),
                     id, QString("%1").arg(pid).toStdString(), device_name,
                     plugin_name, false, sink_id_, kAudioInputTypeApp),
      &mediasdk::CreateAudioInput, id.c_str(), params);
}

void ModelWidget::OnVisualPause(const std::string& visual, bool pause) {
  if (pause) {
    MediaSDKCall<bool>(base::BindOnce([](bool* v) {}),
                       mediasdk::PauseVisualCapture, visual.c_str());
  } else {
    MediaSDKCall<bool>(base::BindOnce([](bool* v) {}),
                       mediasdk::ContinueVisualCapture, visual.c_str());
  }
}

void ModelWidget::OnFAVSeek(const std::string& visual, int32_t pos) {
  auto duration = MediaSDKSyncCall<mediasdk::MediaSDKString>(
      mediasdk::GetVisualInputProperty, visual.c_str(),
      fav_visual_source::kFAVVisualPropertyDuration);
  int64_t duration_seconds = std::atoll(duration.ToString().c_str());
  int64_t new_pos = int64_t((double)duration_seconds * (1.f / 100.f * pos));

  auto action = fav_visual_source::SeekAction({new_pos, true});
  bool suc = MediaSDKSyncCall<bool>(mediasdk::DoVisualInputAction,
                                    visual.c_str(), action.c_str());
}

void ModelWidget::OnStaticsTimerTimeout() {
  auto current_row = ui_.visualListWidget->GetCurrentItem();
  if (!current_row.isEmpty()) {
    MediaSDKCall(base::BindOnce(&ModelWidget::OnDumpCurrentVisualInfo,
                                base::Unretained(this)),
                 &mediasdk::GetCurrentCanvasItemOnVideoModel, sink_id_);
    std::stringstream oss;
    oss << "fps:" << base::StringPrintf("%.2f", visual_fps_)
        << " flip: " << (visual_transform_.flip_v ? "true" : "false") << " x "
        << (visual_transform_.flip_h ? "true" : "false")
        << " angle: " << base::StringPrintf("%.2f", visual_transform_.angle)
        << " scale: " << base::StringPrintf("%.2f", visual_transform_.scale.x)
        << " " << base::StringPrintf("%.2f", visual_transform_.scale.y)
        << " clip: " << base::StringPrintf("%.2f", visual_transform_.clip.x)
        << " " << base::StringPrintf("%.2f", visual_transform_.clip.y) << " "
        << base::StringPrintf("%.2f", visual_transform_.clip.z) << " "
        << visual_transform_.clip.w << " translate: "
        << base::StringPrintf("%.2f", visual_transform_.translate.x) << " "
        << base::StringPrintf("%.2f", visual_transform_.translate.y);
    ui_.labelVisualInfo->setText(oss.str().c_str());
  } else {
    ui_.labelVisualInfo->setText("");
  }

  if (!current_push_stream_id_.empty()) {
    MediaSDKCall(
        base::BindOnce(
            [](ModelWidget* self, mediasdk::StatisticInfo* result) {
              if (!result) {
                return;
              }
              std::ostringstream oss;
              oss << base::StringPrintf("send bitrate:  %4d",
                                        (int32_t)result->send_bitrate)
                  << base::StringPrintf(", video send fps:  %4d",
                                        (int32_t)result->video_send_fps)
                  << base::StringPrintf(", video total drops: %4d",
                                        (int32_t)result->total_drops);
              self->OnUpdateStreamStatistic(oss.str());

              oss << base::StringPrintf(" send audio bitrate:  %4d",
                                        (int32_t)result->audio_send_bitrate)
                  << base::StringPrintf(", send video bitrate:  %4d",
                                        (int32_t)result->video_send_bitrate)
                  << base::StringPrintf(", enc video bitrate: %4d",
                                        (int32_t)result->video_enc_bitrate)
                  << base::StringPrintf(", enc audio bitrate: %4d",
                                        (int32_t)result->audio_enc_bitrate);
              qDebug() << QString::fromStdString(oss.str());
            },
            base::Unretained(this)),
        &mediasdk::GetStatisticInfo, current_push_stream_id_.c_str());
  }
  if (!current_push_stream_id_.empty() || !current_record_stream_id_.empty()) {
    mediasdk::EncoderStatisticInfo info{};
    mediasdk::GetEncoderStatisticInfo(current_record_stream_id_.empty()
                                          ? current_push_stream_id_.c_str()
                                          : current_record_stream_id_.c_str(),
                                      info);
    std::ostringstream oss;
    oss << "[" << info.video_codec_name.ToString() << "] "
        << base::StringPrintf("video encode fps: %4d",
                              (int32_t)info.video_enc_fps);
    OnUpdateEncoderStatistic(oss.str());

    MediaSDKCall(base::BindOnce(
                     [](ModelWidget* self, mediasdk::EncoderTsInfo* result) {
                       if (!result) {
                         return;
                       }
                       qDebug() << "Encoder Pts: " << (int)result->audio_in_pts
                                << " " << (int)result->audio_out_pts << " "
                                << (int)result->audio_out_dts << " "
                                << (int)result->video_in_pts << " "
                                << (int)result->video_out_dts << " "
                                << (int)result->video_out_pts;
                     },
                     base::Unretained(this)),
                 &mediasdk::GetEncoderPtsByStreamId,
                 current_record_stream_id_.empty()
                     ? current_push_stream_id_.c_str()
                     : current_record_stream_id_.c_str());
  }
}

void ModelWidget::OnFAVReopen(const std::string& visual_id,
                              const std::string& fav) {
  MediaSDKCall(base::BindOnce([](bool*) {}), &mediasdk::ReopenVisual,
               visual_id.c_str(), fav.c_str());
}

void ModelWidget::OnFAVInputItemCreate(
    const std::string& visual_id,
    const std::string& plugin_name,
    const fav_visual_source::CreateParam& fav) {
  CreateFAVInput(visual_id, plugin_name, fav);
}

void ModelWidget::OnDShowVisualInputItemClick(
    const std::string& plugin_name,
    const std::string& visual_id,
    int32_t capture_type,
    const dshow_visual_source::CaptureParams& vparams,
    const dshow_visual_source::AudioCaptureParams& aparams) {
  DShowCreateVisual(plugin_name, visual_id, capture_type, vparams, aparams);
}

void ModelWidget::OnDShowVisualInputItemClickWithFilter(
    const std::string& plugin_name,
    const std::string& visual_id,
    int32_t capture_type,
    const dshow_visual_source::CaptureParams& vparams,
    const dshow_visual_source::AudioCaptureParams& aparams,
    const std::string& filter_name,
    const std::string& filter_id,
    const std::string& filter_params) {
  if (visual_id.empty()) {
    std::string visual_id_new = QUuid::createUuid().toString().toStdString();
    VisualDeviceInfo info = {};
    info.device_id = vparams.device_id;
    info.plugin_name = plugin_name;
    info.type = capture_type;
    if (capture_type == dshow_visual_source::kCaptureTypeCamera) {
      mediasdk::CreateVisualParams params = {};
      params.plugin_name = dshow_visual_source::GetPluginName();
      params.audio_track_id = 0;
      {
        nlohmann::json json_root;
        json_root["type"] = dshow_visual_source::kCaptureTypeCamera;
        json_root["video"] = dshow_visual_source::CaptureParamsToJson(vparams);
        params.json_params = json_root.dump();
      }
      mediasdk::CreateVisualFilterParams create_filter_params = {};
      create_filter_params.filter_name = filter_name;
      create_filter_params.json_params = filter_params;

      nlohmann::json filter_json;
      filter_json["added_visual_filters"] = nlohmann::json::array();
      filter_json["added_visual_filters"].push_back(
          {{"name", filter_name}, {"id", filter_id}});
      info.filters_json = filter_json.dump();

      MediaSDKCall(base::BindOnce(&ModelWidget::OnCreateFilteredVisualResult,
                                  base::Unretained(this), visual_id_new, info,
                                  sink_id_, filter_id, create_filter_params),
                   &dshow_visual_source::CreateCameraCapture,
                   visual_id_new.c_str(), vparams, sink_id_);

      Config::GetInstance().SetFilterInitParamConfig(filter_name,
                                                     filter_params);
    } else {
      MediaSDKCall(
          base::BindOnce(&ModelWidget::OnCreateVisualResultAudio,
                         base::Unretained(this), visual_id_new, info, sink_id_,
                         (aparams.render_type ==
                          dshow_visual_source::kAnalogRenderTypeNone)
                             ? mediasdk::kAudioMonitorRender
                             : 0),
          &dshow_visual_source::CreateAnalogCapture, visual_id_new.c_str(),
          vparams, aparams, sink_id_);
    }
  } else {
    MediaSDKCall(base::BindOnce([](ModelWidget* self, bool* result) {},
                                base::Unretained(this)),
                 &dshow_visual_source::Reopen, visual_id.c_str(), vparams,
                 aparams);
  }
}

void ModelWidget::OnDesktopCaptureVisualInputItemClick(
    const std::string& plugin_name,
    const std::string& device_id,
    int32_t method,
    uint64_t item_id,
    int32_t capture_method,
    bool show_cursor,
    bool gdi_compatible,
    desktop_capture_visual_source::WindowCaptureParams params) {
  CreateDesktopCaptureVisual(plugin_name, device_id, method, item_id,
                             capture_method, show_cursor, gdi_compatible,
                             params);
}

void ModelWidget::OnMainWindowMoved() {}

void ModelWidget::OnStartStreamResultSlot(const QString& id, int error_code) {
  bool success = (error_code == mediasdk::kStreamSuccess);
  if (kRecordStreamId == id.toStdString()) {
    ui_.startRecordButton->EndLoading(success ? true : false);
  } else {
    ui_.startStreamButton->EndLoading(success ? true : false);
  }

  if (!success) {
    QString text = QString("[OnStartStreamResult] %1 error_code %2")
                       .arg(id)
                       .arg(error_code);

    QMessageBox::critical(nullptr, "error", text);
  } else {
    if (kRecordStreamId == id.toStdString()) {
      current_record_stream_id_ = id.toStdString();
    } else {
      current_push_stream_id_ = id.toStdString();
    }
  }
}

void ModelWidget::OnStreamStoppedSlot(const QString& id, int error_code) {
  if (error_code == mediasdk::StreamErrorCode::kStreamSuccess) {
    OnStreamStoppedManuallySuccess(id);
  } else {
    OnStreamStoppedWithFatalError(id, error_code);
  }
  UpdateVideoParamsPushButton(true);
}

void ModelWidget::OnStreamStoppedManuallySuccess(const QString& id) {
  if (kRecordStreamId == id.toStdString()) {
    ui_.startRecordButton->EndLoading(true);
  } else {
    ui_.startStreamButton->EndLoading(true);
  }
}

void ModelWidget::OnStreamStoppedWithFatalError(const QString& id,
                                                int error_code) {
  ui_.startStreamButton->SwitchStateDirectly();

  QString text =
      QString("Stream stopped with fatal error: ID = %1, Error Code = %2")
          .arg(id)
          .arg(error_code);
  QMessageBox::critical(nullptr, "error", text);
}

void ModelWidget::OnVisualMenuSlot(const VisualMenuSignalStruct& message) {
  /*QMessageBox::critical(nullptr, "error",
                        QString::fromStdString(message.json_param));*/
  if (message.signal_type == CREATE_VISUAL) {
    nlohmann::json json_root;
    std::string visual_id = "";
    std::string visual_type = "";
    int sink_id = sink_id_;
    try {
      json_root = nlohmann::json::parse(message.json_param);
      visual_type = json_root["visual_type"];
      visual_id = json_root["visual_id"];
      sink_id = json_root["sink_id"];
    } catch (...) {
    }
    if (sink_id == sink_id_) {
      if (!visual_id.empty()) {
        VisualDeviceInfo info;
        info.device_id = "";
        info.plugin_name = visual_type;
        bool result;
        OnCreateVisualResult(visual_id, info, sink_id, &result);
      }
    } else {
      emit SendSignalToParent(message);
    }
  }
}

VisualSourceType ModelWidget::GetDragFileVisualSourceType(
    const std::string& file_path) {
  QMimeDatabase mime_db;
  QMimeType mime_type = mime_db.mimeTypeForFile(QString(file_path.c_str()));
  if (mime_type.name().startsWith("image/")) {
    return kVisualSourceTypeImage;
  } else if (mime_type.name().startsWith("video/")) {
    return kVisualSourceTypeFav;
  } else {
    return kVisualSourceTypeDefault;
  }
}

void ModelWidget::OnRTCEvent(int event_type,
                             int param,
                             const QString& event_info) {
  switch (event_type) {
    case mediasdk::kRTCNotifyEventEngineStart: {
      ui_.startLinkMicButton->EndLoading(!param);
      if (param == 0) {
        ShowLinkMicDialog();
      } else {
        QString text =
            QString("Create RTC engine failed, error[%1]").arg(param);
        QMessageBox::critical(nullptr, "LinkMic", text);
      }
    } break;
    case mediasdk::kRTCNotifyEventEngineStop: {
    } break;
    case mediasdk::kRTCNotifyEventConnectionStateChanged: {
      int state = param;
    } break;
    case mediasdk::kRTCNotifyEventJoinChannel: {
      nlohmann::json json_root;
      if (param == 0) {
        MediaSDKSyncCall(mediasdk::EnableLocalAudio,
                         mediasdk::StreamIndex::kStreamIndexMain, true, 0);

        MediaSDKSyncCall(mediasdk::EnableLocalVideo,
                         mediasdk::StreamIndex::kStreamIndexMain, true,
                         sink_id_);
        MediaSDKSyncCall(mediasdk::PublishStream, mediasdk::kStreamIndexMain,
                         mediasdk::kMediaStreamTypeBoth);

        json_root["video_encoder_configs"] = nlohmann::json::array();

        nlohmann::json config;
        json_root["frame_rate"] = 15;
        json_root["max_bitrate"] = -1;
        json_root["min_bitrate"] = 0;
        json_root["enable_simulcast"] = false;
        MediaSDKSyncCall(mediasdk::UpdateCropAndScale,
                         mediasdk::kStreamIndexMain, json_root.dump().c_str());
      } else {
        QString text = QString("Join RTC channel failed, error[%1]").arg(param);
        QMessageBox::critical(nullptr, "LinkMic", text);
      }
    } break;
    case mediasdk::kRTCNotifyEventUserJoined:
      break;
    case mediasdk::kRTCNotifyEventUserLeave:
      break;
    case mediasdk::kRTCNotifyEventFirstRemoteVideoFrameDecoded: {
      nlohmann::json json_root;
      RTCVisualInfo info;
      int stream_index;
      try {
        json_root = nlohmann::json::parse(event_info.toStdString());
        if (!json_root.empty()) {
          info.room_id = json_root["room_id"];
          info.user_id = json_root["user_id"];
          info.stream_index = json_root["stream_index"];
        }
      } catch (...) {
      }

      std::string visual_id;
      for (auto visual : rtc_visual_info_) {
        if (visual.second.room_id == info.room_id &&
            visual.second.user_id == info.user_id &&
            visual.second.stream_index == info.stream_index) {
          visual_id = visual.first;
          break;
        }
      }

      if (!visual_id.empty()) {
        MediaSDKCall(base::BindOnce(&ModelWidget::OnDestroyVisualResult,
                                    base::Unretained(this), visual_id),
                     &mediasdk::DestroyVisual, visual_id.c_str());
      }
      visual_id = CreateVisual(rtc_visual_source::GetPluginName().data(),
                               event_info.toStdString());
      rtc_visual_info_[visual_id] = info;
    } break;
    default:
      break;
  }
}

void ModelWidget::OnShowContextMenu(const QPoint& pos) {
  bool show_visual_menu = ui_.visualListWidget->ShowContextMenu(pos);
  if (!show_visual_menu) {
    QRect rect = ui_.previewRegion->geometry();
    QMainWindow* mainWindow = dynamic_cast<QMainWindow*>(this->window());
    QPoint rect_pos = ui_.previewRegion->mapTo(mainWindow, rect.topLeft());
    if (fix_width_ > 0 && fix_height_ > 0) {
      rect.setWidth(fix_width_);
      rect.setHeight(fix_height_);
    }
    rect.moveTopLeft(rect_pos);
    rect = CalcRatioFill(rect, kTargetRatio);

    ModelMenu menu(this, sink_id_, rect);
    menu.exec(QCursor::pos());
  }
}

std::string ModelWidget::BuildRecordFileNameUseCurrentTime() {
  base::Time now_time = base::Time::Now();
  base::Time::Exploded exploded_tim;
  now_time.LocalExplode(&exploded_tim);

  std::string file_name =
      std::string("record_file_") + std::to_string(exploded_tim.year) +
      std::to_string(exploded_tim.month) +
      std::to_string(exploded_tim.day_of_month) +
      std::to_string(exploded_tim.hour) + std::to_string(exploded_tim.minute) +
      std::to_string(exploded_tim.second);

  return file_name;
}

void ModelWidget::on_addButton_clicked() {
  MediaSDKCall<mediasdk::PluginInfoArray>(
      base::BindOnce(
          [](ModelWidget* self, mediasdk::PluginInfoArray* result) {
            std::vector<std::string> source_names;
            auto infos = result->ToVector();
            for (uint32_t i = 0; i < infos.size(); ++i) {
              source_names.push_back(infos[i].name.ToString());
            }
            self->ShowVisualSourceMenu(source_names);
          },
          base::Unretained(this)),
      &mediasdk::EnumVisualSource);
}

void ModelWidget::on_delButton_clicked() {
  mediasdk::MediaSDKString result = MediaSDKSyncCall<mediasdk::MediaSDKString>(
      mediasdk::GetCurrentCanvasItemOnVideoModel, sink_id_);

  std::string canvas_item_id = result.ToString();

  // result = MediaSDKSyncCall<mediasdk::MediaSDKString>(
  //     mediasdk::GetVisualFromCanvasItem, canvas_item_id.c_str());
  // DestroyVisual(result.ToString(), false);

  canvas_mgr_->DestroyCanvasItem(canvas_item_id);

  MediaSDKSyncCall<bool>(mediasdk::SetCurrentCanvasItem,
                         canvas_mgr_->CurrentCanvas().c_str(),
                         canvas_item_id.c_str());
}

void ModelWidget::ShowVisualMenu(const std::string& canvas_item_id,
                                 const std::string& canvas_item_name,
                                 const std::string& visual_id) {
  VisualDeviceInfo info;
  auto iter = visual_device_info_.find(visual_id);
  if (iter != visual_device_info_.end()) {
    info = iter->second;
  }

  VisualMenu menu(this, this, sink_id_, canvas_item_id, canvas_item_name,
                  visual_id, info, info.filters_json);
  connect(&menu, &VisualMenu::SendSignalToParent, this,
          &ModelWidget::OnVisualMenuSlot);
  menu.exec(QCursor::pos());
  if (iter != visual_device_info_.end()) {
    iter->second.filters_json = menu.GetSettings();
  }
  if (menu.IsNeedOpenDShowInput()) {
    ReopenDShowInput(info, visual_id);
  }
}
std::map<std::string, std::string> ModelWidget::GetComparedCanvasItems(
    const std::string& from_canvas,
    const std::string& to_canvas,
    const std::string& compare_type) {
  auto source_items_it = canvas_map_data_.find(from_canvas);
  auto target_items_it = canvas_map_data_.find(to_canvas);
  if (source_items_it == canvas_map_data_.end() ||
      target_items_it == canvas_map_data_.end()) {
    return {};
  }
  auto source_items = source_items_it->second;
  auto target_items = target_items_it->second;
  if (source_items.empty() || target_items.empty()) {
    return {};
  }

  std::map<std::string, std::string> matched_items;
  for (auto& source_item : source_items) {
    auto target_it = target_items.begin();
    while (target_it != target_items.end()) {
      if ((compare_type == "same_visual" &&
           source_item.visual_id == target_it->visual_id) ||
          (compare_type == "same_type" &&
           source_item.plugin_name == target_it->plugin_name)) {
        matched_items[source_item.canvas_item_id] = target_it->canvas_item_id;
        target_items.erase(target_it);
        break;
      }
      ++target_it;
    }
  }
  return matched_items;
}