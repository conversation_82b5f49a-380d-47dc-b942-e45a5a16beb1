#include "mjpeg_frame_handler.h"

namespace mediasdk {

MjpegFrameHandler::MjpegFrameHandler() {}

MjpegFrameHandler::~MjpegFrameHandler() {}

bool MjpegFrameHandler::Prepare(graphics::Device* device,
                                ColorSpace& color_space,
                                VideoRange& video_range,
                                int64_t& pts) {
  if (!device) {
    return false;
  }

  const auto frame = GetFrame();
  if (!frame) {
    return false;
  }

  ResetConvertIfNeeded();

  if (!convert_) {
    convert_ = graphics::CreateYUVToBGRAGraphics(*device);
  }

  graphics::TextureFrame media_frame = {};
  FFFrameToTextureFrame(*frame, media_frame);
  if (convert_->ConvertMemoryToBGRAPrepare(media_frame)) {
    color_space = media_frame.color_space;
    video_range = media_frame.video_range;
    pts = frame->pts;
    return true;
  }
  return false;
}

void MjpegFrameHandler::PushFrame(std::shared_ptr<AVFrame> frame) {
  std::lock_guard<std::mutex> lock(lock_buffer_);
  while (buffered_frames_.size() > buffered_frames_plan_count_ - 1) {
    buffered_frames_.erase(buffered_frames_.begin());
  }
  buffered_frames_.push_back(std::move(frame));
}

void MjpegFrameHandler::CleanFrame() {
  std::lock_guard<std::mutex> lock(lock_buffer_);
  buffered_frames_.clear();
  reset_convert_on_next_loop_ = true;
}

void MjpegFrameHandler::Convert() {
  if (convert_) {
    convert_->ConvertMemoryToBGRADraw();
  }
}

graphics::Texture* MjpegFrameHandler::GetTexture() {
  if (convert_) {
    return convert_->GetOutputTexture().get();
  }
  return nullptr;
}

void MjpegFrameHandler::SetBufferedFramesPlanCount(uint32_t count) {
  if (count > 0) {
    buffered_frames_plan_count_ = count;
  }
}

std::shared_ptr<AVFrame> MjpegFrameHandler::GetFrame() {
  std::lock_guard<std::mutex> lock(lock_buffer_);
  if (buffered_frames_.empty()) {
    return nullptr;
  }
  auto frame = std::move(buffered_frames_.front());
  buffered_frames_.erase(buffered_frames_.begin());

  return frame;
}

void MjpegFrameHandler::ResetConvertIfNeeded() {
  if (reset_convert_on_next_loop_) {
    convert_.reset();
    reset_convert_on_next_loop_ = false;
  }
}

}  // namespace mediasdk
