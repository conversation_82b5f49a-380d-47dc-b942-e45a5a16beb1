#pragma once

#include "dshow_filter/dshow_capture_base.h"
#include "format_adapter/video_format_transform.h"

namespace mediasdk {

class CameraVideoCapture : public DShowCaptureBase {
 public:
  explicit CameraVideoCapture();
  ~CameraVideoCapture() override;

  // DShowCaptureBase
  bool Create(const nlohmann::json& json_root) override;

  void OnAudioFrameReceive(const uint8_t* data,
                           int32_t size,
                           int64_t tm) override;

  DShowCaptureType GetDShowType() override;

  const char* GetSubTypeName() override;

  bool HasAudio() override;

protected:
  bool has_audio_ = false;
};

}  // namespace mediasdk