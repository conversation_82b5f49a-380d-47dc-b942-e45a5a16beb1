#include "transition_move.h"
#include <graphics/transform_calc.h>
#include "mediasdk/utils/math_helper.h"
#include "mediasdk/video/canvas_render.h"

namespace mediasdk {

constexpr TransitionMoveParams GetDefaultMoveParams() {
  return TransitionMoveParams{
      // move_type
      TransitionMoveType::kTransitionMoveTypeSlide,
      // move_in_functionp
      TransitionProgressFunctionType::
          kTransitionProgressFunctionTypeEaseInCubic,
      // move_out_function
      TransitionProgressFunctionType::
          kTransitionProgressFunctionTypeEaseOutCubic,
      // move_to_function
      TransitionProgressFunctionType::
          kTransitionProgressFunctionTypeEaseInOutCubic,
      // move_in_from_direction
      TransitionDirection::kTransitionDirectionLeft,
      // move_out_to_direction
      TransitionDirection::kTransitionDirectionRight,
      // move_in_from_position
      MSTranslateF{0.0f, 0.0f},
      // move_out_to_position
      MSTranslateF{0.0f, 0.0f},
  };
}

void ParseMoveParams(const nlohmann::json& j, TransitionMoveParams& params) {
  if (j.contains("move_type") && j["move_type"].is_number_integer()) {
    params.move_type = static_cast<TransitionMoveType>(j["move_type"]);
  }
  if (j.contains("move_in_function") &&
      j.at("move_in_function").is_number_integer()) {
    params.move_in_function =
        static_cast<TransitionProgressFunctionType>(j["move_in_function"]);
  }
  if (j.contains("move_out_function") &&
      j.at("move_out_function").is_number_integer()) {
    params.move_out_function =
        static_cast<TransitionProgressFunctionType>(j["move_out_function"]);
  }
  if (j.contains("move_to_function") &&
      j["move_to_function"].is_number_integer()) {
    params.move_to_function =
        static_cast<TransitionProgressFunctionType>(j["move_to_function"]);
  }
  if (j.contains("move_in_from_direction") &&
      j["move_in_from_direction"].is_number_integer()) {
    params.move_in_from_direction =
        static_cast<TransitionDirection>(j["move_in_from_direction"]);
  }
  if (j.contains("move_out_to_direction") &&
      j["move_out_to_direction"].is_number_integer()) {
    params.move_out_to_direction =
        static_cast<TransitionDirection>(j["move_out_to_direction"]);
  }
  if (j.contains("move_in_from_position") &&
      j["move_in_from_position"].is_object()) {
    auto position = j["move_in_from_position"];
    if (position.contains("x") && position["x"].is_number_float()) {
      params.move_in_from_position.x = position["x"];
    }
    if (position.contains("y") && position["y"].is_number_float()) {
      params.move_in_from_position.y = position["y"];
    }
  }
  if (j.contains("move_out_to_position") &&
      j["move_out_to_position"].is_object()) {
    auto position = j["move_out_to_position"];
    if (position.contains("x") && position["x"].is_number_float()) {
      params.move_out_to_position.x = position["x"];
    }
    if (position.contains("y") && position["y"].is_number_float()) {
      params.move_out_to_position.y = position["y"];
    }
  }
}

float FormatRotate(float rotate) {
  while (IsGreaterThan(rotate, 180.f)) {
    rotate -= 360.f;
  }
  while (IsLessThan(rotate, -180.f) || IsNearEqual(rotate, -180.f)) {
    rotate += 360.f;
  }
  return rotate;
}

bool ParseTransitionMoveParams(TransitionActionMoveParams& params,
                               const std::string& property) {
  try {
    nlohmann::json json_obj = nlohmann::json::parse(property);

    if (json_obj.contains("duration_ms") &&
        json_obj["duration_ms"].is_number_unsigned()) {
      auto duration_ms = json_obj["duration_ms"];
      if (duration_ms > 0 && duration_ms <= TRANSITION_DURATION_MS_MAX) {
        params.duration_ms = duration_ms;
      } else {
        params.duration_ms = TRANSITION_DURATION_MS_MAX;
        LOG(WARNING) << "duration_ms: " << duration_ms
                     << " is too large, set to max value: "
                     << TRANSITION_DURATION_MS_MAX;
      }
    }

    if (json_obj.contains("middle_progress") &&
        json_obj["middle_progress"].is_number_float()) {
      params.middle_progress = json_obj["middle_progress"];
    }

    if (json_obj.contains("compared_items")) {
      params.matched_canvas_items.clear();
      if (json_obj["compared_items"].is_array()) {
        auto compared_items = json_obj["compared_items"];
        for (auto& item : compared_items) {
          if (item.contains("source") && item["source"].is_string() &&
              item.contains("target") && item["target"].is_string()) {
            params.matched_canvas_items.push_back(
                std::make_pair(item["source"], item["target"]));
          }
        }
      }
    }

    if (json_obj.contains("default_move_param") &&
        json_obj["default_move_param"].is_object()) {
      params.default_move_params = GetDefaultMoveParams();
      ParseMoveParams(json_obj["default_move_param"],
                      params.default_move_params);
    }

    if (json_obj.contains("items_move") && json_obj["items_move"].is_object()) {
      params.item_move_params.clear();
      for (auto& [key, value] : json_obj["items_move"].items()) {
        TransitionMoveParams item_move_params = GetDefaultMoveParams();
        ParseMoveParams(value, item_move_params);
        params.item_move_params.insert_or_assign(key, item_move_params);
      }
    }

  } catch (...) {
    LOG(ERROR) << "Failed to parse move transition params";
    return false;
  }
  return true;
}

std::shared_ptr<Transition> CreateTransitionMove(
    const std::string& id,
    TransitionProxy* proxy,
    const std::string& properties) {
  TransitionActionMoveParams params{};
  if (!ParseTransitionMoveParams(params, properties)) {
    LOG(ERROR) << "Failed to parse move transition params";
    return nullptr;
  }
  auto transition = std::make_shared<TransitionMove>(id, proxy, params);
  return std::dynamic_pointer_cast<Transition>(transition);
}

float TransitionMove::CaculateProgress(int64_t begin_ms, int64_t used_ms) {
  return CaculateMoveProgress(
      static_cast<float>(used_ms) / static_cast<float>(params_.duration_ms),
      TransitionProgressFunctionType::kTransitionProgressFunctionTypeLinear);
}

void TransitionMove::UpdateProperties(const std::string& properties,
                                      MSCallbackBool callback) {
  TransitionActionMoveParams params = params_;
  if (!ParseTransitionMoveParams(params, properties)) {
    LOG(ERROR) << "Failed to parse move transition params";
    std::move(callback).Resolve(false);
    return;
  }
  std::move(callback).Resolve(true);

  if (0 == transition_ref_count_) {
    params_ = params;
  } else {
    delay_params_ = params;
  }
}

void TransitionMove::DrawTransition(graphics::Graphics& target,
                                    const CanvasRender* from_canvas,
                                    const CanvasRender* to_canvas,
                                    graphics::Graphics& from_graphics,
                                    graphics::Graphics& to_graphics,
                                    const float progress,
                                    const DirectX::XMFLOAT2* scale,
                                    VisualDrawReason draw_reason) {
  if ((IsLessThan(progress, 1.f)) && from_canvas && to_canvas) {
    MatchedItemMap from_item_matched_items;
    MatchedItemMap to_item_matched_items;
    MatchCanvasItem(from_canvas, to_canvas, from_item_matched_items,
                    to_item_matched_items);
    DrawTransitionInternal(target, from_canvas, to_canvas, from_graphics,
                           to_graphics, progress, scale, draw_reason,
                           from_item_matched_items, to_item_matched_items);
  } else if (to_canvas) {
    for (auto& item : to_canvas->GetItems()) {
      DrawItem(target, scale, draw_reason, item);
    }
  }
}

void TransitionMove::IncreaseTransitionRefCount() {
  Transition::IncreaseTransitionRefCount();
  if (delay_params_.has_value()) {
    params_ = delay_params_.value();
    delay_params_.reset();
  }
}

void TransitionMove::MatchCanvasItem(const CanvasRender* from_canvas,
                                     const CanvasRender* to_canvas,
                                     MatchedItemMap& from_item_matched_items,
                                     MatchedItemMap& to_item_matched_items) {
  std::unordered_map<std::string, CanvasItemPtr> from_items_map;
  std::unordered_map<std::string, CanvasItemPtr> to_items_map;

  for (auto& from_item : from_canvas->GetItems()) {
    from_items_map[from_item->GetId()] = from_item;
  }

  for (auto& to_item : to_canvas->GetItems()) {
    to_items_map[to_item->GetId()] = to_item;
  }

  for (auto& compared : params_.matched_canvas_items) {
    auto from_it = from_items_map.find(compared.first);
    auto to_it = to_items_map.find(compared.second);

    if (from_it != from_items_map.end() && to_it != to_items_map.end()) {
      from_item_matched_items[compared.first].push_back(to_it->second);
      to_item_matched_items[compared.second].push_back(from_it->second);
    }
  }
}

void TransitionMove::DrawTransitionInternal(
    graphics::Graphics& target,
    const CanvasRender* from_canvas,
    const CanvasRender* to_canvas,
    graphics::Graphics& from_graphics,
    graphics::Graphics& to_graphics,
    const float progress,
    const DirectX::XMFLOAT2* scale,
    VisualDrawReason draw_reason,
    const MatchedItemMap& from_item_matched_items,
    const MatchedItemMap& to_item_matched_items) {
  if (IsLessThan(progress, params_.middle_progress)) {
    for (auto& to_item : to_canvas->GetItems()) {
      auto item_id = to_item->GetId();
      if (to_item_matched_items.find(item_id) == to_item_matched_items.end()) {
        DrawTransitionItemMoveIn(to_item, target, to_graphics, progress, scale,
                                 draw_reason);
        continue;
      }
    }

    for (auto& from_item : from_canvas->GetItems()) {
      auto item_id = from_item->GetId();
      auto from_it = from_item_matched_items.find(item_id);
      if (from_it == from_item_matched_items.end()) {
        DrawTransitionItemMoveOut(from_item, target, from_graphics, progress,
                                  scale, draw_reason);
        continue;
      }
      for (auto& to_item : from_it->second) {
        DrawTransitionItemMove(from_item, to_item, target, progress, scale,
                               draw_reason);
      }
    }
  } else {
    for (auto& from_item : from_canvas->GetItems()) {
      auto item_id = from_item->GetId();
      if (from_item_matched_items.find(item_id) ==
          from_item_matched_items.end()) {
        DrawTransitionItemMoveOut(from_item, target, from_graphics, progress,
                                  scale, draw_reason);
        continue;
      }
    }

    for (auto& to_item : to_canvas->GetItems()) {
      auto item_id = to_item->GetId();
      auto to_it = to_item_matched_items.find(item_id);
      if (to_it == to_item_matched_items.end()) {
        DrawTransitionItemMoveIn(to_item, target, to_graphics, progress, scale,
                                 draw_reason);
        continue;
      }
      for (auto& from_item : to_it->second) {
        DrawTransitionItemMove(to_item, from_item, target, 1 - progress, scale,
                               draw_reason);
      }
    }
  }
}

void TransitionMove::DrawTransitionItemMoveIn(
    const mediasdk::CanvasItemPtr& item,
    graphics::Graphics& target,
    graphics::Graphics& to_graphics,
    const float progress,
    const DirectX::XMFLOAT2* scale,
    VisualDrawReason draw_reason) {
  if (!item) {
    return;
  }

  const TransitionMoveParams& move_params = GetMoveParams(item->GetId());
  float move_progress =
      CaculateMoveProgress(progress, move_params.move_in_function);
  auto move_type = move_params.move_type;
  switch (move_type) {
    case TransitionMoveType::kTransitionMoveTypeSlide:
      move_progress =
          CaculateMoveProgress(progress, move_params.move_to_function);
      DrawTransitionItemSlide(item, target, 1 - move_progress, scale,
                              draw_reason, move_params.move_in_from_direction);
      break;

    case TransitionMoveType::kTransitionMoveTypeScale:
      DirectX::XMFLOAT2 translate = CalcTranslate(
          move_params.move_in_from_position, target.GetSize(), scale);
      DrawTransitionItemScale(item, target, move_progress, scale, draw_reason,
                              translate);
      break;

    case TransitionMoveType::kTransitionMoveTypeFade:
      DrawTransitionItemFade(item, target, to_graphics, move_progress, scale,
                             draw_reason);
      break;

    default:
      break;
  }
}

void TransitionMove::DrawTransitionItemMoveOut(
    const mediasdk::CanvasItemPtr& item,
    graphics::Graphics& target,
    graphics::Graphics& from_graphics,
    const float progress,
    const DirectX::XMFLOAT2* scale,
    VisualDrawReason draw_reason) {
  if (!item) {
    return;
  }
  const TransitionMoveParams& move_params = GetMoveParams(item->GetId());
  float move_progress =
      CaculateMoveProgress(progress, move_params.move_out_function);
  switch (move_params.move_type) {
    case TransitionMoveType::kTransitionMoveTypeSlide:
      DrawTransitionItemSlide(item, target, move_progress, scale, draw_reason,
                              move_params.move_out_to_direction);
      break;

    case TransitionMoveType::kTransitionMoveTypeScale:
      DirectX::XMFLOAT2 translate = CalcTranslate(
          move_params.move_out_to_position, target.GetSize(), scale);
      DrawTransitionItemScale(item, target, 1.0f - move_progress, scale,
                              draw_reason, translate);
      break;

    case TransitionMoveType::kTransitionMoveTypeFade:
      DrawTransitionItemFade(item, target, from_graphics, 1.0 - move_progress,
                             scale, draw_reason);
      break;

    default:
      DrawItem(target, scale, draw_reason, item);
      break;
  }
}

void TransitionMove::DrawTransitionItemSlide(
    const mediasdk::CanvasItemPtr& item,
    graphics::Graphics& target,
    const float progress,
    const DirectX::XMFLOAT2* scale,
    VisualDrawReason draw_reason,
    TransitionDirection move_direction) {
  if (!item) {
    return;
  }

  DirectX::XMFLOAT2 direction = {0.0f, 0.0f};
  TransitionDirectionToVector2(move_direction, direction);
  DirectX::XMFLOAT2 cur_translate = item->GetTranslate();
  DirectX::XMFLOAT2 cur_scale = item->GetScale();
  float cur_rotate = item->GetRotate();
  DirectX::XMFLOAT2 size = item->GetSize();
  DirectX::XMFLOAT2 render_size = graphics::GetClipAndScaledSize(
      size, item->GetTransform().GetClip(), cur_scale);
  DirectX::XMFLOAT2 center = {cur_translate.x + render_size.x / 2.0f,
                              cur_translate.y + render_size.y / 2.0f};

  DirectX::XMFLOAT4 center_to_edge = XMFLOAT4_EMPTY;
  CaculateMaxDistanceCenterToEdge(render_size, cur_scale, cur_rotate,
                                  center_to_edge);
  DirectX::XMFLOAT4 direction_4;
  TransitionDirectionToVector4(move_direction, direction_4);

  DirectX::XMFLOAT2 vp_size = target.GetSize();
  DirectX::XMFLOAT4 center_to_vp_border = {
      center.x, center.y, vp_size.x - center.x, vp_size.y - center.y};

  DirectX::XMFLOAT2 total_translate = {
      (center_to_vp_border.x + center_to_edge.z) * direction_4.x +
          (center_to_vp_border.z + center_to_edge.x) * direction_4.z,
      (center_to_vp_border.y + center_to_edge.w) * direction_4.y +
          (center_to_vp_border.w + center_to_edge.y) * direction_4.w};

  total_translate.x = total_translate.x < 0 ? 0 : total_translate.x;
  total_translate.x =
      total_translate.x > vp_size.x ? vp_size.x : total_translate.x;
  total_translate.y = total_translate.y < 0 ? 0 : total_translate.y;
  total_translate.y =
      total_translate.y > vp_size.y ? vp_size.y : total_translate.y;

  DirectX::XMFLOAT2 next_translate = XMFLOAT2_EMPTY;
  next_translate.x = total_translate.x * progress * direction.x;
  next_translate.y = total_translate.y * progress * direction.y;

  graphics::Transform old_trans = item->GetTransform();
  graphics::Transform new_trans = old_trans;
  new_trans.SetTranslate(old_trans.GetTranslate() + next_translate);
  item->SetTransform(new_trans, false);
  DrawItem(target, scale, draw_reason, item);
  item->SetTransform(old_trans, false);
}

void TransitionMove::CaculateMaxDistanceCenterToEdge(
    const DirectX::XMFLOAT2& size,
    const DirectX::XMFLOAT2& scale,
    const float rotate,
    DirectX::XMFLOAT4& max_distance) {
  // Calculate the maximum distance from the center point of the rectangle to
  // the edges in four directions.

  float radians = XMConvertToRadians(rotate);

  DirectX::XMMATRIX transform =
      XMMatrixScaling(size.x, size.y, 1.0f) * XMMatrixRotationZ(-radians);

  DirectX::XMVECTOR vertices[4] = {
      XMVectorSet(-0.5f, -0.5f, 0, 1), XMVectorSet(0.5f, -0.5f, 0, 1),
      XMVectorSet(0.5f, 0.5f, 0, 1), XMVectorSet(-0.5f, 0.5f, 0, 1)};

  DirectX::XMFLOAT2 transformed[4];
  for (int i = 0; i < 4; ++i) {
    DirectX::XMVECTOR vec = XMVector3Transform(vertices[i], transform);
    XMStoreFloat2(&transformed[i], vec);
  }

  float min_x = transformed[0].x;
  float max_x = transformed[0].x;
  float min_y = transformed[0].y;
  float max_y = transformed[0].y;

  for (int i = 1; i < 4; ++i) {
    min_x = std::min(min_x, transformed[i].x);
    max_x = std::max(max_x, transformed[i].x);
    min_y = std::min(min_y, transformed[i].y);
    max_y = std::max(max_y, transformed[i].y);
  }

  max_distance.x = 0.f - min_x;
  max_distance.y = 0.f - min_y;
  max_distance.z = 0.f + max_x;
  max_distance.w = 0.f + max_y;
}

void TransitionMove::DrawTransitionItemScale(
    const mediasdk::CanvasItemPtr& item,
    graphics::Graphics& target,
    const float progress,
    const DirectX::XMFLOAT2* scale,
    VisualDrawReason draw_reason,
    const DirectX::XMFLOAT2& translate) {
  graphics::Transform old_trans = item->GetTransform();
  DirectX::XMFLOAT2 new_scale = old_trans.GetScale();
  new_scale.x *= progress;
  new_scale.y *= progress;

  auto item_translate = item->GetTranslate();
  DirectX::XMFLOAT2 next_translate = XMFLOAT2_EMPTY;
  next_translate.x = translate.x + (item_translate.x - translate.x) * progress;
  next_translate.y = translate.y + (item_translate.y - translate.y) * progress;

  graphics::Transform new_trans = old_trans;
  new_trans.SetScale(new_scale);
  new_trans.SetTranslate(next_translate);
  item->SetTransform(new_trans, false);
  DrawItem(target, scale, draw_reason, item);
  item->SetTransform(old_trans, false);
}

void TransitionMove::DrawTransitionItemFade(
    const mediasdk::CanvasItemPtr& item,
    graphics::Graphics& target,
    graphics::Graphics& from_or_to_graphics,
    const float progress,
    const DirectX::XMFLOAT2* scale,
    VisualDrawReason draw_reason) {
  from_or_to_graphics.BeginDraw(true);
  DrawItem(from_or_to_graphics, scale, draw_reason, item);
  from_or_to_graphics.EndDraw();
  auto texture = from_or_to_graphics.GetOutputTexture();
  if (!texture) {
    return;
  }

  graphics::ColorParams normalized_color_params;
  normalized_color_params.SetOpacity(progress);
  target.DrawBGRATextureColorParams(*texture, graphics::Transform(),
                                    normalized_color_params);
}

void TransitionMove::DrawTransitionItemMove(
    const mediasdk::CanvasItemPtr& from_item,
    const mediasdk::CanvasItemPtr& to_item,
    graphics::Graphics& target,
    const float progress,
    const DirectX::XMFLOAT2* scale,
    VisualDrawReason draw_reason) {
  const TransitionMoveParams& move_params = GetMoveParams(from_item->GetId());
  float move_progress =
      CaculateMoveProgress(progress, move_params.move_to_function);
  auto from_size = GetItemSizeOnWindow(from_item, target.GetSize(), scale);
  auto to_size = GetItemSizeOnWindow(to_item, target.GetSize(), scale);
  auto from_tex_size = from_item->GetSize();
  auto to_tex_size = to_item->GetSize();
  bool equal_ratio = IsNearEqual(from_tex_size.x * to_tex_size.y,
                                 from_tex_size.y * to_tex_size.x);
  auto cur_size =
      CalcCurrentSize(from_size, to_size, !equal_ratio, move_progress);

  DirectX::XMFLOAT2 scale_diff{
      IsLessThan(from_size.x, 1.f) ? cur_size.x : cur_size.x / from_size.x,
      IsLessThan(from_size.y, 1.f) ? cur_size.y : cur_size.y / from_size.y};

  auto old_transform = from_item->GetTransform();
  auto new_transform =
      CalcTransform(from_item->GetTransform(), to_item->GetTransform(),
                    scale_diff, move_progress);

  from_item->SetTransform(new_transform, false);
  DrawItem(target, scale, draw_reason, from_item);
  from_item->SetTransform(old_transform, false);
}

const TransitionMoveParams& TransitionMove::GetMoveParams(
    const std::string& item_id) {
  auto it = params_.item_move_params.find(item_id);
  if (it != params_.item_move_params.end()) {
    return it->second;
  }
  return params_.default_move_params;
}

DirectX::XMFLOAT2 TransitionMove::GetItemSizeOnWindow(
    const CanvasItemPtr& item,
    const DirectX::XMFLOAT2& window_region,
    const DirectX::XMFLOAT2* scale) {
  uint32_t wx = static_cast<uint32_t>(window_region.y);
  uint32_t wy = static_cast<uint32_t>(window_region.y);
  graphics::Transform old_trans = item->GetTransform();
  graphics::Transform new_trans = old_trans;
  new_trans.SetRotate(0);
  new_trans.SetClip({0.f, 0.f, 0.f, 0.f});
  if (scale) {
    new_trans.SetScale(old_trans.GetScale() * (*scale));
    new_trans.SetTranslate(old_trans.GetTranslate() * (*scale));
  }
  item->SetTransform(new_trans, false);
  auto size = item->GetRegionOnWindow(wx, wy);
  item->SetTransform(old_trans, false);
  return DirectX::XMFLOAT2{size.z - size.x, size.w - size.y};
}

DirectX::XMFLOAT2 TransitionMove::CalcCurrentSize(
    const DirectX::XMFLOAT2& from_size,
    const DirectX::XMFLOAT2& to_size,
    bool keep_ratio,
    const float progress) {
  float cur_x = from_size.x + (to_size.x - from_size.x) * progress;
  float cur_y = keep_ratio
                    ? (cur_x / from_size.x * from_size.y)
                    : (from_size.y + (to_size.y - from_size.y) * progress);
  if (IsLessThan(cur_y, 1.f)) {
    cur_y = 1.f;
  }
  if (IsLessThan(cur_x, 1.f)) {
    cur_x = 1.f;
  }
  return DirectX::XMFLOAT2{cur_x, cur_y};
}

DirectX::XMFLOAT2 TransitionMove::CalcTranslate(
    const MSTranslateF& loc,
    const DirectX::XMFLOAT2& target_region,
    const DirectX::XMFLOAT2* scale) {
  float region_x = target_region.x;
  float region_y = target_region.y;
  if (scale) {
    if (IsGreaterThan(scale->x, 0.f)) {
      region_x /= scale->x;
    }
    if (IsGreaterThan(scale->y, 0.f)) {
      region_y /= scale->y;
    }
  }
  return {loc.x * region_x, loc.y * region_y};
}

graphics::Transform TransitionMove::CalcTransform(
    const graphics::Transform& src_transform,
    const graphics::Transform& dst_transform,
    const DirectX::XMFLOAT2& scale_diff,
    float move_progress) {
  graphics::Transform new_transform = src_transform;

  float from_rotate = FormatRotate(src_transform.GetRotate());
  float to_rotate = FormatRotate(dst_transform.GetRotate());
  float cur_rotate = (from_rotate + (to_rotate - from_rotate) * move_progress);

  DirectX::XMFLOAT4 clip_diff =
      dst_transform.GetClip() - src_transform.GetClip();
  DirectX::XMFLOAT4 clip = clip_diff * move_progress + src_transform.GetClip();

  bool src_flip_h = src_transform.IsFlipH();
  bool src_flip_v = src_transform.IsFlipV();
  bool dst_flip_h = dst_transform.IsFlipH();
  bool dst_flip_v = dst_transform.IsFlipV();
  float shear_angle = cur_rotate;
  float shear_x = 1.f;
  float shear_y = 1.f;
  if (src_flip_h != dst_flip_h) {
    shear_x = std::abs(shear_x - 2 * move_progress);
  }
  if (src_flip_v != dst_flip_v) {
    shear_y = std::abs(shear_y - 2 * move_progress);
  }

  new_transform.SetFlipH(move_progress > params_.middle_progress ? dst_flip_h
                                                                 : src_flip_h);
  new_transform.SetFlipV(move_progress > params_.middle_progress ? dst_flip_v
                                                                 : src_flip_v);
  new_transform.SetRotate(cur_rotate);
  new_transform.SetClip(clip);
  DirectX::XMFLOAT2 shear{shear_x, shear_y};
  new_transform.SetShear(shear);

  DirectX::XMFLOAT2 new_scale = src_transform.GetScale();
  new_scale.x *= scale_diff.x;
  new_scale.y *= scale_diff.y;
  new_transform.SetScale(new_scale);

  auto from_translate = src_transform.GetTranslate();
  auto to_translate = dst_transform.GetTranslate();
  float translate_x =
      from_translate.x + (to_translate.x - from_translate.x) * move_progress;
  float translate_y =
      from_translate.y + (to_translate.y - from_translate.y) * move_progress;
  new_transform.SetTranslate({translate_x, translate_y});

  return new_transform;
}

}  // namespace mediasdk