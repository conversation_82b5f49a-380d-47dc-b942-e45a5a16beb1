#pragma once

#include <QListWidget>
#include "mediasdk/public/mediasdk_api_canvas.h"

class CanvasListWidget : public QListWidget {
  Q_OBJECT

 public:
  CanvasListWidget(QWidget* parent);

  void SetCurrentCanvas(const QString& id);

  QString GetCurrentCanvas() const;

  void SetCurrentTransition(const QString& id, const std::string& type);

  std::map<std::string, std::string> GetCanvasIdMapName();
 signals:
  void ChangeCanvas(const std::string& new_canvas,
                    const std::string& transition);

 public slots:
  void OnItemSelected();

 private:
  QString current_id_;
  QString current_transition_;
};