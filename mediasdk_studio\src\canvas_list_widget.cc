#include "canvas_list_widget.h"

#include <qevent.h>

#include <QDebug>
#include <QUuid>

#include "mediasdk/mediasdk_call.h"
#include "mediasdk_api_canvas.h"

#include "common_defs.h"

CanvasListWidget::CanvasListWidget(QWidget* parent) {
  connect(this, &QListWidget::itemSelectionChanged, this,
          &CanvasListWidget::OnItemSelected);

  setContextMenuPolicy(Qt::CustomContextMenu);
}

void CanvasListWidget::SetCurrentCanvas(const QString& id) {
  current_id_ = id;

  if (id.isEmpty()) {
    return;
  }

  for (int i = 0; i < count(); ++i) {
    auto it = item(i);
    if (it->data(STR_KEY_CANVAS_ID) == id) {
      setCurrentRow(i);
      break;
    }
  }
}

QString CanvasListWidget::GetCurrentCanvas() const {
  return current_id_;
}

void CanvasListWidget::SetCurrentTransition(const QString& id,
                                            const std::string& type) {
  current_transition_ = id;
}

void CanvasListWidget::OnItemSelected() {
  QListWidgetItem* item = currentItem();

  if (item) {
    QString canvas_id = item->data(STR_KEY_CANVAS_ID).toString();
#ifdef _DEBUG
    qDebug() << "[DBG] Selected item with id: " << canvas_id
             << "; current id: " << current_id_;
#endif  // _DEBUG

    const std::string transition_id_str = current_transition_.toStdString();

    uint32_t video_model_id = item->data(STR_KEY_SINK_ID).toUInt();
    if (canvas_id != current_id_) {
      emit ChangeCanvas(canvas_id.toStdString(),
                        current_transition_.toStdString());
      /*MediaSDKCall(
          base::BindOnce([](bool* result) {}), &mediasdk::SetCurrentCanvas,
          video_model_id, canvas_id.toStdString().c_str(),
          transition_id_str.empty() ? nullptr : transition_id_str.c_str());*/
      current_id_ = canvas_id;
    }
  }
}

std::map<std::string, std::string> CanvasListWidget::GetCanvasIdMapName() {
  std::map<std::string, std::string> canvas_id_map;
  for (int i = 0; i < count(); ++i) {
    auto cur_item = item(i);
    canvas_id_map.insert_or_assign(
        cur_item->data(STR_KEY_CANVAS_ID).toString().toStdString(),
        cur_item->text().toStdString());
  }
  return canvas_id_map;
}