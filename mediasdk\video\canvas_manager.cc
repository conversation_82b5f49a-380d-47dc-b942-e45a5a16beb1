#include "canvas_manager.h"
#include "mediasdk/notify_center.h"
#include "mediasdk/video/clip_mask_canvas_item.h"
#include "mediasdk/video/visual_canvas_item.h"
#include "mediasdk/video/visual_filter_manager.h"

namespace mediasdk {

CanvasManager::CanvasManager(graphics::Device& device) {}

void CanvasManager::OnVideoModelActiveSet(uint32_t video_model_id,
                                          bool is_active) {
  auto it = std::find(disabled_video_model_arr_.begin(),
                      disabled_video_model_arr_.end(), video_model_id);
  if (is_active && it != disabled_video_model_arr_.end()) {
    disabled_video_model_arr_.erase(it);
  }
  if (!is_active && it == disabled_video_model_arr_.end()) {
    disabled_video_model_arr_.emplace_back(video_model_id);
  }

  ChangeCurrentCanvasActiveOfVideoModel(video_model_id, is_active);
}

bool CanvasManager::AddCanvas(uint32_t video_model_id,
                              const std::string& canvas_id,
                              graphics::Device& device) {
  auto it = canvases_.find(canvas_id);
  if (it != canvases_.end()) {
    LOG(ERROR) << "[Canvas] canvas:" << canvas_id << " already exist";
    return false;
  }
  auto canvas = std::make_shared<Canvas>(canvas_id, device);
  auto pair = canvases_.insert(std::make_pair(canvas_id, canvas));
  if (!pair.second) {
    LOG(ERROR) << "[Canvas] insert canvas:" << canvas_id << " failed";
    return false;
  }
  OnCanvasAdded(canvas_id, video_model_id);
  return true;
}

bool CanvasManager::RemoveCanvas(const std::string& canvas_id) {
  size_t size = canvases_.erase(canvas_id);
  if (0 == size) {
    LOG(ERROR) << "[Canvas] remove canvas:" << canvas_id << " failed";
    return false;
  }
  OnCanvasErased(canvas_id);
  return true;
}

bool CanvasManager::ContainsCanvas(const std::string& canvas_id) {
  return canvases_.find(canvas_id) != canvases_.end();
}

void CanvasManager::ClearCanvasesOfVideoModel(uint32_t video_model_id) {
  std::vector<std::string> canvas_id_arr;
  for (auto& [canvas_id, cur_video_model_id] : canvas_map_video_model_) {
    if (video_model_id == cur_video_model_id) {
      canvas_id_arr.emplace_back(canvas_id);
    }
  }
  for (auto& canvas_id : canvas_id_arr) {
    RemoveCanvas(canvas_id);
  }
}

bool CanvasManager::CanvasIsCurrent(const std::string& canvas_id) {
  for (auto& [video_model_id, cur_canvas_id] : current_canvas_map_) {
    if (cur_canvas_id == canvas_id) {
      return true;
    }
  }
  return false;
}

std::optional<uint32_t> CanvasManager::GetVideoModelOfCanvas(
    const std::string& canvas_id) {
  auto it = canvas_map_video_model_.find(canvas_id);
  if (it == canvas_map_video_model_.end()) {
    return std::nullopt;
  }
  return std::make_optional(it->second);
}

CanvasPtr CanvasManager::GetCanvas(const std::string& canvas_id) {
  auto it = canvases_.find(canvas_id);
  if (canvases_.end() == it) {
    return nullptr;
  }
  return it->second;
}

CanvasPtr CanvasManager::GetCanvasByItemId(const std::string& canvas_item_id) {
  auto it = item_map_canvas_.find(canvas_item_id);
  if (item_map_canvas_.end() == it) {
    LOG(ERROR) << "[Canvas] can not find canvas by canvas item:"
               << canvas_item_id;
    return nullptr;
  }
  auto& canvas_id = it->second;
  auto canvas_it = canvases_.find(canvas_id);
  if (canvas_it == canvases_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id
               << " by canvas item:" << canvas_item_id;
    return nullptr;
  }
  return canvas_it->second;
}

bool CanvasManager::AddCanvasItem(const std::string& canvas_item_id,
                                  const std::string& canvas_id,
                                  const CreateCanvasItemParams& params,
                                  VisualPtr visual) {
  if (!visual) {
    return false;
  }

  if (!visual->CanReference()) {
    LOG(ERROR) << "[Canvas] visual:" << visual->GetId()
               << " can not reference for item:" << canvas_item_id;
    return false;
  }

  auto canvas_it = canvases_.find(canvas_id);
  if (canvas_it == canvases_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id;
    return false;
  }

  auto canvas_item = VisualCanvasItem::Create(
      canvas_item_id, graphics::Transform::FromMsTransform(params.transform),
      *(canvas_it->second), visual);
  canvas_item->SetVisible(params.is_visible);
  if (!canvas_it->second->AddItem(canvas_item)) {
    LOG(ERROR) << "[Canvas] AddCanvasItem failed";
    return false;
  }
  OnItemAddedToCanvas(canvas_item_id, canvas_id);
  return true;
}

bool CanvasManager::EraseCanvasItem(const std::string& item_id) {
  auto it = item_map_canvas_.find(item_id);
  if (it == item_map_canvas_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas item:" << item_id;
    return false;
  }
  auto canvas_id = it->second;
  auto canvas_it = canvases_.find(canvas_id);
  if (canvas_it == canvases_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id
               << ", by canvas item:" << item_id;
    return false;
  }
  if (!canvas_it->second->EraseItem(item_id)) {
    return false;
  }
  OnItemErasedFromCanvas(item_id, canvas_id);
  return true;
}

bool CanvasManager::SetCurrentItemForCanvas(const std::string& canvas_item_id,
                                            const std::string& canvas_id,
                                            bool manual) {
  auto canvas = GetCanvas(canvas_id);
  if (!canvas) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id;
    return false;
  }

  if (IsSetSameItem(canvas.get(), canvas_item_id)) {
    return true;
  }

  if (!canvas->SetCurrentItem(canvas_item_id)) {
    LOG(ERROR) << "[Canvas] failed to set current item:" << canvas_item_id;
    return false;
  }

  OnCanvasItemCurrentChanged(canvas_item_id, canvas_id, manual);
  return true;
}

bool CanvasManager::SetCurrentItemForCanvas(CanvasItemPtr item,
                                            std::shared_ptr<Canvas> canvas,
                                            bool manual) {
  if (!canvas) {
    return false;
  }

  const std::string item_id = item ? item->GetId() : "";
  return SetCurrentItemForCanvas(item_id, canvas->GetCanvasId(), manual);
}

CanvasItemPtr CanvasManager::GetCurrentItemOfCanvas(
    const std::string& canvas_id) {
  auto canvas = GetCanvas(canvas_id);
  if (!canvas) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id;
    return nullptr;
  }
  return canvas->GetCurrentItem();
}

bool CanvasManager::SetHoveredItemForVideoModel(uint32_t video_model_id,
                                                CanvasItemPtr item) {
  auto canvas = GetCurrentCanvas(video_model_id);
  if (canvas && canvas->SetHoveredItem(item)) {
    auto nc = com::GetNotifyCenter();
    nc->CanvasEvent()->Notify(
        FROM_HERE, &MediaSDKCanvasEventObserver::OnHittestCanvasItemChanged,
        video_model_id, item ? item->GetId() : "");
    return true;
  }
  return false;
}

CanvasItemPtr CanvasManager::GetCanvasItem(const std::string& canvas_item_id) {
  auto it = item_map_canvas_.find(canvas_item_id);
  if (it == item_map_canvas_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas item:" << canvas_item_id;
    return nullptr;
  }
  auto& canvas_id = it->second;
  auto canvas_it = canvases_.find(canvas_id);
  if (canvas_it == canvases_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id
               << " from canvas item:" << canvas_item_id;
    return nullptr;
  }
  return canvas_it->second->GetItem(canvas_item_id);
}

bool CanvasManager::SetScaleForItemsByVideoModel(uint32_t video_model_id,
                                                 float scale) {
  if (canvas_map_video_model_.empty()) {
    return false;
  }

  bool changed = false;
  for (auto& [canvas_id, video_id] : canvas_map_video_model_) {
    if (video_id != video_model_id) {
      continue;
    }
    auto canvas_it = canvases_.find(canvas_id);
    if (canvas_it == canvases_.end()) {
      continue;
    }
    auto& canvas = canvas_it->second;
    if (!canvas) {
      continue;
    }
    auto items = canvas->GetItems();
    for (auto& item : items) {
      if (!item) {
        continue;
      }
      item->SetScale(item->GetScale() * scale, false);
      item->SetTranslate(item->GetTranslate() * scale, false);
      changed = true;
    }
  }
  return changed;
}

CanvasPtr CanvasManager::GetCurrentCanvas(uint32_t video_model_id) {
  auto it = current_canvas_map_.find(video_model_id);
  if (it == current_canvas_map_.end()) {
    return nullptr;
  }

  auto& canvas_id = it->second;
  auto canvas_it = canvases_.find(canvas_id);
  if (canvas_it == canvases_.end()) {
    return nullptr;
  }
  return canvas_it->second;
}

CanvasPtrList CanvasManager::GetAllCurrentCanvas() {
  CanvasPtrList canvases;
  canvases.reserve(current_canvas_map_.size());
  for (auto& [video_model_id, canvas_id] : current_canvas_map_) {
    auto canvas_it = canvases_.find(canvas_id);
    if (canvas_it != canvases_.end()) {
      canvases.emplace_back(canvas_it->second);
    }
  }
  return canvases;
}

std::map<uint32_t, CanvasPtr> CanvasManager::GetAllCurrentCanvasMap() {
  std::map<uint32_t, CanvasPtr> canvas_map;
  for (auto& [video_model_id, canvas_id] : current_canvas_map_) {
    auto canvas_it = canvases_.find(canvas_id);
    if (canvas_it != canvases_.end()) {
      canvas_map.emplace(video_model_id, canvas_it->second);
    }
  }
  return canvas_map;
}

bool CanvasManager::SetCurrentCanvas(uint32_t video_model_id,
                                     const std::string& canvas_id,
                                     bool old_canvas_deactive_now) {
  auto new_canvas_it = canvases_.find(canvas_id);
  if (new_canvas_it == canvases_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id;
    return false;
  }

  auto current_it = current_canvas_map_.find(video_model_id);
  if (current_it != current_canvas_map_.end()) {
    auto old_canvas_it = canvases_.find(current_it->second);
    if (old_canvas_it != canvases_.end() && old_canvas_deactive_now) {
      LOG(INFO) << "Old canvas: " << current_it->second
                << "; new canvas: " << canvas_id;
      old_canvas_it->second->SetActive(false);
    }
    current_it->second = canvas_id;
  } else {
    if (!current_canvas_map_.insert(std::make_pair(video_model_id, canvas_id))
             .second) {
      return false;
    }
  }

  if (!IsVideoModelDisabled(video_model_id)) {
    new_canvas_it->second->SetActive(true);
  }

  OnCurrentCanvasChanged(video_model_id, canvas_id);
  return true;
}

bool CanvasManager::BeginCanvasItemClip(const std::string& item_id) {
  for (auto& [video_model_id, canvas_id] : current_canvas_map_) {
    auto canvas_it = canvases_.find(canvas_id);
    if (canvas_it == canvases_.end()) {
      continue;
    }
    auto& canvas = canvas_it->second;
    auto item = canvas->GetItem(item_id);
    if (!item) {
      continue;
    }
    auto clip_item = ClipMaskCanvasItem::Create(item_id, item);
    if (clip_item && canvas->SetClipItem(clip_item)) {
      canvas->SetCurrentItem(clip_item);
      OnCanvasItemCurrentChanged(clip_item->GetId(), canvas_id, false);
      return true;
    }
  }

  LOG(ERROR) << "[Canvas] Cannot find such item:" << item_id;
  return false;
}

bool CanvasManager::EndCanvasItemClip(uint32_t video_model_id) {
  auto it = current_canvas_map_.find(video_model_id);
  if (it == current_canvas_map_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas for video model:"
               << video_model_id;
    return false;
  }
  auto canvas_id = it->second;
  auto canvas_it = canvases_.find(canvas_id);
  if (canvas_it == canvases_.end()) {
    LOG(ERROR) << "[Canvas] can not find canvas:" << canvas_id;
    return false;
  }
  canvas_it->second->EraseClipItem();
  LOG(INFO) << "[Canvas] end clip for video model:" << video_model_id;
  return true;
}

std::vector<MediaSDKString> CanvasManager::GetCanvasItemOrderIDS(
    const std::shared_ptr<Canvas>& canvas) const {
  std::vector<MediaSDKString> canvas_item_ids;
  auto items = canvas->GetItems();
  canvas_item_ids.reserve(items.size());
  for (auto it = items.rbegin(); it != items.rend(); ++it) {
    if (it->get()) {
      canvas_item_ids.emplace_back(MediaSDKString(it->get()->GetId()));
    }
  }
  return canvas_item_ids;
}

bool CanvasManager::SetCanvasItemByOrderIDS(
    const std::shared_ptr<Canvas>& canvas,
    const std::vector<MediaSDKString>& array) {
  std::vector<std::string> item_ids_str;
  item_ids_str.reserve(array.size());
  for (auto& id : array) {
    auto id_str = id.ToString();
    item_ids_str.emplace_back(id_str);
  }
  if (canvas) {
    return canvas->SetItemsOrder(item_ids_str);
  }
  return false;
}

std::vector<MediaSDKString> CanvasManager::GetCanvasItemOrderIDS(
    const std::string& canvas_id) const {
  auto it = canvases_.find(canvas_id);
  if (it == canvases_.end()) {
    return std::vector<MediaSDKString>();
  }

  return GetCanvasItemOrderIDS(it->second);
}

bool CanvasManager::SetCanvasItemByOrderIDS(
    const std::string& canvas_id,
    const std::vector<MediaSDKString>& item_ids) {
  auto it = canvases_.find(canvas_id);
  if (it == canvases_.end()) {
    return false;
  }
  if (!it->second.get()) {
    return false;
  }

  return SetCanvasItemByOrderIDS(it->second, item_ids);
}

PreviewManager* CanvasManager::BuildPreview(const std::string& preview_id,
                                            const std::string& canvas_item_id) {
  if (preview_map_item_.find(preview_id) != preview_map_item_.end()) {
    LOG(WARNING) << "[Preview] Preview has existed. Id=" << preview_id;
    return nullptr;
  }

  preview_map_item_[preview_id] = canvas_item_id;

  const auto canvas_item = GetCanvasItem(canvas_item_id);
  if (!canvas_item) {
    LOG(ERROR) << "Cannot find canvas item. Id=" << canvas_item_id;
    return nullptr;
  }

  const auto preview_mgr = canvas_item->GetPreviewManager();
  if (preview_mgr) {
    LOG(ERROR) << "AddObserver. Id=" << preview_id;
    canvas_item->AddObserver(preview_mgr);
  }

  return preview_mgr;
}

bool CanvasManager::ErasePreview(const std::string& preview_id) {
  if (preview_map_item_.find(preview_id) == preview_map_item_.end()) {
    LOG(ERROR) << "Cannot find preview. Id=" << preview_id;
    return false;
  }

  const auto canvas_item = GetCanvasItemByPreviewId(preview_id);
  if (!canvas_item) {
    LOG(ERROR) << "Cannot get canvas item from preview id=" << preview_id;
    return false;
  }

  preview_map_item_.erase(preview_id);
  if (const auto preview_mgr = canvas_item->GetPreviewManager()) {
    preview_mgr->StopPreview(preview_id);
    if (!preview_mgr->HasPreview()) {
      LOG(WARNING) << "Preview all stopped and observer removed. Id="
                   << preview_id;
      canvas_item->RemoveObserver(preview_mgr);
    }
    LOG(INFO) << "Preview stopped. Id=" << preview_id;
    return true;
  }

  return false;
}

CanvasItemPtr CanvasManager::GetCanvasItemByPreviewId(
    const std::string& preview_id) {
  if (preview_map_item_.find(preview_id) == preview_map_item_.end()) {
    return nullptr;
  }

  return GetCanvasItem(preview_map_item_[preview_id]);
}

std::map<uint32_t, std::vector<std::string>> CanvasManager::GetCanvasIdMap() {
  std::map<uint32_t, std::vector<std::string>> canvas_id_map;
  for (const auto& [canvas_id, video_model_id] : canvas_map_video_model_) {
    canvas_id_map[video_model_id].push_back(canvas_id);
  }
  return canvas_id_map;
}

std::map<std::string, std::vector<std::string>>
CanvasManager::GetCanvasItemIdMap() {
  std::map<std::string, std::vector<std::string>> canvas_item_id_map;
  for (const auto& [item_id, canvas_id] : item_map_canvas_) {
    canvas_item_id_map[canvas_id].push_back(item_id);
  }

  return canvas_item_id_map;
}

void CanvasManager::OnCanvasItemCurrentChanged(
    const std::string& canvas_item_id,
    const std::string& canvas_id,
    bool manual) {
  auto video_model_id = GetVideoModelOfCanvas(canvas_id);
  if (!video_model_id.has_value()) {
    return;
  }

  if (auto nc = com::GetNotifyCenter()) {
    nc->CanvasEvent()->Notify(
        FROM_HERE, &MediaSDKCanvasEventObserver::OnCanvasCurrentItemChanged,
        video_model_id.value(), canvas_id, manual, canvas_item_id);
  }
}

void CanvasManager::OnCurrentCanvasChanged(uint32_t video_model_id,
                                           const std::string& canvas_id) {
  LOG(INFO) << "[Canvas] Current " << canvas_id << "in " << video_model_id;
  if (const auto nc = com::GetNotifyCenter()) {
    nc->CanvasEvent()->Notify(
        FROM_HERE, &MediaSDKCanvasEventObserver::OnCurrentCanvasChanged,
        video_model_id, canvas_id);
  }
}

bool CanvasManager::IsVideoModelDisabled(uint32_t video_model_id) {
  return disabled_video_model_arr_.end() !=
         std::find(disabled_video_model_arr_.begin(),
                   disabled_video_model_arr_.end(), video_model_id);
}

void CanvasManager::ChangeCurrentCanvasActiveOfVideoModel(
    uint32_t video_model_id,
    bool active) {
  auto current_it = current_canvas_map_.find(video_model_id);
  if (current_it == current_canvas_map_.end()) {
    return;
  }

  auto canvas_id = current_it->second;
  auto it = canvases_.find(canvas_id);
  if (it == canvases_.end()) {
    return;
  }
  if (!it->second) {
    return;
  }

  it->second->SetActive(active);
  LOG(INFO) << "[Canvas] canvas:" << canvas_id << " is set active:" << active
            << " for video model:" << video_model_id;
}

bool CanvasManager::IsSetSameItem(Canvas* canvas,
                                  const std::string& target_canvas_item_id) {
  if (!canvas) {
    return false;
  }

  if (const auto item = canvas->GetCurrentItem()) {
    if (item->GetId() == target_canvas_item_id) {
      return true;
    }
  } else if (target_canvas_item_id.empty()) {
    // Both current canvas item and target_canvas_item_id are empty
    return true;
  }

  return false;
}

void CanvasManager::OnItemAddedToCanvas(const std::string& canvas_item_id,
                                        const std::string& canvas_id) {
  item_map_canvas_.insert_or_assign(canvas_item_id, canvas_id);
  LOG(INFO) << "[Canvas] canvas item:" << canvas_item_id
            << " is added to canvas:" << canvas_id;
}

void CanvasManager::OnItemErasedFromCanvas(const std::string& canvas_item_id,
                                           const std::string& canvas_id) {
  item_map_canvas_.erase(canvas_item_id);
  LOG(INFO) << "[Canvas] canvas item:" << canvas_item_id
            << " is erased from canvas:" << canvas_id;
}

void CanvasManager::OnCanvasAdded(const std::string& canvas_id,
                                  uint32_t video_model_id) {
  canvas_map_video_model_.insert_or_assign(canvas_id, video_model_id);
  LOG(INFO) << "[Canvas] canvas:" << canvas_id
            << " is added to video model:" << video_model_id;
}

void CanvasManager::OnCanvasErased(const std::string& canvas_id) {
  // if canvas_id is current, need remove it from current_canvas_map_
  for (auto& [video_model_id, cur_canvas_id] : current_canvas_map_) {
    if (cur_canvas_id == canvas_id) {
      LOG(INFO) << "[Canvas] canvas:" << cur_canvas_id
                << " remove current from video model:" << video_model_id;
      OnCurrentCanvasChanged(video_model_id, "");
      current_canvas_map_.erase(video_model_id);
      break;
    }
  }

  // erase canvas from canvas_map_video_model_
  auto canvas_map_it = canvas_map_video_model_.find(canvas_id);
  if (canvas_map_it != canvas_map_video_model_.end()) {
    LOG(INFO) << "[Canvas] canvas:" << canvas_id
              << " is erased for video model:" << canvas_map_it->second;
    canvas_map_video_model_.erase(canvas_map_it);
  }

  // erase canvas item from item_map_canvas_
  auto it = item_map_canvas_.begin();
  while (it != item_map_canvas_.end()) {
    if (it->second == canvas_id) {
      LOG(INFO) << "[Canvas] canvas item:" << it->first
                << " is erased for canvas:" << canvas_id;
      it = item_map_canvas_.erase(it);
    } else {
      ++it;
    }
  }

  LOG(INFO) << "[Canvas] canvas:" << canvas_id << " is erased";
}

}  // namespace mediasdk