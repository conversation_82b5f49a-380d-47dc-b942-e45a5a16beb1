﻿#include "effect_visual_filter.h"

#include <bitset>
#include <functional>
#include <map>
#include <vector>

#include "base/files/file_path.h"
#include "base/logging.h"
#include "base/path_service.h"
#include "composer_task.h"
#include "effect_processor_destroy_holder.h"

namespace {

constexpr char kABEffectKey[] = "effect";
constexpr char kMediasdkKey[] = "mediasdk";
constexpr char kEffectFilter<PERSON>ey[] = "effect_filter";
constexpr int kEffectDestroyWaitTimeoutMS = 1000;

enum class FILTER_EVENT_TYPE : int { kCreate = 0, kDestroy = 1, kStatus = 2 };

}  // namespace

namespace mediasdk {

namespace {
const std::map<std::string,
               std::function<std::unique_ptr<ComposerTask>(const std::string&)>>
    kComposerExecs = {
        {effect_visual_filter::kComposerAdderKey, &ComposerTask::CreateAddTask},
        {effect_visual_filter::kComposerSetterKey,
         &ComposerTask::CreateSetTask},
        {effect_visual_filter::kComposerUpdaterKey,
         &ComposerTask::CreateUpdateTask},
        {effect_visual_filter::kComposerRemoverKey,
         &ComposerTask::CreateRemoveTask},
        {effect_visual_filter::kComposerSetTextKey,
         &ComposerTask::CreateTextTask},
        {effect_visual_filter::kComposerSetBackgroundKey,
         &ComposerTask::CreateBackgroundTask},
        {effect_visual_filter::kComposerReplaceKey,
         &ComposerTask::CreateReplaceTask},
        {effect_visual_filter::kSendMsg,
         &ComposerTask::CreateMessageTask}
};
}  // namespace

EffectVisualFilter::EffectVisualFilter() = default;

EffectVisualFilter::~EffectVisualFilter() = default;

bool EffectVisualFilter::Create(const char* json_params) {
  DCHECK(json_params);
  std::string json_str(json_params ? json_params : "");
  LOG(INFO) << "[effect] init json:" << json_str;
  try {
    auto j = nlohmann::json::parse(json_str);

    if (j.contains("composers") && j["composers"].is_array()) {
      for (auto& e : j["composers"]) {
        init_params_.composers.emplace_back(e.get<std::string>());
      }
    }

    if (j.contains("tags") && j["tags"].is_array()) {
      for (auto& e : j["tags"]) {
        init_params_.tags.emplace_back(e.get<std::string>());
      }
    }

    if (j.contains("backgroundPath") && j["backgroundPath"].is_string()) {
      init_params_.background_path = j["backgroundPath"].get<std::string>();
    }

    if (j.contains("backgroundKey") && j["backgroundKey"].is_string()) {
      init_params_.background_key = j["backgroundKey"].get<std::string>();
    }

    init_params_.use_empty_frame_before_effect_process = false;
    if (j.contains("useEmptyFrameBeforeEffectProcess") &&
        j["useEmptyFrameBeforeEffectProcess"].is_boolean()) {
      init_params_.use_empty_frame_before_effect_process =
          j["useEmptyFrameBeforeEffectProcess"].get<bool>();
    }
  } catch (const std::exception& e) {
    LOG(INFO) << "parse effect init param failed:" << e.what()
              << " json string: " << json_str;
    return false;
  }

  return true;
}

void EffectVisualFilter::Destroy() {
  ;
}

MediaSDKString EffectVisualFilter::GetProperty(const char* key) {
  return {};
}

bool EffectVisualFilter::SetProperty(const char* key, const char* json) {
  using namespace effect_visual_filter;

  DCHECK(processor_);
  DCHECK(key);
  DCHECK(json);

  if (!processor_) {
    LOG(ERROR) << "[effect] processor empty";
    return false;
  }

  if (!key) {
    LOG(ERROR) << "[effect] key empty";
    return false;
  }

  std::string json_data = json ? std::string(json) : std::string();
  LOG(INFO) << "[effect] property key:" << key << ", json:" << json_data;

  if (auto it = kComposerExecs.find(key); it != kComposerExecs.end()) {
    auto task = (it->second)(json_data);
    if (task) {
      processor_->ApplyComposerTask(std::move(task));
      return true;
    }
  } else {
    LOG(WARNING) << "[effect] property not process key:" << key;
  }
  return false;
}

MediaSDKString EffectVisualFilter::Action(const char* action,
                                          const char* json_params) {
  DCHECK(action);
  if (!action) {
    NOTREACHED() << "[Effect] Action is empty";
    return MediaSDKString();
  }

  if (0 == strcmp(action, effect_visual_filter::kComposerGetExclusionKey)) {
    if (!json_params) {
      NOTREACHED() << "[Effect] Action param empty for aciton" << action;
      return MediaSDKString();
    }
    return MediaSDKString(GetComposerExclusion(action, json_params));
  } else if (0 == strcmp(action, effect_visual_filter::kActionGetEffectFps)) {
    return MediaSDKString(GetEffectStatistic(action));
  }
  return MediaSDKString();
}

void EffectVisualFilter::InitGraphicsResource(VisualFilterProxy* proxy) {
  proxy_ = proxy;
  if (!processor_) {
    uint32_t effect_destroy_wait_ms = GetEffectDestoryWaitTimeoutMSFromAB();

    EffectConfig effect_config;
    GetEffectConfigFromAB(effect_config);
    // create processor_
    processor_ = std::make_unique<EffectProcessor>(
        effect_destroy_wait_ms,
        init_params_.use_empty_frame_before_effect_process, this,
        effect_config);

    // init composer
    nlohmann::json init_composers = {{"paths", init_params_.composers},
                              {"tags", init_params_.tags}};
    processor_->SetNecessaryComposers(init_params_.composers);
    nlohmann::json init_bk = {{"path", init_params_.background_path},
                              {"key", init_params_.background_key}};
    if (init_params_.composers.empty() &&
        init_params_.background_path.empty() && init_params_.background_key.empty()) {
      processor_->NotifyReady();
    }
    processor_->ApplyComposerTask(
        ComposerTask::CreateSetTask(init_composers.dump()));
    processor_->ApplyComposerTask(
        ComposerTask::CreateBackgroundTask(init_bk.dump()));
  }
}

void EffectVisualFilter::ReleaseGraphicsResource() {
  proxy_ = nullptr;
  if (processor_) {
    // If `Destory` returns a failure, it indicates that the Effect thread is
    // likely stuck. In this case, a evasion strategy is used to save processor_
    // and make it leak.
    if (!processor_->Destory()) {
      EffectProcessorDestoryHolder::GetInstance().AddWillDestoryEffectProcessor(
          std::move(processor_));
    }
    processor_.reset();
  }
}

void EffectVisualFilter::ClearVideoCache() {
  if (processor_) {
    processor_->ClearResultCache();
  }
}

graphics::Texture* EffectVisualFilter::Process(
    graphics::Texture* texture,
    const VisualFilterTransform& texture_trans,
    int64_t timestamp) {
  if (!texture) {
    return nullptr;
  }

  frame_calc_.AddFrame(1);
  in_fps_calc_.AddFrame(1);

  if (!processor_) {
    return texture;
  }
  return processor_->Process(texture, texture_trans, timestamp);
}

int64_t EffectVisualFilter::GetProcessDelayNS() {
  if (!processor_) {
    return 0;
  }
  return processor_->GetProcessDelayNS();
}

void EffectVisualFilter::Notify(const std::string& notify,
                                const std::string& json_data) {
  if (!proxy_) {
    return;
  }
  proxy_->SendNotify(this, MediaSDKString(notify), MediaSDKString(json_data));
}

void EffectVisualFilter::OnStateChanged(bool success) {
  if (!proxy_) {
    return;
  }
  proxy_->ReportFilterSourceEvent(VideoFilterSourceEvent{
      effect_visual_filter::kEffectVisualFilterName,
      static_cast<int>(FILTER_EVENT_TYPE::kStatus), success, ""});
}

void EffectVisualFilter::Profilter(const char* name, int64_t period_ns) {
  if (!proxy_) {
    return;
  }
  proxy_->ReportProfilterData(name, period_ns);
}

void EffectVisualFilter::BeginCostProfilter(const char* name,
                                            int64_t begin_ns) {
  if (!proxy_) {
    return;
  }
  proxy_->ReportBeginCostProfilterData(name, begin_ns);

}

void EffectVisualFilter::CostProfilter(const char* name, int64_t period_ns) {
  if (!proxy_) {
    return;
  }
  proxy_->ReportCostProfilterData(name, period_ns);
}

int EffectVisualFilter::GetRenderFps() {
  if (!proxy_) {
    return 0;
  }
  return proxy_->GetModelSettingfps();
}

std::tuple<bool, std::string> EffectVisualFilter::GetAlgoAndABConfig() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return std::make_tuple(false, "");
  }

  auto ab_info = proxy_->GetABConfig(kABEffectKey).ToString();
  DCHECK(!ab_info.empty());

  nlohmann::json effect_json = {
      {"effect_directory", ""}, {"license", "mediasdk"}, {"log_level", 1}};
  bool expect_algo = false;
  std::string ab_config;
  try {
    nlohmann::json ab_json = nlohmann::json::parse(ab_info);
    effect_json.merge_patch(ab_json);
    ab_config = effect_json.dump();
  } catch (const std::exception& e) {
    LOG(ERROR) << "Catch exception: " << e.what()
               << " json string: " << ab_info;
  }
  if (effect_json.contains("configs") &&
      effect_json.at("configs").contains(
          "enable_useEffectProcessor_ind_context")) {
    expect_algo = effect_json.at("configs")
                      .at("enable_useEffectProcessor_ind_context")
                      .get<bool>();
  }
  return std::make_tuple(expect_algo, ab_config);
}

uint32_t EffectVisualFilter::GetEffectDestoryWaitTimeoutMSFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return kEffectDestroyWaitTimeoutMS;
  }

  uint32_t use_timeout = 0;
  std::string ab_info = proxy_->GetABConfig(kMediasdkKey).ToString();
  DCHECK(!ab_info.empty());

  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);
    int32_t timeout_ms =
        mediasdk_json["effect_close_wait_time_ms"].get<int32_t>();
    if (timeout_ms > 0) {
      use_timeout = timeout_ms;
    }
    LOG(INFO) << "[Effect] find effect_close_wait_time_ms:" << timeout_ms
              << " , use_timeout: " << use_timeout;
  } catch (const std::exception& e) {
    LOG(ERROR) << "[Effect] found effect_close_wait_time_ms failed: "
               << e.what();
  }
  if (0 == use_timeout) {
    use_timeout = kEffectDestroyWaitTimeoutMS;
    LOG(INFO) << "[Effect] use default effect_close_wait_time_ms:"
              << use_timeout;
  }
  return use_timeout;
}

bool EffectVisualFilter::GetGLContextEnableDownGradeFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return true;
  }

  bool enable_down_grade = true;
  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());

  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);
    enable_down_grade =
        mediasdk_json["gl_context_enable_down_grade"].get<bool>();
  } catch (const std::exception& e) {
    LOG(INFO) << "[Effect] found gl_context_enable_down_grade failed:"
              << e.what() << ", use default value";
  }
  LOG(INFO) << "[Effect] gl_context_enable_down_grade:" << enable_down_grade;
  return enable_down_grade;
}

bool EffectVisualFilter::GetEnableANGLELogFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return true;
  }

  bool enable_angle_log = false;
  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());

  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);
    enable_angle_log = mediasdk_json.value("enable_angle_log", false);
  } catch (const std::exception& e) {
    LOG(INFO) << "[Effect] found genable_angle_log failed:"
              << e.what() << ", use default value";
  }
  LOG(INFO) << "[Effect] gl_context_enable_down_grade:" << enable_angle_log;
  return enable_angle_log;
}

bool EffectVisualFilter::GetEnableEffectFirstFrameStatisticsFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return true;
  }

  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());
  bool enable_first_frame_statistics = true;
  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);

    enable_first_frame_statistics =
        mediasdk_json.value("enable_first_frame_statistics", true);

  } catch (const std::exception& e) {
    LOG(INFO) << "[Effect] found enable_first_frame_statistics failed:"
              << e.what();
  }

  LOG(INFO) << "[Effect] enable effect first frame statistics: "
            << enable_first_frame_statistics;

  return enable_first_frame_statistics;
}

bool EffectVisualFilter::GetEnableHideOriginFrameFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return true;
  }

  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());
  bool enable_hide_origin_frame = false;
  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);

    enable_hide_origin_frame =
        mediasdk_json.value("enable_hide_origin_frame", false);

  } catch (const std::exception& e) {
    LOG(INFO) << "[Effect] found enable_hide_origin_frame failed:"
              << e.what();
  }

  LOG(INFO) << "[Effect] enable hide origin frame: "
            << enable_hide_origin_frame;

  return enable_hide_origin_frame;
}

bool EffectVisualFilter::GetPrecompileShaderFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return true;
  }

  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());
  bool enable_effect_precompiled_shader = false;
  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);

    enable_effect_precompiled_shader =
        mediasdk_json.value("enable_effect_precompiled_shader", false);

  } catch (const std::exception& e) {
    LOG(INFO)
        << "[Effect] found enable_effect_precompiled_shader failed:"
        << e.what();
  }

  LOG(INFO) << "[Effect] enable effect precomiling shader: "
            << enable_effect_precompiled_shader;

  return enable_effect_precompiled_shader;
}

bool EffectVisualFilter::GetEnableEffectStutteringStatusFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return true;
  }

  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());
  bool enable_effect_stuttering_status_statistics = false;
  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);

    enable_effect_stuttering_status_statistics =
        mediasdk_json.value("enable_effect_stuttering_status_statistics", true);

  } catch (const std::exception& e) {
    LOG(INFO) << "[Effect] found enable_effect_stuttering_status_statistics failed:"
              << e.what();
  }

  LOG(INFO) << "[Effect] enable effect stuttering status statistics: "
            << enable_effect_stuttering_status_statistics;

  return enable_effect_stuttering_status_statistics;
}

bool EffectVisualFilter::GetUseInterfaceWithTagFromAB() {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return true;
  }

  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());
  bool use_interface_with_tag = false;
  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);

    use_interface_with_tag =
        mediasdk_json.value("use_interface_with_tag", false);

  } catch (const std::exception& e) {
    LOG(INFO)
        << "[Effect] found use_interface_with_tag failed:"
        << e.what();
  }

  LOG(INFO) << "[Effect] use_interface_with_tag: "
            << use_interface_with_tag;

  return use_interface_with_tag;
}

std::tuple<bool, uint8_t> EffectVisualFilter::GetANGLEProgramCacheConfigFromAB(
    std::vector<std::pair<int, int>>& angle_config) {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return std::make_tuple(false, 0);
  }
  std::string ab_info = proxy_->GetABConfig(kEffectFilterKey).ToString();
  DCHECK(!ab_info.empty());

  bool enable_angle_program_cache = false;
  uint8_t angle_program_cache_gles_version = 0;

  try {
    nlohmann::json mediasdk_json = nlohmann::json::parse(ab_info);

    if (const auto& program_cache_json =
            mediasdk_json.find("angle_program_cache");
        program_cache_json != mediasdk_json.end()) {
      enable_angle_program_cache = program_cache_json.value().value(
          "enable", enable_angle_program_cache);
      angle_program_cache_gles_version = program_cache_json.value().value(
          "gles_version", angle_program_cache_gles_version);
    }


    if (const auto& anble_ab_json =
        mediasdk_json.find("anble_ab_json");
        anble_ab_json != mediasdk_json.end()) {
        for (const auto& [key, value] : anble_ab_json.value().items()) {
        int intKey = std::stoi(key);
        if (value.is_boolean()) {
          // 将布尔值转换为整数 (false=0, true=1)
          int intValue = value.get<bool>() ? 1 : 0;
          // 添加到结果向量
          angle_config.emplace_back(intKey, intValue);
        }
      }
    }
  } catch (const std::exception& e) {
    LOG(INFO) << "[Effect] found angle program cache failed:" << e.what();
  }

  LOG(INFO) << "[Effect] enable angle program cache: "
            << enable_angle_program_cache << ", gles version binary value: "
            << std::bitset<8>(angle_program_cache_gles_version);
  return std::make_tuple(enable_angle_program_cache,
                         angle_program_cache_gles_version);
}

bool EffectVisualFilter::GetEffectConfigFromAB(EffectConfig& effect_config) {
  if (!proxy_) {
    LOG(ERROR) << "[Effect] proxy null";
    return false;
  }

  // sdk config
  auto [algo, ab_config] = GetAlgoAndABConfig();
  effect_config.sdk_config.expect_algo = algo;
  effect_config.sdk_config.lib_effect_ab_config = ab_config;
  effect_config.sdk_config.enable_first_frame_statistics =
      GetEnableEffectFirstFrameStatisticsFromAB();
  effect_config.sdk_config.enable_hide_origin_frame =
      GetEnableHideOriginFrameFromAB();
  effect_config.sdk_config.enable_stuttering_status_statistics =
      GetEnableEffectStutteringStatusFromAB();
  effect_config.sdk_config.enable_precompiled_shader =
      GetPrecompileShaderFromAB();
  effect_config.sdk_config.use_interface_with_tag =
      GetUseInterfaceWithTagFromAB();

  // context config
  effect_config.context_config.enable_down_grade =
      GetGLContextEnableDownGradeFromAB();

  auto [enable_program_cache, program_cache_glse_version] =
      GetANGLEProgramCacheConfigFromAB(effect_config.context_config.angle_ab_configs);
  effect_config.context_config.enable_angle_program_cache =
      enable_program_cache;
  effect_config.context_config.angle_program_cache_gles_version =
      program_cache_glse_version;
  effect_config.context_config.enable_angle_log = GetEnableANGLELogFromAB();

  return true;
}

std::string EffectVisualFilter::GetComposerExclusion(
    const std::string& action,
    const std::string& action_param) {
  int is_exclusion = 0;
  try {
    nlohmann::json param = nlohmann::json::parse(action_param);
    std::string node_path = param["nodePath"].get<std::string>();
    std::string node_tag = param["nodeTag"].get<std::string>();
    is_exclusion =
        processor_ ? processor_->GetComposerExclusive(node_path, node_tag) : 0;
  } catch (const std::exception& e) {
    LOG(ERROR) << "exec " << action << " failed:" << e.what()
               << ",json:" << action_param;
  }
  nlohmann::json return_json = {
      {"action", action},
      {"isExclusion", is_exclusion},
  };
  return return_json.dump();
}

std::string EffectVisualFilter::GetEffectStatistic(const std::string& action) {
  double achieve_rate = -1.0;
  float effect_fps = 0.f;
  float in_effect_fps = 0.f;
  double tick_achieve_rate_ = -1.0;
  if (processor_) {
    effect_fps = processor_->GetEffectFps();
    in_effect_fps = in_fps_calc_.CalculateFPS();
    int64_t input_frame_diff = frame_calc_.CalculateFrameDiff();
    int64_t output_frame_diff = processor_->GetLastEffectFrame();
    if (input_frame_diff > 0 && output_frame_diff >= 0) {
      achieve_rate =
          (static_cast<double>(output_frame_diff)) / input_frame_diff;
    }
    int64_t tick_new_frame_diff = processor_->GetTickNewFrame();
    if (input_frame_diff > 0 && tick_new_frame_diff > 0) {
      tick_achieve_rate_ =
          (static_cast<double>(tick_new_frame_diff)) / input_frame_diff;
    }
  }
  nlohmann::json return_json = {{"action", action},
                                {"effectFps", effect_fps},
                                {"effectAchieveRate", achieve_rate},
                                {"inEffectFps", in_effect_fps},
                                {"effectTickAchieveRate", tick_achieve_rate_}};
  return return_json.dump();
}

}  // namespace mediasdk
