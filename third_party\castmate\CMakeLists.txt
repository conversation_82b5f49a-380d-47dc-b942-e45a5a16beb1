include(FetchContent)
Set(FETCHCONTENT_QUIET FALSE)

add_library(castmate IMPORTED SHARED GLOBAL)
if (WIN32)
    FetchContent_Declare(
            castmate
            URL https://tosv.byted.org/obj/mediasdkclientproduct/streaming_media_global/media_sdk/prebuilt/castmate-v4.1.2.39-overseas-x86-64.zip
            URL_HASH SHA256=2641627375250872AB5721E686224098BF14FAA76DBEF81A62B70F5587636BE7
            SOURCE_SUBDIR somewhere_that_not_exists
            EXCLUDE_FROM_ALL
    )
    FetchContent_MakeAvailable(castmate)
    target_include_directories(castmate INTERFACE ${castmate_SOURCE_DIR}/include)

    if (CMAKE_SIZEOF_VOID_P EQUAL 8)
        set(CASTMATE_LIB_DIR ${castmate_SOURCE_DIR}/lib/windows/overseas/x86_64/Release)
    else ()
        set(CASTMATE_LIB_DIR ${castmate_SOURCE_DIR}/lib/windows/overseas/x86/Release)
    endif ()

    set_target_properties(
            castmate
            PROPERTIES
            IMPORTED_IMPLIB ${CASTMATE_LIB_DIR}/castmate.lib
            IMPORTED_LOCATION ${CASTMATE_LIB_DIR}/castmate.dll
    )

    file(GLOB ROOT_DEPS_RAW "${CASTMATE_LIB_DIR}/*.dll")
    set(ROOT_DEPS)
    foreach (DEP ${ROOT_DEPS_RAW})
        if (NOT DEP MATCHES ".*(castmate|cllamaengine|jpeg62|RTCFFmpeg|openh264-4|libcrypto-3-x64|libssl-3-x64|libcurl)\\.dll$")
            list(APPEND ROOT_DEPS ${DEP})
        endif ()
    endforeach ()

    mediasdk_declare_resource(castmate "" ${ROOT_DEPS})

    file(GLOB WIRED_DEPS
            "${CASTMATE_LIB_DIR}/wired/*.dll"
            "${CASTMATE_LIB_DIR}/wired/*.exe"
    )
    mediasdk_declare_resource(castmate "wired" ${WIRED_DEPS})

    file(GLOB CAMERA_DEPS
            "${CASTMATE_LIB_DIR}/camera/*.dll"
            "${CASTMATE_LIB_DIR}/camera/*.exe"
    )
    mediasdk_declare_resource(castmate "camera" ${CAMERA_DEPS})

endif ()
