#include "dshow_visual_source.h"

#include <Shlwapi.h>
#include <base/logging.h>
#include <base/strings/utf_string_conversions.h>
#include <mediasdk/public/plugin/plugin_defines.h>
#include "dshow_server_delegate.h"
#include "dshow_server_ipc_delegate.h"
#include "dshow_track_defines.h"
#include "dshow_visual_details.h"
#include "ff_av_player/ffmpeg_vframe_to_texture.h"
#include "math_helper.h"

namespace mediasdk {

constexpr int kCameraMaxCache = 3;

const char kMaxFrameQueueCountAnalogSectionName[] = "analog_visual_source";

DShowVisualSource::DShowVisualSource(VisualProxy* proxy)
    : proxy_(proxy),
      create_index_(-1),
      capture_(dynamic_cast<DShowVideoFrameNotify*>(this),
               dynamic_cast<DShowAudioFrameNotify*>(this),
               dynamic_cast<DShowOperationNotify*>(this)) {}

bool DShowVisualSource::Create(const char* json_params) {
  if (!json_params || !json_params[0]) {
    LOG(ERROR) << "json:null";
    return false;
  }

  nlohmann::json json_root;
  try {
    json_root = nlohmann::json::parse(json_params);
    if (json_root.empty()) {
      LOG(ERROR) << base::StringPrintf("json:%s", json_params);
      return false;
    }
    capture_type_ =
        json_root.value("type", dshow_visual_source::kCaptureTypeCamera);
  } catch (const std::exception& e) {
    LOG(ERROR) << "Catch exception: " << e.what()
               << " json string: " << json_params;
    return false;
  }

  ParseDShowRunError();
  capture_.SetCameraConfig(ParseCameraConfig());
  ReportDshowEvent(json_root);
  auto start_time = base::Time::Now();
  auto create_res = capture_.Create(capture_type_, json_root);
  auto end_time = base::Time::Now();

  auto duration = end_time - start_time;
  if (!create_res) {
    LOG(ERROR) << base::StringPrintf("create error:%s", json_params);
  }
  ReportDshowCreate(json_root, create_res, duration.InMilliseconds());
  detector_.SetDelegate(this);
  detector_.Start();
  return true;
}

void DShowVisualSource::Destroy() {
  capture_.Destroy();
}

bool DShowVisualSource::HasAudio() {
  return capture_.HasAudio();
}

bool DShowVisualSource::Prepare(int64_t timestamp) {
  detector_.OnPrepare();

  AutoCleanVideoBuffers(false);

  auto format = capture_.GetCaptureVideoFormat().GetFormat();

  frame_flip_v_.store(capture_.GetCaptureVideoFormat().GetFlipV());

  if (format == PixelFormat::kPixelFormatUnspecified) {
    return false;
  }

  if (format != PixelFormat::kPixelFormatMJPEG) {
    return true;
  }

  bool success = false;
  ColorSpace cs = ColorSpace::kColorSpaceUnspecified;
  VideoRange vr = VideoRange::kVideoRangeUnspecified;
  int64_t pts = 0;
  if (mjpeg_frame_handler_) {
    success = mjpeg_frame_handler_->Prepare(proxy_->GetDevice(), cs, vr, pts);
  }

  if (success) {
    if (IsFormatChanged(true, cs, vr)) {
      UpdateFormat(true, cs, vr);
      LogCurrentFormat();
    }

    texture_ts_balancer_.UpdateTimestampNS(pts);
  }

  return success;
}

void DShowVisualSource::Convert() {
  auto format = capture_.GetCaptureVideoFormat().GetFormat();
  if (format == PixelFormat::kPixelFormatMJPEG && mjpeg_frame_handler_) {
    mjpeg_frame_handler_->Convert();
  }
}

graphics::Texture* DShowVisualSource::GetTexture() {
  texture_ts_balancer_.WillGetTimestamp();

  auto format = capture_.GetCaptureVideoFormat().GetFormat();
  if (format == PixelFormat::kPixelFormatUnspecified) {
    return nullptr;
  }

  graphics::Texture* tex = nullptr;
  if (format != PixelFormat::kPixelFormatMJPEG) {
    if (proxy_) {
      tex = proxy_->GetLatestTextureFromQueue();
      if (proxy_->IsLastFrameConvertSuccess()) {
        texture_ts_balancer_.UpdateTimestampNS(
            proxy_->GetLatestTextureTimestampNSFromQueue());
      }
    }
  } else {
    if (mjpeg_frame_handler_) {
      tex = mjpeg_frame_handler_->GetTexture();
    }
  }

  return tex;
}

int64_t DShowVisualSource::GetTextureTimestampNS() {
  auto format = capture_.GetCaptureVideoFormat().GetFormat();
  if (format == PixelFormat::kPixelFormatUnspecified) {
    return 0;
  }
  return texture_ts_balancer_.GetTimestampNSByBalance();
}

bool DShowVisualSource::Pause() {
  is_paused_ = true;

  if (proxy_) {
    proxy_->CleanVideoFrame();
  }

  if (mjpeg_frame_handler_) {
    mjpeg_frame_handler_->CleanFrame();
  }

  detector_.Pause();
  return capture_.Pause();
}

bool DShowVisualSource::Continue() {
  is_paused_ = false;

  detector_.Start();
  return capture_.Continue();
}

bool DShowVisualSource::IsPaused() {
  return capture_.IsPaused();
}

MediaSDKString DShowVisualSource::GetProperty(const char* key) {
  if (!key || !key[0]) {
    return {};
  }
  nlohmann::json json_root;
  if (_stricmp(key, dshow_visual_source::kDShowCameraVideoProCamp) == 0) {
    auto info = capture_.GetVideoProcAmp();
    nlohmann::json json_root_tmp, json_item;
    for (const auto& iter : info) {
      json_item["default_value"] = iter.default_value;
      json_item["value"] = iter.value;
      json_item["min_value"] = iter.min_value;
      json_item["max_value"] = iter.max_value;
      json_item["step"] = iter.step;
      json_item["default_flag"] = iter.default_flag;
      json_item["flag"] = iter.flag;
      json_item["property_index"] = (int32_t)iter.property_index;
      json_root_tmp.push_back(json_item);
    }
    json_root[dshow_visual_source::kDShowCameraVideoProCamp] = json_root_tmp;
  } else if (_stricmp(key, dshow_visual_source::kDShowCameraVideoControl) ==
             0) {
    auto info = capture_.GetCameraControl();
    nlohmann::json json_root_tmp, json_item;
    for (const auto& iter : info) {
      json_item["default_value"] = iter.default_value;
      json_item["value"] = iter.value;
      json_item["min_value"] = iter.min_value;
      json_item["max_value"] = iter.max_value;
      json_item["step"] = iter.step;
      json_item["default_flag"] = iter.default_flag;
      json_item["flag"] = iter.flag;
      json_item["property_index"] = (int32_t)iter.property_index;
      json_root_tmp.push_back(json_item);
    }
    json_root[dshow_visual_source::kDShowCameraVideoControl] = json_root_tmp;
  } else if (_stricmp(key, dshow_visual_source::kDShowCameraState) == 0) {
    FILTER_STATE state = State_Stopped;
    capture_.IsRunningOrPause(state);
    auto create_status = capture_.GetLastCreateError();
    nlohmann::json item;
    item["step"] = (int32_t)create_status.create_step;
    item["error"] = create_status.last_error;
    json_root[dshow_visual_source::kDShowCameraState] = (int32_t)state;
    json_root[dshow_visual_source::kDShowCameraCreateState] = item;
    json_root[dshow_visual_source::kDShowCameraIsReady] =
        (state == State_Running) || (state == State_Paused);
  } else if (_stricmp(key, dshow_visual_source::kDShowCameraAfterDropFps) ==
             0) {
    json_root[dshow_visual_source::kDShowCameraAfterDropFps] =
        GetAfterDropFps();
  }
  return {json_root.dump()};
}

bool DShowVisualSource::SetProperty(const char* key, const char* json) {
  if (!key || !key[0]) {
    return false;
  }
  if (!json || !json[0]) {
    return false;
  }
  try {
    nlohmann::json json_root = nlohmann::json::parse(json);
    if (json_root.empty()) {
      return false;
    }
    if (_stricmp(key, dshow_visual_source::kDShowCameraVideoProCamp) == 0) {
      long property_index = json_root.value("property_index", -1);
      long flag = json_root.value("flag", 0);
      long value = json_root.value("value", 0);
      return capture_.SetVideoProcAmp((VideoProcAmpProperty)property_index,
                                      value, flag);
    }
    if (_stricmp(key, dshow_visual_source::kDShowCameraVideoControl) == 0) {
      long property_index = json_root.value("property_index", -1);
      long flag = json_root.value("flag", 0);
      long value = json_root.value("value", 0);
      return capture_.SetCameraControl((CameraControlProperty)property_index,
                                       value, flag);
    }
    if (_stricmp(key, dshow_visual_source::kDShowCameraLimitRate) == 0) {
      int limit_rate = static_cast<int>(json_root.value("limit_rate", 0.0f));
      capture_.SetLimitCaptureFPS(limit_rate);
      return true;
    }
    if (_stricmp(key, dshow_visual_source::kDShowCameraStartDetectVideoRange) ==
        0) {
      int32_t detect_interval = json_root["detect_interval"];
      int32_t limited_detect_count = json_root["limited_detect_count"];
      capture_.StartVideoRangeDetect(detect_interval, limited_detect_count);
      return true;
    }
    if (_stricmp(key, dshow_visual_source::kDShowCameraStopDetectVideoRange) ==
        0) {
      capture_.StopVideoRangeDetect();
      return true;
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "Catch exception: " << e.what() << " json string: " << json;
  }
  return false;
}

void DShowVisualSource::OnMouseEvent(const char* json_event) {}

void DShowVisualSource::OnKeyboardEvent(const char* json_event) {}

const char* DShowVisualSource::GetAudioSourceName() {
  return dshow_visual_source::GetPluginName().data();
}

bool DShowVisualSource::Action(const char* json_params) {
  return false;
}

void DShowVisualSource::InitGraphicsResource() {
  if (!proxy_) {
    return;
  }

  mjpeg_frame_handler_ = std::make_unique<MjpegFrameHandler>();

  proxy_->InitVisualFrameQueue(kVisualFrameQueueConverterTypeYuv, 0);
  if (capture_type_ == dshow_visual_source::kCaptureTypeCamera) {
    if (proxy_) {
      if (mjpeg_frame_handler_) {
        mjpeg_frame_handler_->SetBufferedFramesPlanCount(kCameraMaxCache);
      }
      proxy_->SetVideoFrameQueueMaxCount(kCameraMaxCache);
      LOG(INFO) << "[DShowVisualSource] set buffer:" << kCameraMaxCache;
    }
  } else if (capture_type_ == dshow_visual_source::kCaptureTypeAnalog) {
    if (proxy_) {
      auto max_frame_queue_count = proxy_->GetConfiguredMaxFrameQueueCount(
          kMaxFrameQueueCountAnalogSectionName);
      proxy_->SetVideoFrameQueueMaxCount(max_frame_queue_count);
      LOG(INFO) << "DShowVisualSource analog"
                << ": Visual frame queue max count: " << max_frame_queue_count;
    }
  }
}

void DShowVisualSource::ReleaseGraphicsResource() {
  AutoCleanVideoBuffers(true);
  int64_t max_delay_count = texture_ts_balancer_.GetMaxDelayCount();
  texture_ts_balancer_.Clear();

  // Log and report
  if (proxy_) {
    const char* name = GetName();
    std::string name_str = name ? name : "NAN";
    const char* sub_name = capture_.GetSubTypeName();
    std::string sub_name_str = sub_name ? sub_name : "NAN";

    proxy_->ReportSourceEvent(
        VideoSourceEvent{name, kVisualSourceMaxDelayCountEvent,
                         static_cast<int>(max_delay_count), sub_name_str});
    LOG(INFO) << "[" << name_str << "] [" << sub_name_str
              << "] max_delay_count:" << max_delay_count;
  }

  mjpeg_frame_handler_.reset();
}

const char* DShowVisualSource::GetName() const {
  return dshow_visual_source::GetPluginName().data();
}

bool DShowVisualSource::Reopen(const char* json) {
  if (!json || !json[0]) {
    LOG(ERROR) << "params error!";
    return false;
  }
  LOG(INFO) << base::StringPrintf("reopen dshow:%s", json);
  nlohmann::json json_root;
  try {
    json_root = nlohmann::json::parse(json);
    if (json_root.empty()) {
      LOG(ERROR) << "params json error!";
      return false;
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "Catch exception: " << e.what() << " json string: " << json;
  }

  capture_.Destroy();
  AutoCleanVideoBuffers(true);

  auto start_time = base::Time::Now();
  auto create_res = capture_.Create(json_root);
  auto end_time = base::Time::Now();

  auto duration = end_time - start_time;
  ReportDshowCreate(json_root, create_res, duration.InMilliseconds());

  if (!create_res) {
    LOG(ERROR) << "reopen error!";
    return false;
  }

  is_paused_ = false;
  ReportDshowEvent(json_root);
  return true;
}

bool DShowVisualSource::NeedSyn() {
  return false;
}

void DShowVisualSource::SetDeviceVolume(const float volume) {}

float DShowVisualSource::GetDeviceVolume() {
  return 0.0;
}

void DShowVisualSource::SetDeviceMute(bool is_mute) {}

bool DShowVisualSource::GetDeviceMute() {
  return false;
}

void DShowVisualSource::SetDeviceUsedQPC(bool used) {}

bool DShowVisualSource::GetDeviceUsedQPC() {
  return false;
}

bool DShowVisualSource::EnableAlpha() {
  return true;
}

MSTransform DShowVisualSource::GetExtraTransform() const {
  MSTransform transform = EMPTY_MSTRANSFORM;
  transform.flip_v = frame_flip_v_.load();
  return transform;
}

float DShowVisualSource::GetFps() {
  return rate_calc_.CalculateFPS();
}

void DShowVisualSource::OnFrameCollected() {
  rate_calc_.AddFrame();
}

void DShowVisualSource::OnVideoFrameStatus(const VideoCaptureFormat& format) {
  capture_.SetRenderFPS(GetRenderFPS());
  after_drop_rate_calc_.AddFrame();
  detector_.OnFrame();
  frame_flip_v_.store(format.GetFlipV());
}

void DShowVisualSource::OnVideoFrame(const VideoCaptureFormat& format,
                                     const VisualSourceFrame& frame) {
  if (IsFormatChanged(false, frame.color_space, frame.video_range)) {
    UpdateFormat(false, frame.color_space, frame.video_range);
    LogCurrentFormat();
  }

  OnVideoFrameStatus(format);

  if (proxy_ && !is_paused_) {
    proxy_->EnqueueVideoFrame(frame);
  }
}

void DShowVisualSource::OnVideoFrame(const VideoCaptureFormat& format,
                                     std::shared_ptr<AVFrame> frame) {
  OnVideoFrameStatus(format);

  frame->pts = nano_now();

  if (mjpeg_frame_handler_ && !is_paused_) {
    mjpeg_frame_handler_->PushFrame(std::move(frame));
  }
}

const char* DShowVisualSource::GetSubTypeName() const {
  return capture_.GetSubTypeName();
}

void DShowVisualSource::OnAudioFrame(const AudioFormat& format,
                                     const AudioSourceFrame& frame,
                                     uint64_t timestamp) {
  if (proxy_) {
    proxy_->OnAudio(format, frame);
  }
}

void DShowVisualSource::OnDShowContinueError(int error_code,
                                             const std::string& video_name) {
  nlohmann::json error_json;
  error_json["Operation"] = "Continue";
  error_json["VideoName"] = video_name;
  proxy_->ReportSourceEvent(
      VideoSourceEvent{GetName(), kVisualSourceOperationErrorEvent, error_code,
                       MediaSDKString(error_json.dump())});

  if (!dshow_run_error_) {
    return;
  }

  auto reopen_it =
      std::find(std::begin(dshow_run_error_->do_reopen_errors),
                std::end(dshow_run_error_->do_reopen_errors), error_code);
  auto occupy_it =
      std::find(std::begin(dshow_run_error_->device_occupied_errors),
                std::end(dshow_run_error_->device_occupied_errors), error_code);

  if (reopen_it != std::end(dshow_run_error_->do_reopen_errors)) {
    auto create_json = capture_.GetCreateJson();
    if (!create_json.empty()) {
      nlohmann::json json_root;
      json_root["event_type"] = "do_reopen_error";
      json_root["reopen_json"] = create_json;
      json_root["error_code"] = error_code;
      proxy_->SignalEvent(json_root.dump().c_str());
      LOG(INFO) << "DShowVisualSource need reopen for error:" << error_code
                << "(0x" << std::hex << error_code << ")";
    } else {
      LOG(WARNING) << "DShowVisualSource need reopen for error:" << error_code
                   << "(0x" << std::hex << error_code << ")"
                   << ", but not got create_json";
    }
  } else if (occupy_it != std::end(dshow_run_error_->device_occupied_errors)) {
    nlohmann::json json_root;
    json_root["event_type"] = "device_occupied_error";
    json_root["error_code"] = error_code;
    proxy_->SignalEvent(json_root.dump().c_str());
    LOG(INFO) << "DShowVisualSource device occupied error:" << error_code
              << "(0x" << std::hex << error_code << ")";
  } else {
    nlohmann::json json_root;
    json_root["event_type"] = "device_unknown_error";
    json_root["error_code"] = error_code;
    proxy_->SignalEvent(json_root.dump().c_str());
    LOG(INFO) << "DShowVisualSource device_unknown_error:" << error_code
              << "(0x" << std::hex << error_code << ")";
  }
}

void DShowVisualSource::OnDShowPauseError(int error_code,
                                          const std::string& video_name) {
  nlohmann::json error_json;
  error_json["Operation"] = "Pause";
  error_json["VideoName"] = video_name;
  proxy_->ReportSourceEvent(
      VideoSourceEvent{GetName(), kVisualSourceOperationErrorEvent, error_code,
                       MediaSDKString(error_json.dump())});
}

void DShowVisualSource::OnDShowControlState(CameraControlProperty pro,
                                            bool state) {
  nlohmann::json root_json;
  root_json["property"] = (int)pro;
  root_json["state"] = state;
  auto control_state = root_json.dump();

  nlohmann::json json_root;
  json_root["dshow_event"] = control_state;
  proxy_->SignalEvent(json_root.dump().c_str());

  LOG(INFO) << base::StringPrintf("[DShowCaptureBase] OnDShowControlState: \n\t%s",
                                  control_state.c_str());
}

void DShowVisualSource::OnFormatDetectResult(VideoRange vr) {
  nlohmann::json root_json;
  root_json["video_range_detect"] = (int)vr;
  auto video_range_detect = root_json.dump();

  nlohmann::json json_root;
  json_root["format_info"] = video_range_detect;
  proxy_->SignalEvent(json_root.dump().c_str());

  LOG(INFO) << base::StringPrintf("[DShowCaptureBase] OnFormatInfo: \n\t%s",
                                  video_range_detect.c_str());
}

void DShowVisualSource::OnStateChangeDetected(bool state) {
  nlohmann::json json_root;
  json_root["state"] = state;
  proxy_->SignalEvent(json_root.dump().c_str());
}

int DShowVisualSource::GetRenderFPS() const {
  if (proxy_) {
    return proxy_->GetRenderFPS();
  }
  return INT_MAX;
}

void DShowVisualSource::AutoCleanVideoBuffers(bool force) {
  int32_t create_index = capture_.GetCreateIndex();
  if (force || create_index != create_index_) {
    create_index_ = create_index;

    if (mjpeg_frame_handler_) {
      mjpeg_frame_handler_->CleanFrame();
    }

    capture_.CleanVideoTransFormBuffers();
    if (proxy_) {
      proxy_->CleanVideoFrame();
    }
  }
}

CameraConfig DShowVisualSource::ParseCameraConfig() const {
  if (!proxy_) {
    return {};
  }

  CameraConfig camera_config{};

  try {
    std::string camera_ab_config = proxy_->GetABConfig("camera").ToString();
    std::string mjpeg_ab_config =
        proxy_->GetABConfig("mjpeg_video_range").ToString();
    if (camera_ab_config.empty() && mjpeg_ab_config.empty()) {
      return camera_config;
    }

    if (!camera_ab_config.empty()) {
      nlohmann::json camera_config_json =
          nlohmann::json::parse(camera_ab_config);
      if (camera_config_json.contains("configs")) {
        camera_config.is_drop_frame_enable =
            camera_config_json["configs"].value("drop_frame", false);
        camera_config.use_pause =
            camera_config_json["configs"].value("use_pause", true);
      }
    }

    if (!mjpeg_ab_config.empty()) {
      nlohmann::json mjpeg_config_json = nlohmann::json::parse(mjpeg_ab_config);
      camera_config.mjpeg_video_range =
          mjpeg_config_json.value("set_video_range", false);
    }

  } catch (const std::exception& e) {
    LOG(ERROR) << "Catch exception: " << e.what();
  }

  return camera_config;
}

void DShowVisualSource::ParseDShowRunError() {
  dshow_run_error_.reset();
  if (!proxy_) {
    return;
  }

  try {
    std::string camera_ab_config = proxy_->GetABConfig("camera").ToString();
    LOG(INFO) << "camera_ab_config:" << camera_ab_config;
    nlohmann::json camera_config_json = nlohmann::json::parse(camera_ab_config);
    if (!camera_config_json.contains("dshow_run_errors")) {
      return;
    }
    DShowRunError run_error;
    auto& error_json = camera_config_json.at("dshow_run_errors");
    if (error_json.contains("do_reopen_errors") &&
        error_json.at("do_reopen_errors").is_array()) {
      run_error.do_reopen_errors =
          error_json.at("do_reopen_errors").get<std::vector<int32_t>>();
    }
    if (error_json.contains("device_occupied_errors") &&
        error_json.at("device_occupied_errors").is_array()) {
      run_error.device_occupied_errors =
          error_json.at("device_occupied_errors").get<std::vector<int32_t>>();
    }
    dshow_run_error_ = run_error;
  } catch (const std::exception& e) {
    LOG(ERROR) << "ParseDShowRunError Catch exception: " << e.what();
  }
}

void DShowVisualSource::ReportDshowEvent(const nlohmann::json& config) {
  std::string device_name;
  int format = 0;
  int resolution = 0;
  float rate = 0.0f;

  try {
    auto video = config["video"];
    if (video.contains("device_name")) {
      device_name = video["device_name"];
    }
    if (video.contains("format")) {
      format = video["format"];
    }
    if (video.contains("rate")) {
      rate = video["rate"];
    }
    if (video.contains("size_cx") && video.contains("size_cy")) {
      resolution = std::max(static_cast<int>(video["size_cx"]),
                            static_cast<int>(video["size_cy"]));
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "[DShowVisualSource] Failed to Parse config: " << e.what();
  }
  if (!device_name.empty()) {
    if (proxy_) {
      if (format > 0) {
        proxy_->ReportSourceEvent(VideoSourceEvent{
            GetName(), static_cast<int>(DshowReportType::kDshowFormat), format,
            device_name.c_str()});
      }
      if (IsGreaterThan(rate, 0.0f)) {
        proxy_->ReportSourceEvent(VideoSourceEvent{
            GetName(), static_cast<int>(DshowReportType::kDshowFps),
            static_cast<int>(rate), device_name});
      }
      if (resolution > 0) {
        proxy_->ReportSourceEvent(VideoSourceEvent{
            GetName(), static_cast<int>(DshowReportType::kDshowResolution),
            resolution, device_name});
      }
    }
  }
}

void DShowVisualSource::ReportDshowCreate(const nlohmann::json& config,
                                          bool is_ok,
                                          int duration) {
  std::string device_name;
  try {
    auto video = config["video"];
    if (video.contains("device_name")) {
      device_name = video["device_name"];
    }
    if (device_name.empty() && video.contains("device_id")) {
      device_name = video["device_id"];
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "[DShowVisualSource] Failed to Parse config: " << e.what();
  }
  if (is_ok) {
    if (proxy_) {
      proxy_->ReportSourceEvent(VideoSourceEvent{
          GetName(), static_cast<int>(DshowReportType::kDshowSueecss), duration,
          device_name.c_str()});
    }
  } else {
    if (proxy_) {
      proxy_->ReportSourceEvent(VideoSourceEvent{
          GetName(), static_cast<int>(DshowReportType::kDshowOpenError),
          duration, device_name.c_str()});
    }
  }
}

float DShowVisualSource::GetAfterDropFps() {
  return after_drop_rate_calc_.CalculateFPS();
}

bool DShowVisualSource::IsFormatChanged(
    bool mjpeg,
    mediasdk::ColorSpace color_space,
    mediasdk::VideoRange video_range) const {
  return (current_color_space_.mjpeg != mjpeg) ||
         current_color_space_.video_range != (int)video_range ||
         current_color_space_.color_space != (int)color_space;
}

void DShowVisualSource::UpdateFormat(bool mjpeg,
                                     mediasdk::ColorSpace color_space,
                                     mediasdk::VideoRange video_range) {
  current_color_space_.mjpeg = mjpeg;
  current_color_space_.color_space = color_space;
  current_color_space_.video_range = video_range;
}

void DShowVisualSource::LogCurrentFormat() const {
  std::string video_range = "invalid";
  if (current_color_space_.video_range == (int)kVideoRangePartial) {
    video_range = "kVideoRangePartial";
  } else if (current_color_space_.video_range == (int)kVideoRangeFull) {
    video_range = "kVideoRangeFull";
  }
  std::string color_space = "invalid";
  if (current_color_space_.color_space == (int)kColorSpaceBT709) {
    color_space = "kColorSpaceBT709";
  } else if (current_color_space_.color_space == (int)kColorSpaceBT601) {
    color_space = "kColorSpaceBT601";
  }
  LOG(INFO) << "[DShowVisualSource] Frame Format: MJPEG: "
            << (current_color_space_.mjpeg ? "true" : "false")
            << " video_range: " << video_range
            << " color_space: " << color_space;
}

//////////////////////////////////////////////////////////////////////////
const PluginInfo* GetPluginInfo() {
  static PluginInfo info;
  info.id = dshow_visual_source::GetPluginID();
  info.type = PluginType::kVisual;
  info.name = dshow_visual_source::GetPluginName();
  info.desc = dshow_visual_source::GetPluginDesc();

  return &info;
}

bool PluginGlobalInit(PluginGlobalProxy* global_proxy) {
  DShowServerDelegate::RunDShowServerHost();
  DShowServerIPCDelegate::Instance().Init();
  return true;
}

void PluginGlobalUnInit() {
  DShowServerIPCDelegate::Instance().Uninit();
}

VisualSource* CreateVisualSource(VisualProxy* proxy, const char* json_params) {
  auto* source = new DShowVisualSource(proxy);
  if (source->Create(json_params)) {
    return source;
  }
  delete source;
  return nullptr;
}

void DestroyVisualSource(VisualSource* source) {
  if (auto* dshow_source = dynamic_cast<DShowVisualSource*>(source)) {
    dshow_source->Destroy();
    delete source;
  }
}

MediaSDKStringData EnumVideoInput(const char* json) {
  try {
    const auto json_root = nlohmann::json::parse(json);
    if (json_root.empty()) {
      return MediaSDKString().Detach();
    }
    const int32_t type = json_root.value("type", 0);
    DShowServerDelegate enum_imp;
    if (type == dshow_visual_source::kCaptureTypeCamera) {
      std::string json_string;
      if (json_root.contains("method")) {
        const int32_t method = json_root.value(
            "method", dshow_visual_source::kCameraEnumMethodCameraAndFormats);
        if (method == dshow_visual_source::kCameraEnumMethodCameraAndFormats) {
          json_string = enum_imp.GetVideoDevicesAndFormats();
        } else if (method == dshow_visual_source::
                                 kCameraEnumMethodCameraAndTargetFormat) {
          const std::wstring device_id =
              base::UTF8ToWide(json_root.value("device_id", ""));
          json_string = enum_imp.GetVideoDevicesAndTargetFormat(device_id);
        }
      } else {
        json_string = enum_imp.GetVideoDevices();
      }
      LOG(INFO) << base::StringPrintf("camera device:%s", json_string.c_str());
      return MediaSDKString(json_string).Detach();
    } else if (type == dshow_visual_source::kCaptureTypeAnalog) {
      const auto json_string = enum_imp.GetAnalogDevices();
      LOG(INFO) << base::StringPrintf("analog device:%s", json_string.c_str());
      return MediaSDKString(json_string).Detach();
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "Catch exception: " << e.what() << " json string: " << json;
  }
  return MediaSDKString().Detach();
}

MediaSDKStringData EnumFormatSafe(const char* device_id, const int32_t type) {
  DShowDeviceName name;
  name.id = base::UTF8ToWide(device_id);

  if (type == kEnumFormatTypeAudio) {
    const auto json_string = GetAudioDeviceFormats(name.id, name.name);
    LOG(INFO) << base::StringPrintf("device_id:%s, ret:%s", device_id,
                                    json_string.c_str());
    return MediaSDKString(json_string).Detach();
  } else if (type == kEnumFormatTypeVideo) {
    const auto json_string = GetVideoDeviceFormats(name.id, name.name);
    LOG(INFO) << base::StringPrintf("device_id:%s, ret:%s", device_id,
                                    json_string.c_str());
    return MediaSDKString(json_string).Detach();
  }
  return MediaSDKString().Detach();
}

MediaSDKStringData EnumFormat(const char* device_id, const int32_t type) {
  if (!device_id || !device_id[0]) {
    return MediaSDKString().Detach();
  }
  DShowServerDelegate enum_imp;
  DShowDeviceName name;
  name.id = base::UTF8ToWide(device_id);

  if (type == kEnumFormatTypeAudio) {
    const auto json_string = enum_imp.GetAudioDeviceFormats(name.id, name.name);
    LOG(INFO) << base::StringPrintf("device_id:%s, ret:%s", device_id,
                                    json_string.c_str());
    return MediaSDKString(json_string).Detach();
  } else if (type == kEnumFormatTypeVideo) {
    const auto json_string = enum_imp.GetVideoDeviceFormats(name.id, name.name);
    LOG(INFO) << base::StringPrintf("device_id:%s, ret:%s", device_id,
                                    json_string.c_str());
    return MediaSDKString(json_string).Detach();
  }
  return MediaSDKString().Detach();
}

}  // namespace mediasdk