#include "canvas.h"

#include "mediasdk/notify_center.h"
#include "mediasdk/video/clip_mask_canvas_item.h"

namespace mediasdk {

Canvas::Canvas(const std::string& canvas_id, graphics::Device& device)
    : canvas_id_(canvas_id), device_(device) {}

Canvas::~Canvas() {
  std::vector<MediaSDKString> item_ids;
  item_ids.reserve(items_.size());
  for (const auto& item : items_) {
    item_ids.push_back(item->GetId());
  }

  // clear all items
  items_.clear();

  // notify destroyed items by destroy canvas
  if (const auto nc = com::GetNotifyCenter()) {
    nc->CanvasEvent()->Notify(
        FROM_HERE,
        &MediaSDKCanvasEventObserver::OnCanvasItemsDestroyedByDestroyCanvas,
        canvas_id_, item_ids);
  }
}

const CanvasItemPtrList& Canvas::GetItems() const {
  return items_;
}

void Canvas::SetActive(bool is_active) {
  is_active_ = is_active;
  if (!is_active) {
    auto hover_item = hovered_item_.lock();
    if (hover_item) {
      hover_item->SetHovered(false);
    }
    hovered_item_.reset();
    EraseClipItem();
  }
  LOG(INFO) << "[Canvas] canvas:" << canvas_id_ << " is_active:" << is_active;
}

CanvasItemPtr Canvas::GetItem(const std::string& item_id) const {
  auto it = std::find_if(
      items_.begin(), items_.end(),
      [&](const CanvasItemPtr& item) { return item_id == item->GetId(); });

  if (items_.end() == it) {
    return nullptr;
  }
  return *it;
}

bool Canvas::AddItem(const CanvasItemPtr& item) const {
  if (!item) {
    return false;
  }
  auto it = std::find_if(items_.begin(), items_.end(),
                         [&](const CanvasItemPtr& item_iter) {
                           return item->GetId() == item_iter->GetId();
                         });
  if (it != items_.end()) {
    LOG(ERROR) << "[Canvas] canvas:" << canvas_id_
               << " already contain item:" << item->GetId();
    return false;
  }
  items_.push_back(item);
  SortItems(items_);
  return true;
}

bool Canvas::EraseItem(const std::string& item_id) const {
  auto it = std::find_if(
      items_.begin(), items_.end(),
      [&](const CanvasItemPtr& item) { return item_id == item->GetId(); });
  if (it != items_.end()) {
    items_.erase(it);
    LOG(INFO) << "[Canvas] canvas:" << canvas_id_ << ". Erase item:" << item_id;
    return true;
  }
  LOG(ERROR) << "[Canvas] canvas:" << canvas_id_ << ". Erase item:" << item_id
             << " Failed";
  return false;
}

bool Canvas::SetClipItem(const CanvasItemPtr& item) {
  if (auto clip_item = clip_item_.lock()) {
    NOTREACHED() << "[Canvas] clip_item exist:" << clip_item->GetId();
    return false;
  }
  items_.push_back(item);
  clip_item_ = item;
  return true;
}

void Canvas::EraseClipItem() {
  if (auto clip_item = clip_item_.lock()) {
    EraseItem(clip_item->GetId());
    clip_item_.reset();
  }
}

bool Canvas::SetItemsOrder(const std::vector<std::string>& item_id_orders) {
  std::vector<std::pair<CanvasItemPtr, bool>> items_with_hit;
  items_with_hit.reserve(items_.size());
  for (const auto& item : items_) {
    items_with_hit.emplace_back(item, false);
  }

  CanvasItemPtrList new_items;
  for (const auto& id : item_id_orders) {
    for (auto& [item_ptr, hit] : items_with_hit) {
      if (id == item_ptr->GetId()) {
        new_items.emplace_back(item_ptr);
        hit = true;
      }
    }
  }

  for (auto it = items_with_hit.rbegin(); it != items_with_hit.rend(); ++it) {
    if (!it->second) {
      new_items.emplace_back(it->first);
    }
  }
  std::reverse(new_items.begin(), new_items.end());

  SortItems(new_items);
  items_ = std::move(new_items);
  return true;
}

void Canvas::SwapItem(CanvasItemPtr old_item, CanvasItemPtr new_item) const {
  if (nullptr == old_item || nullptr == new_item) {
    return;
  }
  auto it = std::find_if(
      items_.begin(), items_.end(),
      [&](const CanvasItemPtr& item) { return item.get() == old_item.get(); });
  if (it != items_.end()) {
    *it = new_item;
  }
}

bool Canvas::SwapWithPrev(const std::string& item_id) {
  const auto it = std::find_if(
      items_.begin(), items_.end(), [&item_id](const CanvasItemPtr& item_ptr) {
        return item_ptr && item_ptr->GetId() == item_id;
      });

  if (it == items_.end()) {
    return false;
  }

  // If the target visual is not the first element in the list, swap it with the
  // previous one
  if (it != items_.begin()) {
    std::iter_swap(it, std::prev(it));
  }
  return true;
}

bool Canvas::SwapWithNext(const std::string& item_id) {
  const auto it = std::find_if(
      items_.begin(), items_.end(), [&item_id](const CanvasItemPtr& item_ptr) {
        return item_ptr && item_ptr->GetId() == item_id;
      });

  if (it == items_.end()) {
    return false;
  }

  // If the target visual is not the last element in the list, swap its position
  // with the next element
  if (std::next(it) != items_.end()) {
    std::iter_swap(it, std::next(it));
  }
  return true;
}

void Canvas::MoveCanvasItemZOrder(const std::string& item_id,
                                  const MovePostion pos) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  for (const auto& item_ptr : items_) {
    if (item_ptr->GetId() == item_id) {
      switch (pos) {
        case kMoveBottom: {
          MoveBottomZorder(items_, item_id);
        } break;
        case kMoveTop: {
          MoveTopZorder(items_, item_id);
        } break;
        case kMoveUp: {
          MoveUpZorder(items_, item_id);
        } break;
        case kMoveDown: {
          MoveDownZorder(items_, item_id);
        } break;
      }
      SortItems(items_);
      break;
    }
  }
}

bool Canvas::SetCurrentItem(const std::string& item_id) {
  if (item_id.empty()) {
    return SetCurrentItem(CanvasItemPtr{nullptr});
  }

  auto item = GetItem(item_id);
  if (!item) {
    LOG(ERROR) << "[Canvas] No such item(" << item_id << ") in canvas("
               << canvas_id_ << ")";
    return false;
  }

  return SetCurrentItem(item);
}

bool Canvas::SetCurrentItem(const CanvasItemPtr& item) {
  auto cur_item = current_item_.lock();
  if (cur_item == item) {
    return true;
  }

  if (item && !Exist(item)) {
    NOTREACHED();
    LOG(ERROR) << "[Canvas] canvas:" << canvas_id_
               << " not contain item:" << item->GetId();
    return false;
  }

  if (cur_item) {
    cur_item->SetSelected(false);
    if (cur_item == clip_item_.lock()) {
      cur_item.reset();
      EraseClipItem();
    }
  }
  if (item) {
    item->SetSelected(true);
  }
  current_item_ = item;

  return true;
}

std::shared_ptr<CanvasItem> Canvas::GetCurrentItem() {
  return current_item_.lock();
}

bool Canvas::SetHoveredItem(const CanvasItemPtr& item) {
  auto hover_item = hovered_item_.lock();
  if (hover_item == item) {
    return false;
  }

  if (item && !Exist(item)) {
    NOTREACHED();
    LOG(ERROR) << "[Canvas] canvas:" << canvas_id_
               << " not contain item:" << item->GetId();
    return false;
  }

  if (hover_item) {
    hover_item->SetHovered(false);
  }
  if (item) {
    item->SetHovered(true);
  }
  hovered_item_ = item;
  return true;
}

std::shared_ptr<CanvasItem> Canvas::GetHoveredItem() {
  return hovered_item_.lock();
}

bool Canvas::Exist(const CanvasItemPtr& item) {
  auto it =
      std::find_if(items_.begin(), items_.end(),
                   [&](const CanvasItemPtr& _item) { return item == _item; });

  return items_.end() != it;
}

void Canvas::MoveTopZorder(CanvasItemPtrList& canvas_item_list,
                           const std::string& canvas_item_id) {
  if (canvas_item_id.empty()) {
    return;
  }
  CanvasItemPtr select = nullptr;
  CanvasItemPtrList final_item_list;
  final_item_list.reserve(canvas_item_list.size());
  for (const auto& item_ptr : canvas_item_list) {
    if (item_ptr && item_ptr->GetId() != canvas_item_id) {
      final_item_list.push_back(item_ptr);
    } else {
      select = item_ptr;
    }
  }
  if (!select) {
    return;
  }
  final_item_list.push_back(select);
  canvas_item_list = std::move(final_item_list);
}

void Canvas::MoveBottomZorder(CanvasItemPtrList& canvas_item_list,
                              const std::string& canvas_item_id) {
  if (canvas_item_id.empty()) {
    return;
  }

  CanvasItemPtr select = nullptr;
  CanvasItemPtrList final_item_list;
  final_item_list.reserve(canvas_item_list.size());

  for (const auto& visual_ptr : canvas_item_list) {
    if (visual_ptr && visual_ptr->GetId() == canvas_item_id) {
      select = visual_ptr;
    }
  }
  if (!select) {
    return;
  }
  final_item_list.push_back(select);
  for (const auto& visual_ptr : canvas_item_list) {
    if (visual_ptr && visual_ptr->GetId() != canvas_item_id) {
      final_item_list.push_back(visual_ptr);
    }
  }
  canvas_item_list = std::move(final_item_list);
}

void Canvas::MoveUpZorder(CanvasItemPtrList& canvas_item_list,
                          const std::string& canvas_item_id) {
  if (canvas_item_id.empty()) {
    return;
  }

  CanvasItemPtrList final_item_list;
  final_item_list.reserve(canvas_item_list.size());
  for (auto item_it = canvas_item_list.begin();
       item_it != canvas_item_list.end(); ++item_it) {
    if (*item_it && (*item_it)->GetId() == canvas_item_id) {
      auto select = *item_it;
      ++item_it;
      if (item_it != canvas_item_list.end()) {
        final_item_list.push_back(*item_it);
        final_item_list.push_back(select);
      } else {
        final_item_list.push_back(select);
        break;
      }
    } else {
      final_item_list.push_back(*item_it);
    }
  }

  canvas_item_list = std::move(final_item_list);
}

void Canvas::MoveDownZorder(CanvasItemPtrList& canvas_item_list,
                            const std::string& visual_id) {
  if (visual_id.empty()) {
    return;
  }
  CanvasItemPtrList final_item_list;
  final_item_list.reserve(canvas_item_list.size());
  for (auto item_it = canvas_item_list.begin();
       item_it != canvas_item_list.end(); ++item_it) {
    if (*item_it && (*item_it)->GetId() == visual_id) {
      if (!final_item_list.empty()) {
        auto back = final_item_list.back();
        final_item_list.pop_back();
        final_item_list.push_back(*item_it);
        final_item_list.push_back(back);
      } else {
        final_item_list.push_back(*item_it);
      }
    } else {
      final_item_list.push_back(*item_it);
    }
  }

  canvas_item_list = std::move(final_item_list);
}

void Canvas::SortItems(CanvasItemPtrList& items) {
  if (items.empty()) {
    return;
  }

  CanvasItemPtrList always_top_list;
  auto remove_it = std::remove_if(
      items.begin(), items.end(),
      [&always_top_list](const CanvasItemPtr& item_ptr) -> bool {
        const bool always_top = item_ptr && item_ptr->IsAlwaysTop();
        if (always_top) {
          always_top_list.emplace_back(item_ptr);
        }
        return always_top;
      });
  // remove always top item
  items.erase(remove_it, items.end());

  // add always top item to end
  for (const auto& item_ptr : always_top_list) {
    items.emplace_back(item_ptr);
  }
}

}  // namespace mediasdk
