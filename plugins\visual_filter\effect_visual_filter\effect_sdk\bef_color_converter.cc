//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/1/14.
//

#include "bef_color_converter.h"
#include <fstream>
#include <string>
#include <vector>
#include "base/logging.h"
#include "gles_loader_autogen.h"
#include "time_helper.h"

#include <windows.h>
#include <Shlobj.h>
#include <Knownfolders.h> // For FOLDERID_RoamingAppData
#include <vector>
#include <filesystem>
#include <mutex>
#include <combaseapi.h> // For CoTaskMemFree

namespace {
static std::mutex g_shader_cache_mutex;
}

NAMESPACE_BEF_EFFECT_FRAMEWORK_BEGIN

struct BEFColorConverter::EffectGLEnv {
  GLenum m_inFormat = GL_RGBA;   // input channel
  GLenum m_outFormat = GL_RGBA;  // output channel
  int m_outputWidth = 0;
  int m_outputHeight = 0;
  GLuint m_fbo = 0;
  GLuint m_program = 0;
};

class BGRA_RGBAConverter : public BEFColorConverter {
 public:
  BGRA_RGBAConverter() { m_type = Default_Converter; };

 protected:
  const char* getFragmentShader();
};

class ARGB_RGBAConverter : public BEFColorConverter {
 public:
  ARGB_RGBAConverter() { m_type = Default_Converter; };

 protected:
  const char* getFragmentShader();
};

class YUV_RGBAConverter : public BEFColorConverter {
 public:
  YUV_RGBAConverter();

 protected:
  virtual int addExtraUniform();
};

class YUY2_RGBAConverter : public YUV_RGBAConverter {
 public:
  YUY2_RGBAConverter() {
    m_parami["colorSpace"] = COLOR_SPACE::SD_REC601;
    m_type = YUY2_RGBA;
  };

 protected:
  const char* getFragmentShader();
};

class I420_RGBAConverter : public YUV_RGBAConverter {
 public:
  I420_RGBAConverter() { m_type = I420_RGBA; };

 protected:
  const char* getFragmentShader();
};

class RGBA_I420Converter : public YUV_RGBAConverter {
 public:
  RGBA_I420Converter() { m_type = RGBA_I420; };

 protected:
  const char* getFragmentShader();
  virtual int addExtraUniform();
};

class I420_RGBA_3C_Converter : public YUV_RGBAConverter {
 public:
  I420_RGBA_3C_Converter() { m_type = I420_RGBA_3C; };

 protected:
  const char* getFragmentShader();
};

class YUV422P_RGBA_Converter : public I420_RGBA_3C_Converter {
 public:
  YUV422P_RGBA_Converter() {
    m_parami["colorSpace"] = COLOR_SPACE::SD_REC601;
    m_type = YUV422P_RGBA;
  };

 protected:
  const char* getFragmentShader();
};

constexpr GLfloat VertexArray[] = {
    -1.0, -1.0, 0.0, 1.0, -1.0, 0.0, -1.0, 1.0, 0.0, 1.0, 1.0, 0.0,
};

constexpr GLfloat UVArray[] = {0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 1.0f, 1.0f, 1.0f};

const char* BEFColorConverter::getVertexShader() {
  return R"(
attribute vec3 attPosition;
attribute vec2 attUV;
varying vec2 textureCoordinate;
uniform float u_flipX;
uniform float u_flipY;
void main()
{
    gl_Position = vec4(attPosition.xy * vec2(u_flipX, u_flipY), attPosition.z, 1.);
    textureCoordinate = attUV;
}
)";
}

const char* BEFColorConverter::getFragmentShader() {
  return R"(
precision highp float;
varying vec2 textureCoordinate;
uniform sampler2D u_inputTexture;

void main()
{
    vec4 color = texture2D(u_inputTexture, textureCoordinate);
    gl_FragColor = vec4(color.r, color.g, color.b, color.a);
}
)";
}

BEFColorConverter::BEFColorConverter() : m_type(Default_Converter) {
  m_parami.insert(std::make_pair("flipX", 0));
  m_parami.insert(std::make_pair("flipY", 0));
  m_parami.insert(std::make_pair("rotateType", 0));
  m_effectGLEnv = new EffectGLEnv();
};

BEFColorConverter::~BEFColorConverter() {
  destroy();
};

static void PrintShaderError(GLuint id) {
  int size = 0;
  glGetShaderiv(id, GL_INFO_LOG_LENGTH, &size);
  std::string value;
  value.resize(size + 1);
  glGetShaderInfoLog(id, size, NULL, &value[0]);
}

// Helper function to get a unique program cache path
static std::string GetProgramCachePath(const std::string& programIdentifier) {
  return "program_cache_" + programIdentifier + ".bin";
}

int BEFColorConverter::LoadProgramBinary(
    bool& loaded_from_binary,
    const std::string& program_cache_path) {
  int ret = -1;
  std::ifstream program_file(program_cache_path, std::ios::binary);
  if (program_file.is_open()) {
    GLenum binary_format_to_load;
    program_file.read(reinterpret_cast<char*>(&binary_format_to_load),
                      sizeof(GLenum));

    if (program_file.good()) {
      program_file.seekg(0, std::ios::end);
      std::streamsize total_size = program_file.tellg();
      std::streamsize program_data_size =
          total_size - static_cast<std::streamsize>(sizeof(GLenum));

      if (program_data_size > 0) {
        std::vector<char> buffer(program_data_size);
        program_file.seekg(sizeof(GLenum), std::ios::beg);
        if (program_file.read(buffer.data(), program_data_size)) {
          // Check if the loaded binary_format_to_load is supported by the
          // current driver
          GLint num_program_binary_formats = 0;
          glGetIntegerv(GL_NUM_PROGRAM_BINARY_FORMATS,
                        &num_program_binary_formats);
          bool format_supported = false;
          if (num_program_binary_formats > 0) {
            std::vector<GLint> supported_formats(num_program_binary_formats);
            glGetIntegerv(GL_PROGRAM_BINARY_FORMATS, supported_formats.data());
            for (GLint supported_format : supported_formats) {
              if (static_cast<GLenum>(supported_format) ==
                  binary_format_to_load) {
                format_supported = true;
                break;
              }
            }
          }

          if (format_supported) {
            try {
              glProgramBinary(m_effectGLEnv->m_program, binary_format_to_load,
                              buffer.data(),
                              static_cast<GLsizei>(program_data_size));
            } catch (...) {
              LOG(ERROR) << "LoadProgramBinary failed. glProgramBinary failed.";
            }

            GLint link_status = 0;
            glGetProgramiv(m_effectGLEnv->m_program, GL_LINK_STATUS,
                           &link_status);
            if (link_status == GL_TRUE) {
              loaded_from_binary = true;
              ret = 0;
            } else {
              // Binary failed to link, fallback to compilation
              loaded_from_binary = false;  // Ensure fallback
              if (m_effectGLEnv->m_program) {
                glDeleteProgram(m_effectGLEnv->m_program);
                m_effectGLEnv->m_program =
                    glCreateProgram();  // Recreate for compilation path
                if (m_effectGLEnv->m_program == 0) {
                  if (m_effectGLEnv->m_fbo) {
                    glDeleteFramebuffers(1, &m_effectGLEnv->m_fbo);
                    m_effectGLEnv->m_fbo = 0;
                  }
                  return -1;
                }
              }
            }
          } else {
            loaded_from_binary = false;  // Format not supported, fallback
          }
        } else {
          loaded_from_binary = false;  // Failed to read program data, fallback
        }
      } else {
        loaded_from_binary = false;  // Program data size is 0 or less, fallback
      }
    } else {
      loaded_from_binary = false;  // Failed to read binary_format_to_load or
                                   // file not good, fallback
    }
    program_file.close();
  } else {
    loaded_from_binary = false;  // Program cache file not open, fallback
  }
  return ret;
}

int BEFColorConverter::init(bool use_binary_shader) {
  int64_t begin_time_ms = mediasdk::milli_now();
  int ret = -1;
  GLuint vertexShader = 0;
  GLuint fragmentShader = 0;
  do {
    // FBO
    GLuint prevFbo = 0;
    glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
    glGenFramebuffers(1, &m_effectGLEnv->m_fbo);

    // CompileShader
    vertexShader = glCreateShader(GL_VERTEX_SHADER);
    const char* vs = getVertexShader();
    glShaderSource(vertexShader, 1, &vs, nullptr);
    glCompileShader(vertexShader);
    GLint compileStatus;
    glGetShaderiv(vertexShader, GL_COMPILE_STATUS, &compileStatus);
    if (!compileStatus)
      break;

    fragmentShader = glCreateShader(GL_FRAGMENT_SHADER);
    const char* fs = getFragmentShader();
    glShaderSource(fragmentShader, 1, &fs, nullptr);
    glCompileShader(fragmentShader);
    glGetShaderiv(fragmentShader, GL_COMPILE_STATUS, &compileStatus);
    if (!compileStatus)
      break;

    // CreateProgram
    m_effectGLEnv->m_program = glCreateProgram();
    glAttachShader(m_effectGLEnv->m_program, vertexShader);
    glAttachShader(m_effectGLEnv->m_program, fragmentShader);
    glLinkProgram(m_effectGLEnv->m_program);
    GLint linked;
    glGetProgramiv(m_effectGLEnv->m_program, GL_LINK_STATUS, &linked);
    if (!linked)
      break;

    ret = 0;
  } while (false);
  // release
  if (vertexShader)
    glDeleteShader(vertexShader);
  if (fragmentShader)
    glDeleteShader(fragmentShader);

  int64_t end_time_ms = mediasdk::milli_now();
  LOG(INFO) << "BEFColorConverter init() costs:" << end_time_ms - begin_time_ms;
  return ret;
}

void BEFColorConverter::destroy() {
  // destroy fbo
  if (m_effectGLEnv->m_fbo) {
    glDeleteFramebuffers(1, &m_effectGLEnv->m_fbo);
  }
  // destroy program
  if (m_effectGLEnv->m_program)
    glDeleteProgram(m_effectGLEnv->m_program);

  delete m_effectGLEnv;
  m_effectGLEnv = nullptr;
}

void BEFColorConverter::finish() {
  glFinish();
}

unsigned int BEFColorConverter::createTexture(int min_filter,
                                              int mag_filter,
                                              int wrap_s,
                                              int wrap_t) {
  unsigned int retTextureID = 0;
  glGenTextures(1, &retTextureID);
  glBindTexture(GL_TEXTURE_2D, retTextureID);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, min_filter);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, mag_filter);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, wrap_s);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, wrap_t);
  glBindTexture(GL_TEXTURE_2D, 0);
  return retTextureID;
}

int BEFColorConverter::updateYUVTexture(unsigned int textureYID,
                                        unsigned int textureUID,
                                        unsigned int textureVID,
                                        void* buffer,
                                        int width,
                                        int height,
                                        bef_color_type format) {
  if (format != color_yuv422p)
    return -1;
  const char* ptr = (char*)buffer;
  glBindTexture(GL_TEXTURE_2D, textureYID);
  GLenum internalFormat = GL_ALPHA;
  GLenum inputFormat = GL_ALPHA;
  glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, width, height, 0, inputFormat,
               GL_UNSIGNED_BYTE, ptr);
  ptr += width * height;
  glBindTexture(GL_TEXTURE_2D, textureUID);
  glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, width / 2, height, 0,
               inputFormat, GL_UNSIGNED_BYTE, ptr);
  ptr += (width / 2) * height;
  glBindTexture(GL_TEXTURE_2D, textureVID);
  glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, width / 2, height, 0,
               inputFormat, GL_UNSIGNED_BYTE, ptr);
  return 0;
}

int BEFColorConverter::updateTexture(unsigned int textureID,
                                     void* buffer,
                                     int width,
                                     int height,
                                     bef_color_type format) {
  GLenum m_format = GL_RGBA;
  int inputWidth = width;
  int inputHeight = height;
  switch (format) {
    case color_rgb:
    case color_bgr:
      m_format = GL_RGB;
      break;
    case color_i420:
      m_format = GL_ALPHA;
      inputWidth = width;
      inputHeight = height * 3 / 2;
      break;
    case color_yuy2:
      inputWidth = width / 2;
      m_format = GL_RGBA;
      break;
  }
  glBindTexture(GL_TEXTURE_2D, textureID);
  glTexImage2D(GL_TEXTURE_2D, 0, m_format, inputWidth, inputHeight, 0, m_format,
               GL_UNSIGNED_BYTE, buffer);
  glBindTexture(GL_TEXTURE_2D, 0);
  return 0;
}

int BEFColorConverter::deleteTexture(unsigned int textureID) {
  if (glIsTexture(textureID)) {
    glDeleteTextures(1, &textureID);
    return 0;
  }
  return -1;
}

int BEFColorConverter::readPixel(unsigned int textureID,
                                 void* buffer,
                                 int width,
                                 int height,
                                 bef_color_type format) {
  int ret = -1;
  GLenum m_format = GL_RGBA;
  switch (format) {
    case color_rgb:
    case color_bgr:
      m_format = GL_RGB;
      break;
    default:
      m_format = GL_RGBA;
  }
  GLuint prevFbo = 0;
  glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
  glBindFramebuffer(GL_FRAMEBUFFER, m_effectGLEnv->m_fbo);
  glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D,
                         textureID, 0);
  glActiveTexture(GL_TEXTURE0);
  glViewport(0, 0, width, height);
  glReadPixels(0, 0, width, height, m_format, GL_UNSIGNED_BYTE, buffer);
  glBindFramebuffer(GL_FRAMEBUFFER, prevFbo);

  ret = glGetError();
  return ret;
}

int BEFColorConverter::convert(unsigned int srcY,
                               unsigned int srcU,
                               unsigned int srcB,
                               unsigned int dstTexture,
                               int width,
                               int height) {
  int ret = -1;
  if (width <= 0 || height <= 0) {
    return -1;
  }
  m_effectGLEnv->m_outputWidth = width;
  m_effectGLEnv->m_outputHeight = height;

  GLuint prevFbo = 0;
  glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
  do {
    if (dstTexture != 0) {
      glBindFramebuffer(GL_FRAMEBUFFER, m_effectGLEnv->m_fbo);
      glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0,
                             GL_TEXTURE_2D, dstTexture, 0);
    } else {
      glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }
    glClearColor(1.0, 0.0, 0.0, 1.0);
    GLint _attPosition =
        glGetAttribLocation(m_effectGLEnv->m_program, "attPosition");
    if (-1 == _attPosition)
      break;
    GLint _attUV = glGetAttribLocation(m_effectGLEnv->m_program, "attUV");
    if (-1 == _attUV)
      break;
    GLint _inputY = glGetUniformLocation(m_effectGLEnv->m_program, "u_inputY");
    if (-1 == _inputY)
      break;
    GLint _inputU = glGetUniformLocation(m_effectGLEnv->m_program, "u_inputU");
    if (-1 == _inputU)
      break;
    GLint _inputV = glGetUniformLocation(m_effectGLEnv->m_program, "u_inputV");
    if (-1 == _inputV)
      break;
    GLint _flipX = glGetUniformLocation(m_effectGLEnv->m_program, "u_flipX");
    if (-1 == _flipX)
      break;
    GLint _flipY = glGetUniformLocation(m_effectGLEnv->m_program, "u_flipY");
    if (-1 == _flipY)
      break;
    glUseProgram(m_effectGLEnv->m_program);
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, srcY);
    glUniform1i(_inputY, 0);
    glActiveTexture(GL_TEXTURE1);
    glBindTexture(GL_TEXTURE_2D, srcU);
    glUniform1i(_inputU, 1);
    glActiveTexture(GL_TEXTURE2);
    glBindTexture(GL_TEXTURE_2D, srcB);
    glUniform1i(_inputV, 2);
    if (m_parami["flipX"]) {
      glUniform1f(_flipX, -1);
    } else {
      glUniform1f(_flipX, 1);
    }
    if (m_parami["flipY"]) {
      glUniform1f(_flipY, -1);
    } else {
      glUniform1f(_flipY, 1);
    }
    glEnableVertexAttribArray(_attPosition);
    glVertexAttribPointer(_attPosition, 3, GL_FLOAT, 0, 0, VertexArray);
    glEnableVertexAttribArray(_attUV);
    glVertexAttribPointer(_attUV, 2, GL_FLOAT, 0, 0, UVArray);
    if (addExtraUniform())
      break;
    if (m_type == RGBA_I420) {
      glViewport(0, 0, width, height * 3 / 2);
    } else {
      glViewport(0, 0, width, height);
    }
    ret = glGetError();
    glDrawArrays(GL_TRIANGLE_STRIP, 0, 4);
    ret = glGetError();
    // glFlush();
    ret = 0;
  } while (false);

  glBindFramebuffer(GL_FRAMEBUFFER, prevFbo);
  ret = glGetError();
  return ret;
}

int BEFColorConverter::convertWithoutTransform(unsigned int srcTexture,
                                               unsigned int dstTexture,
                                               int width,
                                               int height) {
  int ret = -1;
  if (width <= 0 || height <= 0) {
    return -1;
  }
  m_effectGLEnv->m_outputWidth = width;
  m_effectGLEnv->m_outputHeight = height;

  GLuint prevFbo = 0;
  glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
  do {
    if (dstTexture != 0) {
      glBindFramebuffer(GL_FRAMEBUFFER, m_effectGLEnv->m_fbo);
      glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0,
                             GL_TEXTURE_2D, dstTexture, 0);
    } else {
      glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }
    GLint _attPosition =
        glGetAttribLocation(m_effectGLEnv->m_program, "attPosition");
    if (-1 == _attPosition)
      break;
    GLint _attUV = glGetAttribLocation(m_effectGLEnv->m_program, "attUV");
    if (-1 == _attUV)
      break;
    GLint _inputTexture =
        glGetUniformLocation(m_effectGLEnv->m_program, "u_inputTexture");
    if (-1 == _inputTexture)
      break;
    GLint _flipX = glGetUniformLocation(m_effectGLEnv->m_program, "u_flipX");
    if (-1 == _flipX)
      break;
    GLint _flipY = glGetUniformLocation(m_effectGLEnv->m_program, "u_flipY");
    if (-1 == _flipY)
      break;
    // prepareProgram
    glUseProgram(m_effectGLEnv->m_program);
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, srcTexture);
    glUniform1i(_inputTexture, 0);
    glUniform1f(_flipX, 1);
    glUniform1f(_flipY, 1);
    glEnableVertexAttribArray(_attPosition);
    glVertexAttribPointer(_attPosition, 3, GL_FLOAT, 0, 0, VertexArray);
    glEnableVertexAttribArray(_attUV);
    glVertexAttribPointer(_attUV, 2, GL_FLOAT, 0, 0, UVArray);
    if (addExtraUniform())
      break;

    // draw
    glViewport(0, 0, width, height);
    ret = glGetError();
    glDrawArrays(GL_TRIANGLE_STRIP, 0, 4);
    // glFlush();
    ret = 0;
  } while (false);
  glBindFramebuffer(GL_FRAMEBUFFER, prevFbo);

  ret = glGetError();
  return ret;
}

int BEFColorConverter::convert(unsigned int srcTexture,
                               unsigned int dstTexture,
                               int width,
                               int height) {
  int ret = -1;
  if (width <= 0 || height <= 0) {
    return -1;
  }
  m_effectGLEnv->m_outputWidth = width;
  m_effectGLEnv->m_outputHeight = height;

  GLuint prevFbo = 0;
  glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
  do {
    if (dstTexture != 0) {
      glBindFramebuffer(GL_FRAMEBUFFER, m_effectGLEnv->m_fbo);
      glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0,
                             GL_TEXTURE_2D, dstTexture, 0);
    } else {
      glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }
    GLint _attPosition =
        glGetAttribLocation(m_effectGLEnv->m_program, "attPosition");
    if (-1 == _attPosition)
      break;
    GLint _attUV = glGetAttribLocation(m_effectGLEnv->m_program, "attUV");
    if (-1 == _attUV)
      break;
    GLint _inputTexture =
        glGetUniformLocation(m_effectGLEnv->m_program, "u_inputTexture");
    if (-1 == _inputTexture)
      break;
    GLint _flipX = glGetUniformLocation(m_effectGLEnv->m_program, "u_flipX");
    if (-1 == _flipX)
      break;
    GLint _flipY = glGetUniformLocation(m_effectGLEnv->m_program, "u_flipY");
    if (-1 == _flipY)
      break;
    // prepareProgram
    glUseProgram(m_effectGLEnv->m_program);
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, srcTexture);
    glUniform1i(_inputTexture, 0);
    if (m_parami["flipX"]) {
      glUniform1f(_flipX, -1);
    } else {
      glUniform1f(_flipX, 1);
    }
    if (m_parami["flipY"]) {
      glUniform1f(_flipY, -1);
    } else {
      glUniform1f(_flipY, 1);
    }
    glEnableVertexAttribArray(_attPosition);
    glVertexAttribPointer(_attPosition, 3, GL_FLOAT, 0, 0, VertexArray);
    glEnableVertexAttribArray(_attUV);
    glVertexAttribPointer(_attUV, 2, GL_FLOAT, 0, 0, UVArray);
    if (addExtraUniform())
      break;

    // draw
    glViewport(0, 0, width, height);
    ret = glGetError();
    glDrawArrays(GL_TRIANGLE_STRIP, 0, 4);
    // glFlush();
    ret = 0;
  } while (false);

  glBindFramebuffer(GL_FRAMEBUFFER, prevFbo);

  ret = glGetError();
  return ret;
}

int BEFColorConverter::convertTransform(unsigned int srcTexture,
                                        unsigned int dstTexture,
                                        int width,
                                        int height,
                                        bool flag) {
  int ret = -1;
  if (width <= 0 || height <= 0) {
    return -1;
  }
  m_effectGLEnv->m_outputWidth = width;
  m_effectGLEnv->m_outputHeight = height;

  GLuint prevFbo = 0;
  glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
  do {
    if (dstTexture != 0) {
      glBindFramebuffer(GL_FRAMEBUFFER, m_effectGLEnv->m_fbo);
      glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0,
                             GL_TEXTURE_2D, dstTexture, 0);
    } else {
      glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }
    GLint _attPosition =
        glGetAttribLocation(m_effectGLEnv->m_program, "attPosition");
    if (-1 == _attPosition)
      break;
    GLint _attUV = glGetAttribLocation(m_effectGLEnv->m_program, "attUV");
    if (-1 == _attUV)
      break;
    GLint _inputTexture =
        glGetUniformLocation(m_effectGLEnv->m_program, "u_inputTexture");
    if (-1 == _inputTexture)
      break;

    // prepareProgram
    glUseProgram(m_effectGLEnv->m_program);
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, srcTexture);
    glUniform1i(_inputTexture, 0);

    GLfloat texUV[] = {0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 1.0f, 1.0f, 1.0f};

    GLfloat verUV[] = {
        -1.0, -1.0, 0.0, 1.0, -1.0, 0.0, -1.0, 1.0, 0.0, 1.0, 1.0, 0.0,
    };
    GLfloat verUV_tmp[] = {
        -1.0, -1.0, 1.0, -1.0, -1.0, 1.0, 1.0, 1.0,
    };

    // clip
    float clipX = m_paramf["clipX"];
    float clipY = m_paramf["clipY"];
    float clipWidth = m_paramf["clipWidth"];
    float clipHeight = m_paramf["clipHeight"];

    if (clipWidth < 1.0f || clipHeight < 1.0f) {
      texUV[0] = clipX;
      texUV[1] = clipY;
      texUV[2] = clipX + clipWidth;
      texUV[3] = clipY;
      texUV[4] = clipX;
      texUV[5] = clipY + clipHeight;
      texUV[6] = clipX + clipWidth;
      texUV[7] = clipY + clipHeight;
    }

    if (flag) {
      // rotate
      switch (m_parami["rotateType"]) {
        case 1:
          verUV[0] = verUV_tmp[2];
          verUV[1] = verUV_tmp[3];
          verUV[3] = verUV_tmp[6];
          verUV[4] = verUV_tmp[7];
          verUV[6] = verUV_tmp[0];
          verUV[7] = verUV_tmp[1];
          verUV[9] = verUV_tmp[4];
          verUV[10] = verUV_tmp[5];
          break;
        case 2:
          verUV[0] = verUV_tmp[6];
          verUV[1] = verUV_tmp[7];
          verUV[3] = verUV_tmp[4];
          verUV[4] = verUV_tmp[5];
          verUV[6] = verUV_tmp[2];
          verUV[7] = verUV_tmp[3];
          verUV[9] = verUV_tmp[0];
          verUV[10] = verUV_tmp[1];
          break;
        case 3:
          verUV[0] = verUV_tmp[4];
          verUV[1] = verUV_tmp[5];
          verUV[3] = verUV_tmp[0];
          verUV[4] = verUV_tmp[1];
          verUV[6] = verUV_tmp[6];
          verUV[7] = verUV_tmp[7];
          verUV[9] = verUV_tmp[2];
          verUV[10] = verUV_tmp[3];
          break;
        default:
          verUV[0] = verUV_tmp[0];
          verUV[1] = verUV_tmp[1];
          verUV[3] = verUV_tmp[2];
          verUV[4] = verUV_tmp[3];
          verUV[6] = verUV_tmp[4];
          verUV[7] = verUV_tmp[5];
          verUV[9] = verUV_tmp[6];
          verUV[10] = verUV_tmp[7];
          break;
      }

      // flip
      if (m_parami["flipY"] == 1) {
        texUV[0] = 1.0 - texUV[0];
        texUV[2] = 1.0 - texUV[2];
        texUV[4] = 1.0 - texUV[4];
        texUV[6] = 1.0 - texUV[6];
      }
      if (m_parami["flipX"] == 1) {
        texUV[1] = 1.0 - texUV[1];
        texUV[3] = 1.0 - texUV[3];
        texUV[5] = 1.0 - texUV[5];
        texUV[7] = 1.0 - texUV[7];
      }
    } else {
      // flip
      if (m_parami["rotateType"] == 1 || m_parami["rotateType"] == 3) {
        if (m_parami["flipY"] == 1) {
          texUV[1] = 1.0 - texUV[1];
          texUV[3] = 1.0 - texUV[3];
          texUV[5] = 1.0 - texUV[5];
          texUV[7] = 1.0 - texUV[7];
        }
        if (m_parami["flipX"] == 1) {
          texUV[0] = 1.0 - texUV[0];
          texUV[2] = 1.0 - texUV[2];
          texUV[4] = 1.0 - texUV[4];
          texUV[6] = 1.0 - texUV[6];
        }
      } else {
        if (m_parami["flipY"] == 1) {
          texUV[0] = 1.0 - texUV[0];
          texUV[2] = 1.0 - texUV[2];
          texUV[4] = 1.0 - texUV[4];
          texUV[6] = 1.0 - texUV[6];
        }
        if (m_parami["flipX"] == 1) {
          texUV[1] = 1.0 - texUV[1];
          texUV[3] = 1.0 - texUV[3];
          texUV[5] = 1.0 - texUV[5];
          texUV[7] = 1.0 - texUV[7];
        }
      }

      // rotate
      switch (m_parami["rotateType"]) {
        case 1:
          verUV[0] = verUV_tmp[2];
          verUV[1] = verUV_tmp[3];
          verUV[3] = verUV_tmp[6];
          verUV[4] = verUV_tmp[7];
          verUV[6] = verUV_tmp[0];
          verUV[7] = verUV_tmp[1];
          verUV[9] = verUV_tmp[4];
          verUV[10] = verUV_tmp[5];
          break;
        case 2:
          verUV[0] = verUV_tmp[6];
          verUV[1] = verUV_tmp[7];
          verUV[3] = verUV_tmp[4];
          verUV[4] = verUV_tmp[5];
          verUV[6] = verUV_tmp[2];
          verUV[7] = verUV_tmp[3];
          verUV[9] = verUV_tmp[0];
          verUV[10] = verUV_tmp[1];
          break;
        case 3:
          verUV[0] = verUV_tmp[4];
          verUV[1] = verUV_tmp[5];
          verUV[3] = verUV_tmp[0];
          verUV[4] = verUV_tmp[1];
          verUV[6] = verUV_tmp[6];
          verUV[7] = verUV_tmp[7];
          verUV[9] = verUV_tmp[2];
          verUV[10] = verUV_tmp[3];
          break;
        default:
          verUV[0] = verUV_tmp[0];
          verUV[1] = verUV_tmp[1];
          verUV[3] = verUV_tmp[2];
          verUV[4] = verUV_tmp[3];
          verUV[6] = verUV_tmp[4];
          verUV[7] = verUV_tmp[5];
          verUV[9] = verUV_tmp[6];
          verUV[10] = verUV_tmp[7];
          break;
      }
    }

    glEnableVertexAttribArray(_attPosition);
    glVertexAttribPointer(_attPosition, 3, GL_FLOAT, 0, 0, verUV);
    glEnableVertexAttribArray(_attUV);
    glVertexAttribPointer(_attUV, 2, GL_FLOAT, 0, 0, texUV);
    if (addExtraUniform())
      break;

    // draw
    glViewport(0, 0, width, height);
    ret = glGetError();
    glDrawArrays(GL_TRIANGLE_STRIP, 0, 4);
    // glFlush();
    ret = 0;
  } while (false);

  glBindFramebuffer(GL_FRAMEBUFFER, prevFbo);

  ret = glGetError();
  return ret;
}

int BEFColorConverter::copyTexture(unsigned int srcTexture,
                                   unsigned int dstTexture,
                                   int width,
                                   int height) {
  int ret = -1;
  if (width <= 0 || height <= 0) {
    return -1;
  }

  GLuint prevFbo = 0;
  glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
  do {
    if (srcTexture != 0 && dstTexture != 0) {
      glBindFramebuffer(GL_FRAMEBUFFER, m_effectGLEnv->m_fbo);
      glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0,
                             GL_TEXTURE_2D, srcTexture, 0);
      glBindTexture(GL_TEXTURE_2D, dstTexture);
      glCopyTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, 0, 0, width, height, 0);
    } else {
      glBindFramebuffer(GL_FRAMEBUFFER, 0);
    }
  } while (false);

  glBindFramebuffer(GL_FRAMEBUFFER, prevFbo);

  ret = glGetError();
  return ret;
}

int BEFColorConverter::setParami(const char* key, int value) {
  if (m_parami.find(key) == m_parami.end()) {
    return -1;
  }
  m_parami[key] = value;
  return 0;
}

int BEFColorConverter::getParami(const std::string& key, int& value) {
  if (m_parami.find(key) == m_parami.end()) {
    return -1;
  } else {
    value = m_parami[key];
    return 0;
  }
}

int BEFColorConverter::setVideoRange(VIDEO_RANGE range) {
  m_parami["videoRange"] = range;
  return 0;
}

int BEFColorConverter::setColorSpace(COLOR_SPACE space) {
  m_parami["colorSpace"] = space;
  return 0;
}

int BEFColorConverter::setParamf(const char* key, float value) {
  if (m_paramf.find(key) == m_paramf.end()) {
    return -1;
  }
  m_paramf[key] = value;
  return 0;
}

//////////////////////////////////////////
////////////BGRA_RGBAConverter////////////
//////////////////////////////////////////

const char* BGRA_RGBAConverter::getFragmentShader() {
  return R"(
precision highp float;
varying vec2 textureCoordinate;
uniform sampler2D u_inputTexture;

void main()
{
    vec4 color = texture2D(u_inputTexture, textureCoordinate);
    gl_FragColor = vec4(color.b, color.g, color.r, 1.0);
}
)";
}

//////////////////////////////////////////
////////////ARGB_RGBAConverter////////////
//////////////////////////////////////////

const char* ARGB_RGBAConverter::getFragmentShader() {
  return R"(
precision highp float;
varying vec2 textureCoordinate;
uniform sampler2D u_inputTexture;

void main()
{
    vec4 color = texture2D(u_inputTexture, textureCoordinate);
    gl_FragColor = vec4(color.b, color.g, color.r, color.a);
}
)";
}

//////////////////////////////////////////
///////////YUV_RGBAConverter/////////////
//////////////////////////////////////////
YUV_RGBAConverter::YUV_RGBAConverter() {
  m_parami.insert(std::make_pair("videoRange", VIDEO_RANGE::FULL));
  m_parami.insert(std::make_pair("colorSpace", COLOR_SPACE::HD_REC709));
}

namespace {
// Partial
constexpr float YUV_RGBA_MAT601[] = {
    1.164384f,  0.000000f, 1.596027f, -0.874202f, 1.164384f, -0.391762f,
    -0.812968f, 0.531668f, 1.164384f, 2.017232f,  0.000000f, -1.085631f,
    0.000000f,  0.000000f, 0.000000f, 1.000000f};

constexpr float YUV_RGBA_MAT709[] = {
    1.164384f,  0.000000f, 1.792741f, -0.972945f, 1.164384f, -0.213249f,
    -0.532909f, 0.301483f, 1.164384f, 2.112402f,  0.000000f, -1.133402f,
    0.000000f,  0.000000f, 0.000000f, 1.000000f};

// Full Range
constexpr float YUV_RGBA_MAT601_FULL[] = {
    1.000000f,  0.000000f, 1.402000f, -0.701000f, 1.000000f, -0.344136f,
    -0.714136f, 0.529136f, 1.000000f, 1.772000f,  0.000000f, -0.886000f,
    0.000000f,  0.000000f, 0.000000f, 1.000000f};

constexpr float YUV_RGBA_MAT709_FULL[] = {
    1.000000f,  0.000000f, 1.574800f, -0.787400f, 1.000000f, -0.187324f,
    -0.468124f, 0.327724f, 1.000000f, 1.855600f,  0.000000f, -0.927800f,
    0.000000f,  0.000000f, 0.000000f, 1.000000f};

constexpr float YUV_RGBA_IDENTITY[] = {1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f,
                                       0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f,
                                       0.0f, 0.0f, 0.0f, 1.0f};

constexpr float YUV_RGBA_FULL_MIN[] = {0.0f, 0.0f, 0.0f, 1.0f};
constexpr float YUV_RGBA_FULL_MAX[] = {1.0f, 1.0f, 1.0f, 1.0f};

constexpr float YUV_RGBA_PARTIAL_MIN[] = {0.0627451017f, 0.0627451017f,
                                          0.0627451017f, 1.0f};
constexpr float YUV_RGBA_PARTIAL_MAX[] = {0.921568632f, 0.941176474f,
                                          0.941176474f, 1.0f};
}  // namespace

int YUV_RGBAConverter::addExtraUniform() {
  glUseProgram(m_effectGLEnv->m_program);

  GLint _rangeMin =
      glGetUniformLocation(m_effectGLEnv->m_program, "u_rangeMin");
  if (-1 == _rangeMin)
    return -1;
  GLint _rangeMax =
      glGetUniformLocation(m_effectGLEnv->m_program, "u_rangeMax");
  if (-1 == _rangeMax)
    return -1;
  GLint _yuvMat = glGetUniformLocation(m_effectGLEnv->m_program, "u_yuvMat");
  if (-1 == _yuvMat)
    return -1;
  GLint _outputSize =
      glGetUniformLocation(m_effectGLEnv->m_program, "u_outputSize");
  if (-1 == _outputSize)
    return -1;

  switch (m_parami["videoRange"]) {
    case VIDEO_RANGE::PARTIAL:
      glUniform4fv(_rangeMin, 1, YUV_RGBA_PARTIAL_MIN);
      glUniform4fv(_rangeMax, 1, YUV_RGBA_PARTIAL_MAX);
      break;
    case VIDEO_RANGE::FULL:
    default:
      glUniform4fv(_rangeMin, 1, YUV_RGBA_FULL_MIN);
      glUniform4fv(_rangeMax, 1, YUV_RGBA_FULL_MAX);
      break;
  }
  switch (m_parami["colorSpace"]) {
    case COLOR_SPACE::HD_REC709:
      if (m_parami["videoRange"] == VIDEO_RANGE::PARTIAL) {
        glUniformMatrix4fv(_yuvMat, 1, GL_FALSE, YUV_RGBA_MAT709);
      } else {
        glUniformMatrix4fv(_yuvMat, 1, GL_FALSE, YUV_RGBA_MAT709_FULL);
      }
      break;
    case COLOR_SPACE::SD_REC601:
      if (m_parami["videoRange"] == VIDEO_RANGE::PARTIAL) {
        glUniformMatrix4fv(_yuvMat, 1, GL_FALSE, YUV_RGBA_MAT601);
      } else {
        glUniformMatrix4fv(_yuvMat, 1, GL_FALSE, YUV_RGBA_MAT601_FULL);
      }
      break;
    default:
      glUniformMatrix4fv(_yuvMat, 1, GL_FALSE, YUV_RGBA_IDENTITY);
      break;
  }
  float size[] = {float(m_effectGLEnv->m_outputWidth),
                  float(m_effectGLEnv->m_outputHeight)};
  glUniform2fv(_outputSize, 1, size);
  return 0;
}

//////////////////////////////////////////
///////////YUY2_RGBAConverter/////////////
//////////////////////////////////////////

const char* YUY2_RGBAConverter::getFragmentShader() {
  return R"(
precision highp float;
varying vec2      textureCoordinate; // 0.05, 0.15, 0.25, 0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95
uniform sampler2D u_inputTexture;
uniform vec4      u_rangeMin;
uniform vec4      u_rangeMax;
uniform mat4      u_yuvMat;
uniform vec2      u_outputSize; // (10, y)

void main()
{
    float cx = 1.0 / u_outputSize.x; // 0.1
    float odd = floor(mod(textureCoordinate.x * u_outputSize.x, 2.0)); // 0, 1, 0, 1, 0, 1, 0, 1, 0, 1
    float x = textureCoordinate.x + 0.5 * cx - odd * cx; // 0.1, 0.1, 0.3, 0.3, 0.5, 0.5, 0.7, 0.7, 0.9, 0.9
    vec4 color = texture2D(u_inputTexture, vec2(x, textureCoordinate.y));
    color = vec4(odd < 0.5 ? color[0] : color[2], color[1], color[3], 1.0);
    color = clamp(color, u_rangeMin, u_rangeMax);
    color = color * u_yuvMat;
    gl_FragColor = color;
}
)";
}

//////////////////////////////////////////
////////////I420_RGBAConverter////////////
//////////////////////////////////////////

const char* I420_RGBAConverter::getFragmentShader() {
  return R"(
precision highp float;
varying vec2      textureCoordinate; // 0.05, 0.15, 0.25, 0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95
uniform sampler2D u_inputTexture;
uniform vec4      u_rangeMin;
uniform vec4      u_rangeMax;
uniform mat4      u_yuvMat;
uniform vec2      u_outputSize; // (10, y)

void main()
{
    float odd = floor(mod(textureCoordinate.y * u_outputSize.y, 4.0));
    vec4 color;
    color[0] = texture2D(u_inputTexture, vec2(textureCoordinate.x, textureCoordinate.y * 2.0 / 3.0)).a;
    float x = odd < 0.5 ? (textureCoordinate.x / 2.0) : (textureCoordinate.x / 2.0 + 0.5);
    color[1] = texture2D(u_inputTexture, vec2(x, 2.0 / 3.0 + textureCoordinate.y * 1.0 / 6.0)).a;
    color[2] = texture2D(u_inputTexture, vec2(x, 5.0 / 6.0 + textureCoordinate.y * 1.0 / 6.0)).a;
    color[3] = 1.0;
    vec4 outColor = clamp(color, u_rangeMin, u_rangeMax);
    outColor = color * u_yuvMat;
    gl_FragColor = outColor;
}
)";
}

const char* RGBA_I420Converter::getFragmentShader() {
  return R"(
varying vec2      textureCoordinate; // 0.05, 0.15, 0.25, 0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95
uniform sampler2D u_inputTexture;
uniform vec4      u_rangeMin;
uniform vec4      u_rangeMax;
uniform mat4      u_yuvMat;
uniform vec2      u_outputSize; // (10, y)

void main()
{
    if (textureCoordinate.y < 2.0 / 3.0) { //Y
        vec2 coord;
        coord.x = textureCoordinate.x;
        coord.y = textureCoordinate.y * 3.0 / 2.0;
        vec4 srcColor = texture2D(u_inputTexture, coord);
        vec4 yuvValue = srcColor * u_yuvMat;
        float yValue = yuvValue[0];
        gl_FragColor = vec4(yValue);
    }
    else {
        if (textureCoordinate.y < 5.0 / 6.0) { //U
            float odd = textureCoordinate.x < 0.5 ? 0.0 : 1.0;
            vec2 coord;
            coord.x = (textureCoordinate.x - odd * 0.5) * 2.0;
            coord.y = (textureCoordinate.y - 2.0 / 3.0) * 6.0 + odd / u_outputSize.y;
            vec4 srcColor = texture2D(u_inputTexture, coord);
            vec4 yuvValue = srcColor * u_yuvMat;
            float uValue = yuvValue[1];
            gl_FragColor = vec4(uValue);
        }
    }
    if (textureCoordinate.y >= 5.0 / 6.0){ //V
        float odd = textureCoordinate.x < 0.5 ? 0.0 : 1.0;
        vec2 coord;
        coord.x = (textureCoordinate.x - odd * 0.5) * 2.0;
        coord.y = (textureCoordinate.y - 5.0 / 6.0) * 6.0 + odd / u_outputSize.y;
        vec4 srcColor = texture2D(u_inputTexture, coord);
        vec4 yuvValue = srcColor * u_yuvMat;
        float vValue = yuvValue[2];
        gl_FragColor = vec4(vValue);
    }
}
)";
}

const char* I420_RGBA_3C_Converter::getFragmentShader() {
  return R"(
varying vec2      textureCoordinate; // 0.05, 0.15, 0.25, 0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95
uniform sampler2D u_inputY;
uniform sampler2D u_inputU;
uniform sampler2D u_inputV;
uniform vec4      u_rangeMin;
uniform vec4      u_rangeMax;
uniform mat4      u_yuvMat;
uniform vec2      u_outputSize; // (10, y)

void main()
{
    float odd = floor(mod(textureCoordinate.y * u_outputSize.y, 4.0));
    vec4 color;
    color[0] = texture2D(u_inputY, textureCoordinate).a;
    color[1] = texture2D(u_inputU, textureCoordinate).a;
    color[2] = texture2D(u_inputV, textureCoordinate).a;
    color[3] = 1.0;
    vec4 outColor = clamp(color, u_rangeMin, u_rangeMax);
    outColor = color * u_yuvMat;
    gl_FragColor = outColor;
}
)";
}

const char* YUV422P_RGBA_Converter::getFragmentShader() {
  return R"(
precision highp float;
varying vec2      textureCoordinate; // 0.05, 0.15, 0.25, 0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95
uniform sampler2D u_inputY;
uniform sampler2D u_inputU;
uniform sampler2D u_inputV;
uniform vec4      u_rangeMin;
uniform vec4      u_rangeMax;
uniform mat4      u_yuvMat;
uniform vec2      u_outputSize; // (10, y)

void main()
{
    float cx = 1.0 / u_outputSize.x; // 0.1
    float odd = floor(mod(textureCoordinate.x * u_outputSize.x, 2.0)); // 0, 1, 0, 1, 0, 1, 0, 1, 0, 1
    float prev = textureCoordinate.x - (odd * cx); // 0.0, 0.0, 0.2, 0.2, 0.4, 0.4, 0.6, 0.6, 0.8, 0.8
    float post = textureCoordinate.x + (odd * cx); // 0.0, 0.2, 0.2, 0.4, 0.4, 0.6, 0.6, 0.8, 0.8, 1.0
    vec4 color;
	color[0] = texture2D(u_inputY, textureCoordinate).a;
    color[1] = texture2D(u_inputU, vec2(prev, textureCoordinate.y)).a;
    color[2] = texture2D(u_inputV, vec2(prev, textureCoordinate.y)).a;
	 color[3] = 1.0;
    vec4 outColor = clamp(color, u_rangeMin, u_rangeMax);
    outColor = outColor * u_yuvMat;
    gl_FragColor = outColor;
}
)";
}

//
// //prepare transform
class TRANSFORM_RGBAConverter : public BEFColorConverter {
 public:
  TRANSFORM_RGBAConverter() {
    m_paramf.insert(std::make_pair("clipX", 0.f));
    m_paramf.insert(std::make_pair("clipY", 0.f));
    m_paramf.insert(std::make_pair("clipWidth", 1.0f));
    m_paramf.insert(std::make_pair("clipHeight", 1.0f));
  };

  virtual int init(bool use_binary_shader = false) override;

 protected:
  const char* getVertexShader();
  const char* getFragmentShader();
};

//////////////////////////////////////////
////////////TRANSFORM cONVERTER////////////
//////////////////////////////////////////

int TRANSFORM_RGBAConverter::init(bool use_binary_shader) {
  int64_t start_time_ms = mediasdk::milli_now();
  if (!use_binary_shader) {
    return BEFColorConverter::init(false);
  }
  int ret = -1;
  GLuint vertexShader = 0;
  GLuint fragmentShader = 0;
  bool loaded_from_binary = false;

  // FBO setup (can be done regardless of shader compilation method)
  GLuint prevFbo = 0;
  glGetIntegerv(GL_FRAMEBUFFER_BINDING, (GLint*)&prevFbo);
  glGenFramebuffers(1, &m_effectGLEnv->m_fbo);

  // Create program object first
  m_effectGLEnv->m_program = glCreateProgram();
  if (m_effectGLEnv->m_program == 0) {
    // Handle error: Failed to create program
    if (m_effectGLEnv->m_fbo) {
      glDeleteFramebuffers(1, &m_effectGLEnv->m_fbo);
      m_effectGLEnv->m_fbo = 0;
    }
    return -1;
  }

  std::lock_guard<std::mutex> lock(g_shader_cache_mutex);
  // Try to load precompiled program binary
  std::string program_cache_path = GetProgramCachePath(
      std::to_string(m_type));  // Using m_type for a somewhat unique ID

  ret = LoadProgramBinary(loaded_from_binary, program_cache_path);

  if (!loaded_from_binary) {
    do {
      // CompileShader
      vertexShader = glCreateShader(GL_VERTEX_SHADER);
      const char* vs = getVertexShader();
      glShaderSource(vertexShader, 1, &vs, nullptr);
      glCompileShader(vertexShader);
      GLint compileStatus;
      glGetShaderiv(vertexShader, GL_COMPILE_STATUS, &compileStatus);
      if (!compileStatus) {
        PrintShaderError(vertexShader);
        break;
      }

      fragmentShader = glCreateShader(GL_FRAGMENT_SHADER);
      const char* fs = getFragmentShader();
      glShaderSource(fragmentShader, 1, &fs, nullptr);
      glCompileShader(fragmentShader);
      glGetShaderiv(fragmentShader, GL_COMPILE_STATUS, &compileStatus);
      if (!compileStatus) {
        PrintShaderError(fragmentShader);
        break;
      }

      glAttachShader(m_effectGLEnv->m_program, vertexShader);
      glAttachShader(m_effectGLEnv->m_program, fragmentShader);
      glLinkProgram(m_effectGLEnv->m_program);
      GLint linked;
      glGetProgramiv(m_effectGLEnv->m_program, GL_LINK_STATUS, &linked);
      if (!linked) {
        // Print program link error if any
        GLint log_length = 0;
        glGetProgramiv(m_effectGLEnv->m_program, GL_INFO_LOG_LENGTH,
                       &log_length);
        if (log_length > 0) {
          std::vector<char> log_buffer(log_length);
          glGetProgramInfoLog(m_effectGLEnv->m_program, log_length, nullptr,
                              log_buffer.data());
          // Log log_buffer.data()
        }
        break;
      }

      // Save the compiled program binary
      GLint program_binary_length = 0;
      glGetProgramiv(m_effectGLEnv->m_program, GL_PROGRAM_BINARY_LENGTH,
                     &program_binary_length);
      if (program_binary_length > 0) {
        std::vector<char> program_binary(program_binary_length);
        GLenum binary_format;
        glGetProgramBinary(m_effectGLEnv->m_program, program_binary_length,
                           nullptr, &binary_format, program_binary.data());
        std::ofstream out_program_file(program_cache_path,
                                       std::ios::binary | std::ios::trunc);
        if (out_program_file.is_open()) {
          out_program_file.write(reinterpret_cast<const char*>(&binary_format),
                                 sizeof(GLenum));
          out_program_file.write(program_binary.data(), program_binary_length);
          out_program_file.close();
        }
      }
      ret = 0;
    } while (false);
  }

  // release shaders if they were compiled
  if (vertexShader) {
    glDetachShader(m_effectGLEnv->m_program,
                   vertexShader);  // Detach before deleting
    glDeleteShader(vertexShader);
  }
  if (fragmentShader) {
    glDetachShader(m_effectGLEnv->m_program,
                   fragmentShader);  // Detach before deleting
    glDeleteShader(fragmentShader);
  }

  if (ret != 0) {  // If init failed
    if (m_effectGLEnv->m_program) {
      glDeleteProgram(m_effectGLEnv->m_program);
      m_effectGLEnv->m_program = 0;
    }
    if (m_effectGLEnv->m_fbo) {
      glDeleteFramebuffers(1, &m_effectGLEnv->m_fbo);
      m_effectGLEnv->m_fbo = 0;
    }
  }
  int64_t end_time_ms = mediasdk::milli_now();
  LOG(INFO) << "BEFColorConverter init() costs:" << end_time_ms - start_time_ms;
  return ret;
}

const char* TRANSFORM_RGBAConverter::getVertexShader() {
  return R"(
attribute vec3 attPosition;
attribute vec2 attUV;
varying vec2 textureCoordinate;
void main()
{
    gl_Position = vec4(attPosition.xy, attPosition.z, 1.);
    textureCoordinate = attUV;
}
)";
}

const char* TRANSFORM_RGBAConverter::getFragmentShader() {
  return R"(
precision highp float;
varying vec2 textureCoordinate;
uniform sampler2D u_inputTexture;

void main()
{
    vec4 color = texture2D(u_inputTexture, textureCoordinate);
    gl_FragColor = vec4(color.r, color.g, color.b, color.a);
}
)";
}

BEFColorConverter* BEFColorConverterFactory::create(bef_color_type inputType,
                                                    bef_color_type outputType) {
  switch (inputType) {
    case color_argb:
      return new ARGB_RGBAConverter();
    case color_bgra:
    case color_bgr:
      return new BGRA_RGBAConverter();
    case color_yuy2:
      return new YUY2_RGBAConverter();
    case color_i420:
      return new I420_RGBAConverter();
    case color_yuv422p:
      return new YUV422P_RGBA_Converter();
    default:
      return new BEFColorConverter();
  }
}

BEFColorConverter* BEFColorConverterFactory::createTransform() {
  return new TRANSFORM_RGBAConverter();
}

NAMESPACE_BEF_EFFECT_FRAMEWORK_END
