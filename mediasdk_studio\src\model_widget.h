#pragma once

#include <QWidget>
#include "audio_aec_ref_widget.h"
#include "audio_input_property_widget.h"
#include "audio_input_widget.h"
#include "audio_render_params_widget.h"
#include "browser_visual_input_widget.h"
#include "bytelink_visual_input_widget.h"
#include "canvas_manager.h"
#include "desktop_capture_visual_input_widget.h"
#include "dshow_visual_input_widget.h"
#include "fav_input_widget.h"
#include "game_visual_source_input_widget.h"
#include "image_visual_source_input_widget.h"
#include "mediasdk/mediasdk_global_event_observer_impl.h"
#include "mediasdk/public/mediasdk_callback_defines.h"
#include "mediasdk/public/mediasdk_rtc_event_observer.h"
#include "mediasdk/public/mediasdk_stream_status_observer.h"
#include "mediasdk/public/mediasdk_window_event_observer.h"
#include "model_video_params_widget.h"
#include "transition_mgr.h"
#include "ui_model_widget.h"
#include "visual_menu.h"

enum AudioInputType {
  kAudioInputTypeMicrophone,
  kAudioInputTypeLoopback,
  kAudioInputTypeApp,
  kAudioInputTypeFav,
  KAudioInputTypeVisual
};

enum VisualSourceType {
  kVisualSourceTypeDefault,
  kVisualSourceTypeFav,
  kVisualSourceTypeImage
};

struct ListWidgetItemData {
  ListWidgetItemData() = default;
  ListWidgetItemData(QListWidgetItem* item);

  std::string item_name;
  std::string canvas_item_id;
  std::string visual_id;
  std::string plugin_name;
  std::string device_id;
  std::string video_model_id;
  std::string type;
  std::string custom_data_id;
};

class ModelProxy {
 public:
  virtual ~ModelProxy() = default;

  virtual std::string GetCanvasOnModelId(uint32_t model_id) = 0;

  virtual void CreateCanvasItemOnModelId(
      uint32_t model_id,
      const std::string& visual_id,
      const mediasdk::CreateCanvasItemParams& params) = 0;
};

class ModelWidget : public QWidget,
                    public mediasdk::MediaSDKWindowEventObserver,
                    public mediasdk::MediaSDKStreamStatusObserver,
                    public mediasdk::MediaSDKAudioStatusObserver,
                    public mediasdk::MediaSDKRTCEventObserver,
                    public CanvasEventObserver,
                    public VisualMenuDelegate {
  Q_OBJECT

 public:
  ModelWidget(QWidget* parent, uint32_t sink_id, ModelProxy* proxy);

  ~ModelWidget() override;

  bool ContainCanvas(const std::string& canvas_id);

  std::string CreateCanvas(bool is_shown);

  void DestroyCanvas(const std::string& canvas_id);

  void ShowCanvas(const std::string& canvas_id,
                  const std::string& transition_id);

  std::string CurrentCanvas();

  std::string CreateCanvasItemOnVisual(
      const std::string& visual_id,
      const mediasdk::CreateCanvasItemParams& params,
      const VisualDeviceInfo& visual_info);

  std::string CreateCanvasItemOnVisualToCopyCanvas(
      const std::string& visual_id,
      const mediasdk::CreateCanvasItemParams& params,
      const VisualDeviceInfo& visual_info);

  void OnAddToLayout();

  std::string CreateTransition();

  void OnTransitionFinished(const std::string& transition_id);

 public:
  void OnCurrentCanvasChanged(mediasdk::MediaSDKString id);
  void OnCurrentCanvasItemChanged(mediasdk::MediaSDKString id);
  void OnHittestVisualChanged(mediasdk::MediaSDKString id);
  void OnVisualTransformChanged(mediasdk::MediaSDKString id,
                                const mediasdk::MSTransform& transform);
  void OnVisualSizeChanged(mediasdk::MediaSDKString id,
                           const mediasdk::MSSize& new_size);
  void OnBeginTrack(mediasdk::MediaSDKString id);
  void OnEndTrack(mediasdk::MediaSDKString id);
  void OnVisualInvalidArea(mediasdk::MediaSDKString id);
  void OnVisualValidArea(mediasdk::MediaSDKString id);
  void OnVisualStateChange(const std::string& id, bool state);
  void OnCurrentCanvasItem(mediasdk::MediaSDKString* id);
  void SetFixInfo(int32_t& w, int32_t& h);

 protected:
  void resizeEvent(QResizeEvent* event) override;
  void dragEnterEvent(QDragEnterEvent* event) override;
  void dropEvent(QDropEvent* event) override;
  void keyPressEvent(QKeyEvent* event) override;
  void keyReleaseEvent(QKeyEvent* event) override;

  // mediasdk::MediaSDKWindowEventObserver:
  void OnRButtonDown(int sink_id) override;
  void OnRButtonUp(int sink_id) override;
  void OnLButtonDblClk(int sink_id) override;
  void OnLButtonDown(int sink_id) override;
  void OnLButtonUp(int sink_id) override;
  void OnMouseLeave(int sink_id) override;
  void OnMouseHover(int sink_id) override;

  // MediaSDKStreamStatusObserver:
  void OnStartStreamResult(mediasdk::MediaSDKString id,
                           mediasdk::StreamErrorCode error_code) override;
  void OnReconnecting(mediasdk::MediaSDKString id) override;
  void OnConnected(mediasdk::MediaSDKString id) override;
  void OnFirstFrame(mediasdk::MediaSDKString id) override;
  void OnStartFallback(mediasdk::MediaSDKString id) override;
  void OnFallbackResult(mediasdk::MediaSDKString id, bool success) override;
  void OnStreamStopped(mediasdk::MediaSDKString id,
                       mediasdk::StreamErrorCode error_code) override;
  void OnBitrateChange(mediasdk::MediaSDKString id,
                       uint32_t pre_bitrate_kbps,
                       uint32_t bitrate_kbps) override;
  void OnEncodeError(mediasdk::MediaSDKString id) override;
  void OnEncodeEvent(mediasdk::MediaSDKString id,
                     mediasdk::MediaSDKString json_param) override;
  void OnSpeedTestResult(mediasdk::MediaSDKString id,
                         mediasdk::MediaSDKString result) override;

  // MediaSDKAudioStatusObserver
  void OnAudioPeak(mediasdk::MediaSDKString id,
                   const float* peak_array,
                   uint32_t peak_array_size) override {}

  void OnAudioEvent(mediasdk::MediaSDKString id,
                    mediasdk::MediaSDKString event) override;

  void OnAudioPeakLR(mediasdk::MediaSDKString id,
                     const float left,
                     const float right,
                     const float,
                     const float) override{};

  void OnAudioTrackPeak(uint32_t track_id,
                        const float left,
                        const float right) override {}

  void OnEchoDetectionResult(const float probability) override {}

  // MediaSDKGlobalEventObserver
  void OnPluginGlobalEventHappened(mediasdk::PluginInfo info,
                                   mediasdk::MediaSDKString event);

  void AutoAddAudio(const std::string& plugin_name);

  // MediaSDKRTCEventObserver
  void OnEngineStart(int error_code) override;
  void OnEngineStop() override;
  void OnJoinChannel(mediasdk::MediaSDKString room_id,
                     mediasdk::MediaSDKString user_id,
                     int state,
                     mediasdk::MediaSDKString extra_info) override;
  void OnLeaveChannel() override;
  void OnUserJoined(mediasdk::MediaSDKString user_id, int elapsed) override;
  void OnUserLeave(mediasdk::MediaSDKString user_id, int reason) override;
  void OnLocalStreamStats(mediasdk::MediaSDKString stats) override;
  void OnRemoteStreamStats(mediasdk::MediaSDKString user_id,
                           mediasdk::MediaSDKString stats) override;
  void OnUserPublishStream(mediasdk::MediaSDKString user_id,
                           int stream_index,
                           bool is_screen,
                           int media_stream_type) override;
  void OnUserUnpublishStream(mediasdk::MediaSDKString user_id,
                             int stream_index,
                             int media_stream_type,
                             int reason) override;
  void OnNetworkQuality(mediasdk::MediaSDKString quality) override;
  void OnConnectionStateChanged(int state) override;
  void OnWarning(int warn) override;
  void OnError(int error) override;
  void OnForwardStreamStateChanged(
      mediasdk::MediaSDKString stream_state_infos) override;
  void OnLocalAudioPropertiesReport(
      mediasdk::MediaSDKString audio_properties_infos) override;
  void OnRemoteAudioPropertiesReport(
      mediasdk::MediaSDKString audio_properties_infos) override;
  void OnActiveSpeaker(mediasdk::MediaSDKString room_id,
                       mediasdk::MediaSDKString uid) override;
  void OnAudioDeviceStateChanged(mediasdk::MediaSDKString state_info) override;
  void OnFirstRemoteAudioFrame(mediasdk::MediaSDKString user_id,
                               int stream_index) override;
  void OnFirstRemoteVideoFrameDecoded(
      mediasdk::MediaSDKString user_id,
      int stream_index,
      mediasdk::MediaSDKString frame_info) override;
  void OnFirstRemoteVideoFrameRendered(
      mediasdk::MediaSDKString user_id,
      int stream_index,
      mediasdk::MediaSDKString frame_info) override;
  void OnRemoteVideoSizeChanged(mediasdk::MediaSDKString user_id,
                                int stream_index,
                                mediasdk::MediaSDKString frame_info) override;
  void OnSEIMessageReceived(mediasdk::MediaSDKString room_id,
                            mediasdk::MediaSDKString user_id,
                            int stream_index,
                            mediasdk::MediaSDKString message) override;
  void OnRoomMessageReceived(mediasdk::MediaSDKString user_id,
                             mediasdk::MediaSDKString message) override;
  void OnUserMessageReceived(mediasdk::MediaSDKString user_id,
                             mediasdk::MediaSDKString message) override;
  void OnStreamMixingEvent(mediasdk::MediaSDKString task_id,
                           int event_type,
                           int error,
                           int mixed_stream_type) override;

  // CanvasEventObserver
  void OnCanvasCreated(const std::string& canvas_id, bool is_shown) override;

  void OnCanvasDestroyed(const std::string& canvas_id) override;

  void OnCanvasItemCreated(const std::string& canvas_item_id,
                           const VisualDeviceInfo& visual_info) override;

  void OnCanvasItemDestroyed(const std::string& canvas_item_id) override;

  // VisualMenuDelegate
  void CreateCanvasItem(
      const std::string& visual_id,
      const mediasdk::CreateCanvasItemParams& params) override;

  void CreateCanvasItemOnCanvas(
      const std::string& visual_id,
      const std::string& canvas_id,
      const std::string& canvas_item_name,
      const mediasdk::CreateCanvasItemParams& params) override;

  std::map<std::string, std::string> GetCanvasesOfNotCurrent() override;

 private:
  void OnModelCreated(bool* result);
  void OnModelDestroyed(bool* result);
  void ResizePreview();
  void ShowLinkMicDialog();
  void ShowVisualSourceMenu(const std::vector<std::string>& source_name);
  void ShowAudioInputSourceMenu(const std::vector<std::string>& source_name);
  void ShowAudioInputWidget(const std::string& title,
                            const std::string& json_info);
  void ShowDShowInputWidget(const std::string& title,
                            const std::string& json_info,
                            int32_t enum_type,
                            const std::string& visual_id);
  void ShowDesktopCaptureWidget(
      const std::string& title,
      const std::string& json_info,
      desktop_capture_visual_source::EnumMethod method);
  void OnShowImageVisualWidget(const std::string& plugin_name);
  void ShowImageVisualWidget(const std::string& plugin_name,
                             const std::string& json);
  void ShowBrowserVisualWidget(const std::string& title);
  void ShowBytelinkVisualWidget(const std::string& title);
  void ShowCurrentVisualMenu(const std::string& canvas_item_id,
                             const std::string& visual_id);
  void ShowGameVisualWidget(const std::string& title, const std::string& json);
  void ShowAudioAECRefWidget(const std::string& audio_id);
  std::string CreateVisual(const std::string& plugin_name,
                           const std::string& json_params);
  void DShowCreateVisual(
      const std::string& plugin_name,
      const std::string& visual_id,
      int32_t capture_type,
      const dshow_visual_source::CaptureParams& vparams,
      const dshow_visual_source::AudioCaptureParams& aparams);
  void CreateDesktopCaptureVisual(
      const std::string& plugin_name,
      const std::string& device_id,
      int32_t method,
      uint64_t item_id,
      int32_t capture_method,
      bool show_cursor,
      bool gdi_compatible,
      desktop_capture_visual_source::WindowCaptureParams wparams);
  void CreateFAVInput(const std::string& visual_id,
                      const std::string& plugin_name,
                      const fav_visual_source::CreateParam& param);
  void OnCreateVisualResult(const std::string& visual_id,
                            const VisualDeviceInfo& visual_info,
                            uint32_t sink_id,
                            bool* result);

  void OnCreateFilteredVisualResult(
      const std::string& visual_id,
      const VisualDeviceInfo& visual_info,
      uint32_t sink_id,
      const std::string& filter_id,
      const mediasdk::CreateVisualFilterParams& filter_params,
      bool* result);

  void OnCreateVisualResultDesktopCapture(const std::string& visual_id,
                                          const VisualDeviceInfo& visual_info,
                                          uint32_t sink_id,
                                          bool gdi_compatible,
                                          bool* result);
  void OnCreateVisualResultAudio(const std::string& visual_id,
                                 const VisualDeviceInfo& visual_info,
                                 uint32_t sink_id,
                                 int32_t monitor_type,
                                 bool* result);
  void OnCreateFAVResult(const std::string& visual_id,
                         const std::string& plugin_name,
                         uint32_t sink_id,
                         bool* result);
  void OnDestroyVisualResult(const std::string& id, bool* result);
  void OnCreateAudioResult(const std::string& id,
                           const std::string& device_id,
                           const std::string& device_name,
                           const std::string& plugin_name,
                           bool from_video,
                           uint32_t sink_id,
                           AudioInputType type,
                           bool* result);
  void OnCreateAudioResultByVisual(const std::string& id,
                                   const std::string& device_id,
                                   const std::string& device_name,
                                   const std::string& plugin_name,
                                   bool from_video,
                                   uint32_t sink_id,
                                   AudioInputType type,
                                   mediasdk::ResultBoolBool* result);
  void OnImageVisualSourceInputWidgetClick(const std::string& plugin_name,
                                           const std::string& image_path);
  void OnBrowserVisualInputWidgetInputItemClick(const std::string& plugin_name,
                                                const std::string& token,
                                                const std::string& dll_path,
                                                int32_t device_id);
  void OnByteLinkVisualInputWidgetItemClick(
      const std::string& plugin_name,
      const bytelink_visual_source::CastmateSDKParams& param);
  void OnByteLinkVisualInputWidgetItemClose(const std::string& id);

  void OnGameVisualInputItemClick(const std::string& plugin_name,
                                  game_visual_source::GameCaptureParams params);

  void OnDestroyAudioResult(const std::string& id, bool* result);
  void OnAudioInputItemAddClick(QListWidgetItem* item);
  void OnAudioAECRefItemSelected(const std::string& audio_input_id,
                                 const std::string& ref_audio_id);
  void CloseOtherWindow();

  void OnDShowEnumInputs(
      const std::string& plugin_name,
      const std::string& visual_id,
      int32_t capture_type,
      int32_t enum_method =
          dshow_visual_source::kCameraEnumMethodCameraAndFormats);

  void OnDesktopCaptureEnumInputs(const std::string& plugin_name);

  void OnGameEnumInputs(const std::string& plugin_name);

  void ShowFAVWidget();
  std::string CurrentFavVisualId();

  void OnDumpCurrentVisualInfo(mediasdk::MediaSDKString* id);

  void DestroyVisual(const std::string& id, bool is_duplicate);

  void UpdateVideoParamsPushButton(bool enable);

  void OnUpdateStreamStatistic(const std::string& info);

  void OnUpdateEncoderStatistic(const std::string& info);

  void OnGetTransform(mediasdk::ResultBoolMSTransform* transform);

  void OnGetFps(float fps);
  void UpdateSharedAppAudioItem(const nlohmann::json& json_root);

  std::string CreateCanvasItemOnVisualWithFilter(
      const std::string& visual_id,
      const std::string& filter_id,
      const mediasdk::CreateCanvasItemParams& params,
      const mediasdk::CreateVisualFilterParams& filter_params,
      const VisualDeviceInfo& visual_info);

 private slots:
  void on_addButtonCanvasList_clicked();
  void on_delButtonCanvasList_clicked();
  void on_addButton_clicked();
  void on_delButton_clicked();
  void on_addAudioSource_clicked();
  void on_delAudioSource_clicked();
  void on_settingsButton_clicked();
  void on_startRecordButton_clicked();
  void on_startStreamButton_clicked();
  void on_videoParamsPushButton_clicked();
  void on_AudioRenderSettings_clicked();
  void on_startLinkMicButton_clicked();
  void on_addButtonTransition_clicked();
  void on_modifyButtonTransition_clicked();
  void on_settingsButtonTransition_clicked();

  void OnAudioInputItemClick(const std::string& plugin_name,
                             const std::string& device_id,
                             const std::string& device_name);
  void OnAudioOutputItemClick(const std::string& plugin_name,
                              const std::string& device_id,
                              const std::string& device_name);
  void OnAudioAppItemClick(const std::string& plugin_name,
                           uint32_t pid,
                           const std::string& device_name);
  void OnDShowVisualInputItemClick(
      const std::string& plugin_name,
      const std::string& visual_id,
      int32_t capture_type,
      const dshow_visual_source::CaptureParams& vparams,
      const dshow_visual_source::AudioCaptureParams& aparams);

  void OnDShowVisualInputItemClickWithFilter(
      const std::string& plugin_name,
      const std::string& visual_id,
      int32_t capture_type,
      const dshow_visual_source::CaptureParams& vparams,
      const dshow_visual_source::AudioCaptureParams& aparams,
      const std::string& filter_name,
      const std::string& filter_id,
      const std::string& filter_params);

  void OnDesktopCaptureVisualInputItemClick(
      const std::string& plugin_name,
      const std::string& device_id,
      int32_t method,
      uint64_t item_id,
      int32_t capture_method,
      bool show_cursor,
      bool gdi_compatible,
      desktop_capture_visual_source::WindowCaptureParams params);
  void OnFAVReopen(const std::string& visual_id, const std::string& fav);
  void OnFAVInputItemCreate(const std::string& visual_id,
                            const std::string& plugin_name,
                            const fav_visual_source::CreateParam& param);
  void OnMainWindowMoved();

  void OnVisualPause(const std::string& visual, bool pause);

  void OnFAVSeek(const std::string& visual, int32_t pos);

  void OnStaticsTimerTimeout();

  void ReopenDShowInput(const VisualDeviceInfo& info,
                        const std::string& visual_id);

  void ShowLinkMicMenu(const QPoint&);

 public slots:
  void OnStartStreamResultSlot(const QString& id, int error_code);
  void OnStreamStoppedSlot(const QString& id, int error_code);
  void OnStreamStoppedManuallySuccess(const QString& id);
  void OnStreamStoppedWithFatalError(const QString& id, int error_code);
  void OnVisualMenuSlot(const VisualMenuSignalStruct& message);
  void OnRTCEvent(int event_type, int code, const QString& event_info);
  void OnShowContextMenu(const QPoint& pos);

 Q_SIGNALS:
  void startStreamResultSignal(const QString& id, int error_code);
  void streamStoppedSignal(const QString& id, int error_code);
  void RTCEventSignal(int event_type, int param, const QString info);
  void SendSignalToParent(const VisualMenuSignalStruct& message);

 private:
  std::string BuildRecordFileNameUseCurrentTime();
  VisualSourceType GetDragFileVisualSourceType(const std::string& file_path);
  void ShowVisualMenu(const std::string& canvas_item_id,
                      const std::string& canvas_item_name,
                      const std::string& visual_id);
  std::map<std::string, std::string> GetComparedCanvasItems(
      const std::string& from_canvas,
      const std::string& to_canvas,
      const std::string& compare_type);

 private:
  uint32_t sink_id_ = 0;
  Ui::ModelWidgetClass ui_;
  AudioInputWidget* audio_input_ui_ = nullptr;
  AudioInputPropertyWidget* audio_input_property_ui_ = nullptr;
  DShowVisualInputWidget* dshow_input_ui_ = nullptr;
  DesktopCaptureVisualInputWidget* desktop_capture_input_ui_ = nullptr;
  ImageVisualSourceInputWidget* image_visual_input_ui_ = nullptr;
  BrowserVisualInputWidget* browser_visual_input_ui_ = nullptr;
  ByteLinkVisualInputWidget* bytelink_visual_input_ui_ = nullptr;
  ModelVideoParamsWidget* model_video_params_ui_ = nullptr;

  AudioRenderParamsWidget* audio_render_params_widget_ui_ = nullptr;

  GameVisualInputWidget* game_visual_params_ui_ = nullptr;
  FAVInputWidget* fav_input_ui_ = nullptr;
  std::string current_push_stream_id_;
  std::string current_record_stream_id_;
  uint32_t pre_window_width_ = 0;
  uint32_t pre_window_height_ = 0;
  std::map<std::string, VisualDeviceInfo> visual_device_info_;
  std::map<std::string, RTCVisualInfo> rtc_visual_info_;
  std::string linkmic_settings_;
  QTimer* timer_statics_;
  bool has_pcm_audio_source_ = false;
  int32_t fix_width_ = 0;
  int32_t fix_height_ = 0;
  mediasdk::MSTransform visual_transform_ = {};
  float visual_fps_ = 0.0f;
  int64_t connected_time_ = 0;
  AudioAECRefWidget* audio_aec_ref_widget_ = nullptr;
  std::unique_ptr<CanvasManager> canvas_mgr_{nullptr};
  std::unique_ptr<TransitionManager> transition_manager_{nullptr};
  std::string current_canvas_;
  ModelProxy* proxy_{nullptr};
  std::unordered_map<std::string, std::vector<ListWidgetItemData>>
      canvas_map_data_;
  std::string transition_property_;
  int64_t transition_duration_ms_;
};
