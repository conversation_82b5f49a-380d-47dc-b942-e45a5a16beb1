#pragma once

#include <mutex>

#include "dshow_capture_decorator.h"
#include "dshow_filter/dshow_capture_base.h"
#include "dshow_state_detector.h"
#include "format_adapter/ff_mjpeg_decode_with_pool.h"
#include "graphics/graphics.h"
#include "graphics/yuv_to_bgra_graphics.h"
#include "mediasdk/public/plugin/audio_input_source.h"
#include "mediasdk/public/plugin/visual_source.h"
#include "mediasdk/utils/frame_rate_calculator.h"
#include "mediasdk/utils/timestamp_balancer.h"
#include "mjpeg_frame_handler.h"

namespace mediasdk {

struct CurrentColorSapce {
  bool mjpeg = false;
  int color_space = 0;
  int video_range = 0;
};

struct DShowRunError {
  std::vector<int32_t> do_reopen_errors;
  std::vector<int32_t> device_occupied_errors;
  int32_t max_ms_notify_noframe = 3000;
};

class DShowVisualSource : public VisualSource,
                          public AudioInputSource,
                          public DShowVideoFrameNotify,
                          public DShowAudioFrameNotify,
                          public DShowOperationNotify,
                          public StateChangeDelegate {
 public:
  explicit DShowVisualSource(VisualProxy* proxy);

  bool Create(const char* json_params);

  void Destroy();

  bool HasAudio() override;

  bool Prepare(int64_t timestamp) override;

  void Convert() override;

  graphics::Texture* GetTexture() override;

  int64_t GetTextureTimestampNS() override;

  bool Pause() override;

  bool Continue() override;

  bool IsPaused() override;

  MediaSDKString GetProperty(const char* key) override;

  bool SetProperty(const char* key, const char* json) override;

  void OnMouseEvent(const char* json_event) override;

  void OnKeyboardEvent(const char* json_event) override;

  const char* GetAudioSourceName() override;

  bool Action(const char* json_params) override;

  void InitGraphicsResource() override;

  void ReleaseGraphicsResource() override;

  const char* GetName() const override;

  bool Reopen(const char* json) override;

  bool NeedSyn() override;

  void SetDeviceVolume(const float volume) override;

  float GetDeviceVolume() override;

  void SetDeviceMute(bool is_mute) override;

  bool GetDeviceMute() override;

  void SetDeviceUsedQPC(bool used) override;

  bool GetDeviceUsedQPC() override;

  bool EnableAlpha() override;

  MSTransform GetExtraTransform() const override;

  float GetFps() override;

  void OnVideoFrameStatus(const VideoCaptureFormat& format);

  // DShowFrameNotify
  // callback when collects a frame
  void OnFrameCollected() override;

  // system callback memory
  void OnVideoFrame(const VideoCaptureFormat& format,
                    const VisualSourceFrame& frame) override;

  // shared ffmpeg avframe buffer
  void OnVideoFrame(const VideoCaptureFormat& format,
                    std::shared_ptr<AVFrame> frame) override;

  void OnFormatDetectResult(VideoRange vr) override;

  // DShowAudioFrameNotify
  void OnAudioFrame(const AudioFormat& format,
                    const AudioSourceFrame& frame,
                    uint64_t timestamp) override;

  // DShowOperationNotify
  void OnDShowContinueError(int error_code,
                            const std::string& video_name) override;

  void OnDShowPauseError(int error_code,
                         const std::string& video_name) override;

  void OnDShowControlState(CameraControlProperty pro, 
                            bool state) override;

  // StateChangeDelegate
  void OnStateChangeDetected(bool state) override;

  int GetRenderFPS() const;

  const char* GetSubTypeName() const override;

 private:
  void AutoCleanVideoBuffers(bool force);

  CameraConfig ParseCameraConfig() const;

  void ParseDShowRunError();

  void ReportDshowEvent(const nlohmann::json& config);

  void ReportDshowCreate(const nlohmann::json& config,
                         bool is_ok,
                         int duration);

  float GetAfterDropFps();

  bool IsFormatChanged(bool mjpeg,
                       mediasdk::ColorSpace color_space,
                       mediasdk::VideoRange video_range) const;

  void UpdateFormat(bool mjpeg,
                    mediasdk::ColorSpace color_space,
                    mediasdk::VideoRange video_range);

  void LogCurrentFormat() const;

 private:
  VisualProxy* proxy_ = nullptr;
  DShowCaptureDecorator capture_;
  std::atomic_bool frame_flip_v_ = false;
  DshowStateDetector detector_;
  int32_t create_index_;
  ThreadSafeFrameRateCalculator rate_calc_;
  ThreadSafeFrameRateCalculator after_drop_rate_calc_;
  TimestampBalancer texture_ts_balancer_;
  int buffered_frames_plan_count_ = 1;
  CurrentColorSapce current_color_space_ = {};
  dshow_visual_source::CaptureType capture_type_ =
      dshow_visual_source::kCaptureTypeCamera;
  std::atomic_bool is_paused_ = false;
  std::unique_ptr<MjpegFrameHandler> mjpeg_frame_handler_;

  std::optional<DShowRunError> dshow_run_error_;
};

}  // namespace mediasdk
