#pragma once

#include <memory>
#include <mutex>
#include <nlohmann/json.hpp>
#include <shared_mutex>
#include "cpu_collector.h"
#include "data_center.h"
#include "disk_collector.h"
#include "gpu_collector.h"
#include "graphics_api.h"
#include "mediasdk/data_center/profiler.h"
#include "memory_collector.h"
#include "visual_fps_store.h"

namespace mediasdk {

namespace config {

struct Config {
  std::string version;
  std::string uid;
  std::string did;
  std::string parfait_host;
  GlobalConfig global_config;
  LyraxAudioConfig lyrax_audio_config;

  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(Config,
                                              version,
                                              uid,
                                              did,
                                              parfait_host,
                                              global_config,
                                              lyrax_audio_config);
};

}  // namespace config

class DataCenterImpl final : public DataCenter {
 public:
  // {
  //   // some global config
  //   "version": "",
  //   "did": "",
  //   "uid": "",
  //   "parfait_host": "",
  //   // some AB Config
  //   "global_config" {
  //     "abr": {
  //        "disable_quic_strategy": true,
  //        "common_up_fast" : 5,
  //        "common_up_slow" : 10,
  //        "common_step" : 6
  //     },
  //     "camera": {
  //        "configs": {
  //          "drop_frame": true,
  //        },
  //        "dshow_run_errors": {
  //          "do_reopen_errors": [1,2,3],
  //          "device_occupied_errors": [1, 2, 3]
  //         }
  //     },
  //     "trace": {
  //        "report_performance" : true
  //     },
  //     "game_crash": {
  //        "capture_games" : ["game1.exe", "game2.exe"]
  //     },
  //     "dll_load": {
  //        "report_dll_failed_reason": true
  //     },
  //     "effect_filter": {
  //        "gl_context_enable_down_grade": true,
  //     },
  //     "max_frame_queue_count": {
  //        "image_visual_source": 3,
  //        "other_visual_source": 1
  //     },
  //   }
  // }
  explicit DataCenterImpl(const std::string& init_config);

  ~DataCenterImpl() override;

  // Component:
  bool Initialize() override;

  void Uninitialize() override;

  // DataCenter:
  bool UpdateGlobalConfig(const std::string& json_info) override;

  bool UpdateABConfig(const std::string& json_info) override;

  std::string GetVersion() override;

  std::string GetSdkVersion() override;

  std::string GetParfaitHost() override;

  std::string GetUserId() override;

  config::LyraxAudioConfig GetLyraxConfig() override;

  int GetAudioChannel() override;

  int GetAudioSampleRate() override;

  int GetMicAddCount() override;

  int GetMicRemoveCount() override;

  void UpdateRenderFPS(uint32_t fps) override;

  int GetRenderFPS() override;

  CpuInfo GetCpuInfo() override;

  bool GetGpuInfo(std::vector<GpuInformation>& infos) override;

  MemoryInfo GetMemoryInfo() override;

  bool GetDiskInfo(int& free_in_mb, int& total_in_mb) override;

  EncoderStatisticInfo GetEncoderStatisticInfo(
      const std::string& stream_id) override;

  StatisticInfo GetStatisticInfo(const std::string& stream_id) override;

  void StartRenderProfiler() override;

  void StartCollectPerformanceMatrics(
      const std::vector<CostThresholdParam>& params) override;

  void SetRealRenderFPS(float fps) override;

  float GetRealRenderFPS() override;

  void SetNoReadyFPS(float fps) override;

  float GetNoReadyFPS() override;

  void SetGpuTaskNum(int num) override;

  int GetGpuTaskNum() override;

  void SetStreamAdaptiveGearStrategy(
      const std::string& stream_id,
      const AdaptiveGearStrategy& strategy) override;

  std::optional<AdaptiveGearStrategy> GetAdaptiveGearStrategy(
      const std::string& stream_id) override;

  void OnAdaptiveSwitchSuccess(const std::string& stream_id) override;

  int GetAdaptiveSwitchSuccessCount(const std::string& stream_id) override;

  void OnAdaptiveAbnormalUp(const std::string& stream_id) override;

  void OnAdaptiveAbnormalDown(const std::string& stream_id) override;

  void OnAdaptiveNormal(const std::string& stream_id) override;

  int64_t GetAdaptiveAbnormalDuration(const std::string& stream_id) override;

  int64_t GetAdaptiveAbnormalUpDuration(const std::string& stream_id) override;

  int64_t GetAdaptiveAbnormalDownDuration(
      const std::string& stream_id) override;

  void AddStreamReconnectCnt(const std::string& stream_id) override;

  int32_t GetStreamReconnectCnt(const std::string& stream_id) override;

  void SetEncoderStatistics(
      uint32_t sink_id,
      const EncoderStatistics& encoder_statistics) override;

  std::optional<EncoderStatistics> GetEncoderStatistics(
      uint32_t sink_id) override;

  void SetStreamStatistics(const std::string& stream_id,
                           const StreamStatistics& statistics) override;

  std::optional<StreamStatistics> GetStreamStatistics(
      const std::string& stream_id) override;

  void ResetStreamStatus(const std::string& stream_id) override;

  void SetStreamStart(const std::string& stream_id,
                      const StreamStart& start) override;

  std::optional<StreamStart> GetStreamStart(
      const std::string& stream_id) override;

  std::optional<std::string> GetStreamPushSessionId(
      const std::string& stream_id) override;

  std::optional<std::string> GetStreamConnectSessionId(
      const std::string& stream_id) override;

  void UpdateStreamConnectSessionId(const std::string& stream_id) override;

  bool UpdateStreamFirstConnectStartEventTimeStampMS(
      const std::string& stream_id,
      int64_t ms) override;

  std::optional<int64_t> GetStreamFirstConnectStartEventTimeStampMS(
      const std::string& stream_id) override;

  void OnFirstVideoFrame(const std::string& stream_id);

  bool GetFirstVideoFrame(const std::string& stream_id);

  void OnFirstAudioFrame(const std::string& stream_id);

  bool GetFirstAudioFrame(const std::string& stream_id);

  void OnFirstVideoPacket(const std::string& stream_id);

  bool GetFirstVideoPacket(const std::string& stream_id);

  void OnFirstAudioPacket(const std::string& stream_id);

  bool GetFirstAudioPacket(const std::string& stream_id);

  void OnFirstVideoSend(const std::string& stream_id);

  bool GetFirstVideoSend(const std::string& stream_id);

  void OnFirstAudioSend(const std::string& stream_id);

  bool GetFirstAudioSend(const std::string& stream_id);

  void SetSourceFPS(const SourceFPS& fps) override;

  std::optional<SourceFPS> GetSourceFPS() override;

  void SetVideoEncoderBw(const std::string& stream_id,
                         const int32_t& v) override;

  std::optional<int32_t> GetVideoEncoderBw(
      const std::string& stream_id) override;

  void SetStreamSendDelay(const std::string& stream_id,
                          const int64_t& delay) override;

  std::optional<int64_t> GetStreamSendDelay(
      const std::string& stream_id) override;

  void AddStreamTransportReconnectDuration(const std::string& stream_id,
                                           const int64_t duration) override;

  void AddStreamTransportReconnectCount(const std::string& stream_id) override;

  void SetStreamTransportReconnect(
      const std::string& stream_id,
      const StreamTransportReconnect& transport_reconnect) override;

  std::optional<StreamTransportReconnect> GetStreamTransportReconnect(
      const std::string& stream_id) override;

  void SetStreamSendPackageDelay(const std::string& stream_id,
                                 const int64_t& delay) override;

  std::optional<int64_t> GetStreamSendPackageDelay(
      const std::string& stream_id) override;

  void SetSinkMapAndPresentStatistics(
      const uint32_t& sink_id,
      const SinkMapAndPresentStatistics& sink_map_and_present_statistics)
      override;

  std::optional<SinkMapAndPresentStatistics> GetSinkMapAndPresentStatistics(
      const uint32_t& sink_id) override;

  std::optional<RtcStatus> GetRtcStatus() override;

  void SetRtcStatus(std::optional<RtcStatus> rtc_status) override;

  std::shared_ptr<DeviceInfoArray> GetDeviceInfo() override;

  config::AbrConfig GetAbrConfig() override;

  nlohmann::json GetABConfig(const std::string& key) const override;

  graphics::AdapterInfo GetCurrentAdapterInfo() const override;

  std::vector<graphics::GpuAdapterInfo> GetAdapters() const override;

  config::TraceConfig GetTraceConfig() override;

  config::DllLoadConfig GetDllLoadConfig() override;

  config::CaeConfig GetCaeConfig() override;

  config::TcpConfig GetTcpConfig() override;

  config::CameraConfig GetCameraConfig() override;

  config::RTCConfig GetRTCConfig() override;

  config::MjpegVideoRangeConfig GetMjpegVideoRangeConfig() override;

  config::QuicSleepConfig GetQuicSleepConfig() override;

  config::QsvDtsConfig GetQsvDtsConfig() override;

  config::PushSessionIdConfig GetPushSessionIdConfig() override;

  VisualFpsStore& GetVisualFpsStore() override;

  AudioPerformanceStore& GetAudioPerformanceStore() override;

  void SetTTNtp(const TTNtp& tt_ntp) override;

  int64_t ChangeToTTNtpMS(int64_t sdk_ns) override;

  int64_t ChangeToLocalMS(int64_t sdk_ns) override;

  bool IsUseNewVersionPushingTimeStamp() override;

  std::optional<int64_t> GetLocaltoNtpDiffMS() override;

  bool EnableEndtoEndDelay() override;

  void RegistEndtoEndDelay(EndtoEndDelay* e2e_delay) override;

  void UnregistEndtoEndDelay(EndtoEndDelay* e2e_delay) override;

  void FillEndtoEndDelay(event_tracking_data::PushStream& data) override;

 private:
  void ResetProfiler();
  void ReportProfiler();
  void OnProfiler(const char* name, const ProfilerDataItem& data);
  void OnCostProfiler(const char* name, const CostProfilerDataItem& data);
  void ReportCostProfiler();

  std::atomic_int meta_fps_ = 0;

  mutable std::shared_mutex config_mutex_;
  config::Config config_;
  nlohmann::json raw_config_json_;

  std::mutex buffered_render_data_mutex_;
  nlohmann::json buffered_render_data_;

  std::mutex buffered_render_data_cost_mutex_;
  nlohmann::json buffered_render_data_cost_;

  std::shared_ptr<cpu_collector::CpuCollector> cpu_collector_;
  std::shared_ptr<gpu_collector::GpuCollector> gpu_collector_;
  std::shared_ptr<memory_collector::MemoryCollector> memory_collector_;
  std::shared_ptr<disk_collector::DiskCollector> disk_collector_;
  std::vector<graphics::GpuAdapterInfo> gpu_adapters_;

  mutable std::shared_mutex statistics_mutex_;

  std::atomic<float> render_fps_ = 0.f;
  float no_ready_fps_ = 0.0f;
  int gpu_task_ = 0;

  std::optional<SourceFPS> source_pfs_;

  struct StreamInfo {
    int32_t reconnect_count = 0;
    std::string push_session_id;
    std::string connect_session_id;
    bool first_video_frame = true;
    bool first_audio_frame = true;
    bool first_video_packet = true;
    bool first_audio_packet = true;
    bool first_video_send = true;
    bool first_audio_send = true;
    std::optional<StreamStart> start;
    std::optional<int64_t> first_connected_timestamp_ms;
    std::optional<int32_t> encode_bw;
    std::optional<int64_t> delay;
    std::optional<int64_t> package_delay_ms;
    std::optional<StreamStatistics> statistics;
    std::optional<StreamTransportReconnect> transport_reconnect;
    std::optional<StreamAdaptiveStatistics> adaptive_stat;
  };

  std::map<std::string, StreamInfo> stream_info_map_;

  mutable std::shared_mutex adaptive_mutex_;
  std::map<std::string, AdaptiveGearStrategy> stream_adaptive_map_;

  struct SinkInfo {
    std::optional<EncoderStatistics> encoder_statistics;
    std::optional<SinkMapAndPresentStatistics> sink_map_and_present_statistics;
  };

  std::map<uint32_t, SinkInfo> sink_info_map_;

  std::mutex tea_buffer_mutex_;
  nlohmann::json tea_buffer_;
  VisualFpsStore visual_fps_store_;
  AudioPerformanceStore audio_performace_store_;

  std::shared_mutex tt_ntp_mutex_;
  std::optional<TTNtp> tt_ntp_;

  std::mutex e2e_delay_mutex_;
  std::vector<EndtoEndDelay*> e2e_delay_arr_;
  int64_t last_collect_cost_ms_ = 0;
  std::map<std::string, int64_t> last_cost_map_;
  std::optional<RtcStatus> rtc_status_ = std::nullopt;
};

}  // namespace mediasdk
