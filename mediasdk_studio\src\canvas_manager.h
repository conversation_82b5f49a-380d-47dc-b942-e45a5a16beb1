#pragma once

#include <string>
#include <unordered_map>
#include "common_defs.h"
#include "mediasdk/public/mediasdk_defines_canvas.h"

class CanvasEventObserver {
 public:
  virtual ~CanvasEventObserver() = default;

  virtual void OnCanvasCreated(const std::string& canvas_id, bool is_shown) = 0;

  virtual void OnCanvasDestroyed(const std::string& canvas_id) = 0;

  virtual void OnCanvasItemCreated(const std::string& canvas_item_id,
                                   const VisualDeviceInfo& visual_info) = 0;

  virtual void OnCanvasItemDestroyed(const std::string& canvas_item_id) = 0;
};

class CanvasManager {
 public:
  CanvasManager(CanvasEventObserver* observer);

  ~CanvasManager();

  bool CreateCanvas(const std::string& canvas_id,
                    uint32_t parent_id,
                    bool is_shown);

  void DestroyCanvas(const std::string& canvas_id);

  bool ContainCanvas(const std::string& canvas_id);

  void ShowCanvas(const std::string& canvas_id,
                  const std::string& transition_id);

  void SetCurrentCanvas(const std::string& canvas_id);

  std::vector<std::string> GetItems(const std::string& canvas_id);

  std::string CurrentCanvas() const { return current_canvas_; }

  std::string GetCopyCanvas() {
    std::string copy_canvas_id = current_canvas_ + "_copy";
    for (const auto& pair : canvas_map_parent_) {
      if (pair.first == copy_canvas_id) {
        return copy_canvas_id;
      }
    }
    CreateCanvas(copy_canvas_id, canvas_map_parent_[current_canvas_], false);
    return copy_canvas_id;
  }

  void CreateCanvasItem(const std::string& canvas_id,
                        const std::string& canvas_item_id,
                        const std::string& visual_id,
                        const mediasdk::CreateCanvasItemParams& params,
                        const VisualDeviceInfo& visual_info);

  void CreateCanvasItemWithFilter(
      const std::string& canvas_id,
      const std::string& canvas_item_id,
      const std::string& visual_id,
      const std::string& filter_id,
      const mediasdk::CreateCanvasItemParams& params,
      const mediasdk::CreateVisualFilterParams& filter_params,
      const VisualDeviceInfo& visual_info);

  void DestroyCanvasItem(const std::string& canvas_item_id);

  void BindVisualToCanvasItem(const std::string& visual_id,
                              const std::string& canvas_item_id);

  void OnCanvasCreated(const std::string& canvas_id,
                       bool is_shown,
                       bool* result);

  void OnCanvasDestroyed(const std::string& canvas_id, bool* result);

  void OnCanvasItemCreated(const std::string& canvas_item_id,
                           const VisualDeviceInfo& visual_info,
                           bool* result);

  void OnCanvasItemDestroyed(const std::string& canvas_item_id, bool* result);

 private:
  CanvasEventObserver* observer_;
  std::unordered_map<std::string, uint32_t> canvas_map_parent_{};
  std::unordered_map<std::string, std::vector<std::string>>
      canvas_and_items_map_;
  std::unordered_map<std::string, std::string> item_map_visual_{};
  std::unordered_map<std::string, std::vector<std::string>> visual_map_items_{};
  std::string current_canvas_;
};
