#pragma once

#include <memory>

#include <video_capture_format.h>
#include <nlohmann/json.hpp>
#include "audio/audio_format.h"
#include "audio_filter.h"
#include "dshow_visual_source_helper.h"
#include "plugin/visual_proxy.h"
#include "video_filter.h"

#include <optional>
#include "dshow_capture_log.h"
#include "format_adapter/ff_mjpeg_decode_with_pool.h"
#include "format_adapter/format_detector.h"
#include "format_adapter/video_format_transform.h"
#include "frame_dropper.h"

namespace mediasdk {

class DShowVideoFrameNotify {
 public:
  virtual ~DShowVideoFrameNotify() = default;

  virtual void OnFrameCollected() = 0;

  virtual void OnVideoFrame(const VideoCaptureFormat& format,
                            const VisualSourceFrame& frame) = 0;

  virtual void OnVideoFrame(const VideoCaptureFormat& format,
                            std::shared_ptr<AVFrame> frame) = 0;

  virtual void OnFormatDetectResult(VideoRange vr) = 0;
};

class DShowAudioFrameNotify {
 public:
  virtual ~DShowAudioFrameNotify() = default;

  virtual void OnAudioFrame(const AudioFormat& format,
                            const AudioSourceFrame& frame,
                            uint64_t timestamp) = 0;
};

class DShowOperationNotify {
 public:
  virtual ~DShowOperationNotify() = default;

  virtual void OnDShowContinueError(int errorcode,
                                    const std::string& video_name) = 0;

  virtual void OnDShowPauseError(int errorcode,
                                 const std::string& video_name) = 0;

  virtual void OnDShowControlState(CameraControlProperty pro, 
                                    bool state) = 0;
};

// clang-format off
// Camera
//  ----------------         ------------         --------------
// | video name    v|  -->  |           v|  -->  |              |
// | Capture Filter |       | AVI Filter |       | Video Filter |
// |                |       |  If Needed |       |              |
//  ----------------         ------------         --------------
//               if not support mjpeg AddAVIFilter
// 
// Analog video and audio device is same
//  ------------         ----------------         --------------         --------------
// |  Crossbar v|  -->  | video name    v|  -->  |  AVI  Filter |  -->  | Video Filter |
// |            |       | Capture Filter |        --------------        |              |
// |   Filter  a|  -->  | audio name    a|  ------------------------->  | Audio Filter | Audio Filter is Can Audio Filter or Audio Render[WaveOut/DSound]
//  ------------         ----------------                                --------------
// Analog video and audio device is not same
//  ----------------         ------------         --------------
// | video name    v|  -->  |           v|  -->  |              |
// | Capture Filter |       | AVI Filter |       | Video Filter |
// |                |       |  If Needed |       |              |
//  ----------------         ------------         --------------
//  ----------------         --------------
// |                |       |              |
// | Capture Filter |       | Audio Filter | Audio Filter is Can Audio Filter or Audio Render[WaveOut/DSound]
// | audio name    a|  -->  |              |
//  ----------------         --------------
// clang-format on

struct CameraConfig {
  bool is_drop_frame_enable = false;
  bool mjpeg_video_range = false;
  bool use_pause = true;
};

class DShowCaptureBase : public VideoFilterCallBack,
                         public AudioFilterCallBack,
                         public FormatDetectorDelegate {
 public:
  enum DShowCaptureType {
    kDShowCaptureTypeCamera,
    kDShowCaptureTypeAnalog,
  };

  explicit DShowCaptureBase();

  ~DShowCaptureBase() override;

  virtual bool Create(const nlohmann::json& json_root);

  virtual void Destroy();

  virtual bool Reopen(const nlohmann::json& json_root);

  virtual DShowCaptureType GetDShowType() = 0;

  virtual void CleanVideoTransFormBuffers();

  virtual bool HasAudio() = 0;

  virtual BOOL Start(HRESULT* phr = NULL);

  virtual BOOL Stop();

  virtual BOOL Pause(HRESULT* phr = NULL);

  virtual BOOL Continue(HRESULT* phr = NULL);

  virtual BOOL IsPaused();

  virtual BOOL GetState(long time_out_ms, FILTER_STATE& state);

  virtual BOOL IsRunningOrPause(FILTER_STATE& state);

  virtual HRESULT ConnectFilter(const VideoCaptureFormat& ft);

  virtual HRESULT ConnectFilter(const AudioCaptureFormat& ft);

  virtual void DisConnectFilter();

  virtual const char* GetSubTypeName() = 0;

  dshow_visual_source::CreateStatus GetLastCreateError();

  std::string GetVideoName();

  void SetLastCreateError(dshow_visual_source::CreateStatus error);

  std::vector<VideoProcAmpStruct> GetVideoProcAmp();

  bool SetVideoProcAmp(VideoProcAmpProperty pro, long value, long flag);

  std::vector<CameraControlStruct> GetCameraControl();

  bool SetCameraControl(CameraControlProperty pro, long value, long flag);

  VideoCaptureFormat GetCaptureVideoFormat();

  AudioCaptureFormat GetCaptureAudioFormat();

  void SetCameraConfig(const CameraConfig& camera_config);

  void SetLimitCaptureFPS(int limit_fps);

  void SetRenderFPS(int render_fps);

  void StartVideoRangeDetect(const int32_t detect_interval,
                             const int32_t limited_detect_count);

  void StopVideoRangeDetect();

  void SetNotify(DShowVideoFrameNotify* video_notify,
                 DShowAudioFrameNotify* audio_notify,
                 DShowOperationNotify* operation_notify);

  FILTER_STATE WaitForRunning(long time_out_ms = 200, int times = 5);

  // FormatDetectorDelegate
  void OnFormatDetectResult(VideoRange vr) override;

  // VideoFilterCallBack
  void OnVideoFormatChange(const AM_MEDIA_TYPE* mtype) override;

  void OnVideoFrameReceive(const uint8_t* data,
                           int32_t size,
                           int64_t tm) override;

  void OnVideoFrameReceiveCnts() override;

  // AudioFilterCallBack
  void OnAudioFormatChange(const AM_MEDIA_TYPE* mtype) override;

  virtual void OnAudioFrameReceive(const uint8_t* data,
                                   int32_t size,
                                   int64_t tm) {};

 protected:
  virtual HRESULT OpenDevice(const DShowDeviceName& video_name,
                             const DShowDeviceName& audio_name,
                             dshow_visual_source::AnalogRenderType render_type);

  virtual HRESULT AddVideoFilterToGraph(const DShowDeviceName& video_name);

  virtual HRESULT AddAudioFilterToGraph(const DShowDeviceName& video_name,
                                        const DShowDeviceName& audio_name);

  virtual void CloseDevice();

  void SetAntiFlickerInCaptureFilter();

  Microsoft::WRL::ComPtr<IBaseFilter> AdapterCrossbarFilterInGraph(
      IBaseFilter* filter);

  void StopInner();

  void StopInnerSafe();

  bool GetVideoProcAmp(IAMVideoProcAmp* amp,
                       VideoProcAmpProperty pro,
                       bool get,
                       VideoProcAmpStruct& st);

  bool GetCameraControl(IAMCameraControl* amp,
                        CameraControlProperty pro,
                        bool get,
                        CameraControlStruct& st);

  struct MediaTypeParam {
    int index;
    REFERENCE_TIME min_frame_interval;
    REFERENCE_TIME max_frame_interval;
    REFERENCE_TIME avg_time_per_frame;
  };

  HRESULT GetClosestVideoMediaType(IAMStreamConfig* stream_config,
                                   const VideoCaptureFormat& ft,
                                   ScopedMediaType& pmt,
                                   REFERENCE_TIME& device_avg_time_per_frame);

  HRESULT GetClosestAudioMediaType(IAMStreamConfig* stream_config,
                                   const AudioCaptureFormat& ft,
                                   ScopedMediaType& pmt);

  std::vector<MediaTypeParam> GetSameFormatAndSizeByConfig(
      IAMStreamConfig* stream_config,
      PixelFormat format,
      int width,
      int height,
      int& config_size);

  HRESULT AddAVIFilter(VideoCaptureFormat& request,
                       ScopedMediaType& media_type);

  bool IsAnalogType();

  ALLOCATOR_PROPERTIES GetSuitabilityPro(int32_t block);

  bool ShouldDropFrame(int64_t timestamp_ns);

  void OnAudioFrame(const AudioFormat& format,
                    const AudioSourceFrame& frame,
                    uint64_t timestamp);

 private:
  void UpdataVideoRangeDetector();

  bool ModifySetFormat(AM_MEDIA_TYPE* pmt,
                       const REFERENCE_TIME& device_avg_time_per_frame);

  Microsoft::WRL::ComPtr<IBaseFilter> TryGetDeviceBaseFilter(
      const DShowDeviceName& name,
      const CLSID& id,
      int try_times);

 protected:
  std::recursive_mutex lock_video_setting_;
  DShowVideoFrameNotify* video_notify_;

  std::recursive_mutex lock_audio_setting_;
  DShowAudioFrameNotify* audio_notify_;

  std::recursive_mutex lock_operation_setting_;
  DShowOperationNotify* operation_notify_;

  DShowDeviceName video_name_;
  VideoCaptureFormat video_format_;

  DShowDeviceName audio_name_;
  AudioCaptureFormat audio_format_;

  Microsoft::WRL::ComPtr<IGraphBuilder> builder_;
  Microsoft::WRL::ComPtr<ICaptureGraphBuilder2> builder2_;
  Microsoft::WRL::ComPtr<IMediaEvent> event_;
  Microsoft::WRL::ComPtr<IMediaControl> control_;

  Microsoft::WRL::ComPtr<IBaseFilter> video_capture_filter_;
  Microsoft::WRL::ComPtr<IPin> video_capture_filter_out_pin_;

  Microsoft::WRL::ComPtr<IBaseFilter> audio_capture_filter_;
  Microsoft::WRL::ComPtr<IPin> audio_capture_filter_out_pin_;

  Microsoft::WRL::ComPtr<IBaseFilter> cross_bar_;

  Microsoft::WRL::ComPtr<IBaseFilter> video_avi_filter_;
  Microsoft::WRL::ComPtr<IPin> video_avi_filter_out_pin_;
  Microsoft::WRL::ComPtr<IPin> video_avi_filter_in_pin_;

  VideoFilter* video_render_filter_ = nullptr;
  Microsoft::WRL::ComPtr<IPin> video_render_filter_in_pin_;

  dshow_visual_source::AnalogRenderType render_type_;
  Microsoft::WRL::ComPtr<IPin> audio_render_type_filter_in_pin_;
  Microsoft::WRL::ComPtr<IBaseFilter> audio_render_type_filter_;

  AudioFilter* audio_render_filter_ = nullptr;
  Microsoft::WRL::ComPtr<IPin> audio_render_filter_in_pin_;

  std::unique_ptr<VideoFormatTransform> video_transform_;

  DShowCaptureLog log_helper_;

  FrameDropper dropper_{};
  std::atomic_int max_fps_ = INT_MAX;
  std::atomic_int render_fps_ = INT_MAX;
  int final_limit_fps_ = 0;
  CameraConfig camera_config_{};
  std::unique_ptr<FormatDetector> format_detector_ = nullptr;

  struct DetectParam {
    int32_t detect_interval = 0;
    int32_t limited_detect_count = 0;
  };

  std::optional<DetectParam> detect_param_ = {};

  dshow_visual_source::CreateStatus last_create_error_ = {};

  bool stop_instead_pause_ = false;
};

}  // namespace mediasdk