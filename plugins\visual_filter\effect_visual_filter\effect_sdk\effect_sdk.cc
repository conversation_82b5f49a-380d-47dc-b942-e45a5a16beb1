﻿#include "effect_sdk.h"

#include <algorithm>
#include <chrono>
#include <mutex>
#include "algo_effectsdk_processor.h"
#include "base/check.h"
#include "base/logging.h"
#include "bef_color_converter.h"
#include "effect_config.h"
#include "effect_context.h"
#include "effect_gl_sync.h"
#include "effect_processor.h"
#include "effect_statistics.h"
#include "effect_visual_filter_helper.h"
#include "mediasdk/public/effect_platform/effect_bef_api.h"
#include "nlohmann/json.hpp"
#include "normal_effectsdk_processor.h"
#include "scope_profiler.h"
#include "time_helper.h"

namespace {

constexpr int kGLMaxWaitTimeMs = 2000;
constexpr int kGLVersion3 = 3;
constexpr int kAlgoGLVersion = 3;
constexpr char kProfilterEffectSDK[] = "EffectSDK";
constexpr char kProfilterEffectAlgorithm[] = "EffectAlgorithm";
constexpr char kProfilterEffectRender[] = "EffectRender";
constexpr char kProfilterEffectEnvironmentTrans[] = "EffectEnvironmentTrans";
constexpr char kProfilterEffectGLFlush[] = "EffectGLFlush";

const char* LOG_PREFIXS[] = {"ERROR",  "WARN",   "INFO",   "DEBUG", "VERBOSE",
                             "UNKNOW", "UNKNOW", "UNKNOW", "UNKNOW"};

static constexpr char kSeparators[3] = "\\/";

int EffectLogCallback(const int logLevel, const char* msg) {
  if (msg && logLevel <= 2) {
    LOG(INFO) << "[" << LOG_PREFIXS[logLevel] << "] " << msg;
  }
  return 0;
}

// The AB here will involve the calling and usage of the algorithm(algo).
// If shared_tex is used instead of the correct settings,
// a timeout will occur when using acquire(d3d key mutex)
// (i.e. release is not called internally in the effect library)
// before create handle
bool ApplyABConfig(const std::string& ab_config) {
#define EPSILON 1e-4f
  // default value
  {
    bool val = true;
    bef_effect_result_t res = mediasdk::bef_api::ms_bef_effect_config_ab_value(
        "enable_mute_ab_logging", &val, 0);
    if (0 != res) {
      LOG(ERROR) << "[Effect] bef_effect_config_ab_value "
                    "(enable_mute_ab_logging) failed:"
                 << res << ", val:" << val;
    }
  }

  // parse json string and set value to ab config
  try {
    nlohmann::json ab_json = nlohmann::json::parse(ab_config);
    if (!ab_json.contains("configs") || !ab_json.at("configs").is_object()) {
      return false;
    }
    for (auto& pair : ab_json.at("configs").items()) {
      const std::string& key = pair.key();
      auto& value = pair.value();
      if (value.is_boolean()) {
        bool v = value.get<bool>();
        mediasdk::bef_api::ms_bef_effect_config_ab_value(key.c_str(), &v, 0);
      } else if (value.is_number()) {
        double num = value.get<double>();
        if (std::abs(num - std::floor(num)) < EPSILON) {
          int v = static_cast<int>(num);
          mediasdk::bef_api::ms_bef_effect_config_ab_value(key.c_str(), &v, 1);
        } else {
          mediasdk::bef_api::ms_bef_effect_config_ab_value(key.c_str(), &num,
                                                           2);
        }
      } else if (value.is_string()) {
        std::string v = value.get<std::string>();
        mediasdk::bef_api::ms_bef_effect_config_ab_value(key.c_str(), &v, 3);
      }
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "[Effect] Catch exception: " << e.what()
               << " json string: " << ab_config;
    return false;
  }

  return true;
}

constexpr unsigned int kEffectMsgIDResourceStatus = 0x11;
constexpr int kEffectArgComposerFirstFrame = 0x0A;
constexpr int kEffectComposerReplay = 0x39;
constexpr int kEffectComposerReplayFirstFrame = 3;
constexpr int kLagJudgeSize = 2;

}  // namespace

namespace mediasdk {

using namespace bef_api;

EffectSDK::EffectSDK(bef_effect_handle_t effect_handle,
                     int init_width,
                     int init_height,
                     std::shared_ptr<EffectContext> context,
                     EffectNotifyDelegate* notify_delegate,
                     const EffectSDKConfig& sdk_config)
    : effect_handle_(effect_handle),
      context_(context),
      tex_width_(init_width),
      tex_height_(init_height),
      notify_delegate_(notify_delegate),
      sdk_config_(sdk_config) {
  DCHECK(context);
  DCHECK(notify_delegate);
  if (sdk_config.enable_first_frame_statistics) {
    effect_statistics_ = std::make_unique<EffectStatistics>(this);
  }
}

EffectSDK::~EffectSDK() {
  auto gl_current = context_->MakeGLDefaultCurrent();
  if (manager_) {
    bef_api::ms_bef_render_msg_delegate_manager_remove(manager_, this,
                                                       &EffectSDK::BEFDelegete);
    bef_api::ms_bef_render_msg_delegate_manager_destroy(&manager_);
    manager_ = nullptr;
  }
  if (effect_handle_) {
    LOG(INFO) << "[effect] destroy handle:0x" << std::hex << effect_handle_;
    bef_api::ms_bef_effect_destroy(effect_handle_);
    effect_handle_ = nullptr;
  }
  if (dst_tex_) {
    render_->deleteTexture(dst_tex_);
    dst_tex_ = 0;
  }

  if (effect_statistics_) {
    effect_statistics_.reset();
  }
  LOG(INFO) << "[effect] sdk algorithm(err count:" << algorithm_err_count_
            << "), process(err count:" << process_err_count_ << ")";
}

// static
std::unique_ptr<EffectSDK> EffectSDK::Create(
    int init_width,
    int init_height,
    std::shared_ptr<EffectContext> context,
    EffectNotifyDelegate* notify_delegate,
    const EffectSDKConfig& sdk_config) {
  DCHECK(context);

  bef_effect_result_t res =
      bef_api::ms_bef_effect_set_log_to_local_func(EffectLogCallback);
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_set_log_to_local_func callback failed:"
               << res;
  }

  // AB need before create handle
  ApplyABConfig(sdk_config.lib_effect_ab_config);

  bef_effect_handle_t effect_handle = nullptr;
  res = bef_api::ms_bef_effect_create_handle(&effect_handle, false);
  DCHECK(BEF_RESULT_SUC == res);

  if (BEF_RESULT_SUC != res) {
    LOG(ERROR) << "[Effect] create handle failed:" << res;
    return nullptr;
  }
  LOG(INFO) << "[Effect] create handle:0x" << std::hex << effect_handle;
  auto ptr = std::make_unique<EffectSDK>(effect_handle, init_width, init_height,
                                         context, notify_delegate, sdk_config);

  if (!ptr->SetupSDK()) {
    LOG(ERROR) << "[Effect] setup sdk failed";
    ptr.reset();
  }
  return ptr;
}

bool EffectSDK::SetupSDK() {
  auto gl_current = context_->MakeGLDefaultCurrent();
  if (!gl_current) {
    LOG(ERROR) << "[Effect] make current failed";
    return false;
  }

  LOG(INFO) << "[Effect] effect version" << GetEffectVersion();

  use_algo_ =
      sdk_config_.expect_algo && context_->GetGLESVersion() == kAlgoGLVersion;

  if (use_algo_) {
    sdk_processor_.reset(new AlgoEffectSDKProcessor(notify_delegate_));
  } else {
    sdk_processor_.reset(new NormalEffectSDKProcessor());
  }
  if (!sdk_processor_->InitPipeline(effect_handle_)) {
    LOG(ERROR) << "[Effect] init pipeline failed";
    return false;
  }
  if (!SetRenderApi()) {
    LOG(ERROR) << "[Effect] set render api failed";
    return false;
  }
  EffectConfigAB();

  // call in SDK
  if (!ep::UseFinder(this)) {
    LOG(ERROR) << "[Effect] use finder failed";
    return false;
  }

  auto res = ms_bef_effect_composer_set_mode(effect_handle_, 1, 0);
  if (0 != res) {
    LOG(ERROR) << "[Effect] ms_bef_effect_composer_set_mode failed:" << res;
    return false;
  }
  res = ms_bef_effect_set_camera_device_position(effect_handle_,
                                                 bef_camera_position_back);
  if (0 != res) {
    LOG(ERROR) << "[Effect] ms_bef_effect_set_camera_device_position failed:"
               << res;
    return false;
  }
  ms_bef_render_msg_delegate_manager_init(&manager_);
  ms_bef_render_msg_delegate_manager_add(manager_, this,
                                         &EffectSDK::BEFDelegete);

  bool pre_compiled_shader = sdk_config_.enable_precompiled_shader;
  render_.reset(BEF::BEFColorConverterFactory::createTransform());
  if (0 != render_->init(pre_compiled_shader)) {
    LOG(ERROR) << "[Effect] BEFColorConverter init failed";
    return false;
  }

  LOG(INFO) << "[Effect] effect sdk setup success";
  return true;
}

bool EffectSDK::Process(GLuint src_tex,
                        EGLSurface surface,
                        int src_width,
                        int src_height,
                        int dst_width,
                        int dst_height,
                        int64_t pts_ns,
                        std::unique_ptr<MSTransform> gl_trans) {
  if (!necessary_composer_finished && necessary_composers_.empty()) {
    necessary_composer_finished = true;
    notify_delegate_->NotifyReady();
  }
  auto begin_time =
      (base::TimeTicks::Now() - base::TimeTicks()).InNanoseconds();
  if (!surface) {
    return false;
  }

  bool need_update_trans = NeedUpdateTransform(gl_trans.get());
  if (need_update_trans) {
    UpdateTransform(std::move(gl_trans));
  }

  ScopedProfilerNameProxy a(notify_delegate_, kProfilterEffectSDK);
  ScopedProfilerCostNameProxy b(notify_delegate_, kProfilterEffectSDK);

  auto gl_current = context_->MakeGLDefaultCurrent();
  if (!gl_current) {
    return false;
  }
  if (!ProcessPrepare(src_width, src_height)) {
    return false;
  }

  const double bef_time_s = pts_ns / 1000000000.0;
  if (!EffectSetSize(src_width, src_height)) {
    return false;
  }
  if (!sdk_processor_->PrepareRender(context_.get(), src_tex, src_width,
                                     src_height)) {
    return false;
  }
  if (!EffectAlgorithm(src_tex, bef_time_s, need_update_trans)) {
    return false;
  }
  if (sdk_processor_->NeedRecontextAfterAlgorithm()) {
    gl_current->Recurrent();
  }
  if (!EffectProcess(src_tex, dst_tex_, bef_time_s)) {
    return false;
  }
  gl_current->ChangeCurrrentSurface(surface);
  if (!RenderToSurface(dst_tex_, dst_width, dst_height)) {
    return false;
  }
  if (!FinishRender(kGLMaxWaitTimeMs)) {
    return false;
  }
  ProcessStatistics(begin_time);
  return true;
}

void EffectSDK::ProcessStatistics(int64_t begin_time) {
  auto cur_cost =
      (base::TimeTicks::Now() - base::TimeTicks()).InNanoseconds() - begin_time;

  if (sdk_config_.enable_stuttering_status_statistics) {
    if (time_costs_.size() < kLagJudgeSize) {
      time_costs_.push_back(cur_cost);
    } else {
      float average_time_before = 0;
      for (auto it = time_costs_.begin(); it != time_costs_.end(); ++it) {
        average_time_before += *it;
      }
      average_time_before /= time_costs_.size();
      time_costs_.pop_front();
      float lagValue =
          1000.0f / static_cast<float>(notify_delegate_->GetRenderFps());
      float cur_cost_in_ms = static_cast<float>(cur_cost) / 1000000;
      if (average_time_before * 2 < cur_cost && cur_cost_in_ms > lagValue &&
          composers_.size() > 0) {
        bool is_first_frame = false;
        char* data = nullptr;
        int size = 0;
        auto res =
            ms_bef_effect_get_et_data(effect_handle_, 1 << 10, &data, &size);
        if (res == 0) {
          try {
            nlohmann::json data_json = nlohmann::json::parse(data, data + size);
            if (data_json.contains("is_first_frame")) {
              auto& value = data_json["is_first_frame"];
              if (value.is_number_integer()) {
                int num = value.get<int>();
                if (num == 1) {
                  is_first_frame = true;
                }
              }
            }
          } catch (const std::exception& e) {
            LOG(ERROR) << "[Effect] Catch exception: " << e.what()
                       << " et data parse failed. ";
          }
        }
        ms_bef_effect_free_raw_buffer(effect_handle_, data);
        std::string composers;
        for (auto& composer : composers_) {
          composers += composer;
          composers += ",";
        }
        composers.pop_back();
        int64_t current_time_ms = milli_now();
        int type = 1;
        if (cur_cost_in_ms > 2 * lagValue) {
          type = 2;
        }
        nlohmann::json event_json = {{"effect_info", composers},
                                     {"is_first_frame", is_first_frame},
                                     {"ts", current_time_ms},
                                     {"cost", static_cast<int>(cur_cost_in_ms)},
                                     {"type", type}};
        notify_delegate_->Notify(effect_visual_filter::kNotifyEffectLag,
                                 event_json.dump());
        time_costs_.clear();
      }
    }
  }
}

bool EffectSDK::ProcessPrepare(int width, int height) {
  if (width != tex_width_ || height != tex_height_) {
    if (dst_tex_) {
      render_->deleteTexture(dst_tex_);
      dst_tex_ = 0;
    }
    tex_width_ = width;
    tex_height_ = height;
  }
  if (dst_tex_ == 0) {
    dst_tex_ = render_->createTexture();
    render_->updateTexture(dst_tex_, nullptr, width, height,
                           BEF::bef_color_type::color_rgba);
  }

  if (0 == dst_tex_) {
    return false;
  }
  return true;
}

bool EffectSDK::NeedUpdateTransform(MSTransform* gl_trans) {
  if (!gl_trans_ && !gl_trans) {
    return false;
  }
  if (gl_trans_ && gl_trans) {
    return gl_trans_->angle != gl_trans->angle ||
           gl_trans_->flip_h != gl_trans->flip_h ||
           gl_trans_->flip_v != gl_trans->flip_v;
  }
  return true;
}

void EffectSDK::UpdateTransform(std::unique_ptr<MSTransform> gl_trans) {
  gl_trans_ = std::move(gl_trans);

  render_->setParami("flipX", gl_trans_ && gl_trans_->flip_v ? 1 : 0);
  render_->setParami("flipY", gl_trans_ && gl_trans_->flip_h ? 1 : 0);
  int rotate_type = 0;
  if (gl_trans_) {
    rotate_type = std::lroundf(gl_trans_->angle / 90) % 4;
    while (rotate_type < 0)
      rotate_type += 4;
  }
  render_->setParami("rotateType", rotate_type);

  if (gl_trans_) {
    LOG(INFO) << "[effect] trans flip_v:" << gl_trans_->flip_v
              << ",flip_h:" << gl_trans_->flip_h
              << ", rotate:" << gl_trans_->angle;
  } else {
    LOG(INFO) << "[effect] trans empty";
  }
}

bool EffectSDK::EffectSetSize(int width, int height) {
  // This function returns an error, which does not indicate a link failure,
  // only records it.
  bef_effect_result_t res = 0;
  res = ms_bef_effect_set_width_height(effect_handle_, width, height);
  if (res < BEF_RESULT_SUC) {
    LOG(ERROR) << "[Effect] Failed to bef_effect_set_width_height:" << res;
  }
  return true;
}

bool EffectSDK::EffectAlgorithm(GLuint src_texture,
                                double bef_time,
                                bool need_sync) {
  ScopedProfilerNameProxy a(notify_delegate_, kProfilterEffectAlgorithm);
  // FIXME: ms_bef_effect_algorithm_texture returns a value that does not
  // need to be cared about. It may return an error but has the correct result
  bool ret = sdk_processor_->Algorithm(effect_handle_, src_texture, bef_time,
                                       need_sync);
  if (!ret) {
    ++algorithm_err_count_;
  }
  return true;
}

bool EffectSDK::EffectProcess(GLuint src_texture,
                              GLuint dst_texture,
                              double bef_time) {
  // This function returns an error, which does not indicate a link failure,
  // only records it.
  ScopedProfilerNameProxy a(notify_delegate_, kProfilterEffectRender);
  if (bef_effect_result_t res = ms_bef_effect_process_texture(
          effect_handle_, src_texture, dst_texture, bef_time);
      res != BEF_RESULT_SUC) {
    if (res < BEF_RESULT_SUC) {
      ++process_err_count_;
      // LOG(ERROR) << "Failed to bef_effect_process_texture:" << res;
    }
  }
  return true;
}

bool EffectSDK::RenderToSurface(GLuint dst_tex, int width, int height) {
  ScopedProfilerNameProxy a(notify_delegate_, kProfilterEffectEnvironmentTrans);
  return 0 == render_->convertTransform(dst_tex, 0, width, height, false);
}

bool EffectSDK::FinishRender(int wait_ms) {
  ScopedProfilerNameProxy a(notify_delegate_, kProfilterEffectGLFlush);
  if (context_->GetGLESVersion() >= kGLVersion3) {
    return GLSyncWait(wait_ms);
  }
  glFlush();
  glFinish();
  return true;
}

constexpr int32_t kWait2MS = 2000000;

bool EffectSDK::GLSyncWait(int wait_ms) {
  auto start_time = mediasdk::nano_now();

  EffectGLSync sync;
  if (!EffectGLSync::NewSync(&sync)) {
    return false;
  }

  const int64_t timeout_ns = 1000ll * 1000 * wait_ms;
  const int64_t timeout_us = 1000ll * wait_ms;
  GLenum status =
      glClientWaitSync(sync.Sync(), GL_SYNC_FLUSH_COMMANDS_BIT, wait_ms);
  while (true) {
    switch (status) {
      case GL_CONDITION_SATISFIED:
      case GL_ALREADY_SIGNALED:
        return true;

      case GL_WAIT_FAILED:
        LOG(ERROR) << "[Effect] glClientWaitSync failed:" << glGetError();
        return false;
      case GL_TIMEOUT_EXPIRED: {
        auto end_time = mediasdk::nano_now();
        if (end_time - start_time >= timeout_ns) {
          return false;
        } else {
          std::this_thread::yield();
          status = glClientWaitSync(sync.Sync(), 0, 0);
          break;
        }
      }
    }
  }

  return true;
}

std::string EffectSDK::GetEffectVersion() {
  char version[MAX_PATH] = {0};
  ms_bef_effect_get_sdk_version(version, MAX_PATH);
  char commit[MAX_PATH] = {0};
  ms_bef_effect_get_sdk_commit(commit, MAX_PATH);
  version[MAX_PATH - 1] = 0;
  commit[MAX_PATH - 1] = 0;
  return std::string(version) + "-" + std::string(commit);
}

bool EffectSDK::SetRenderApi() {
  auto api_ver = context_->GetGLESVersion() == kGLVersion3
                     ? bef_render_api_gles30
                     : bef_render_api_gles20;
  bef_effect_result_t res =
      ms_bef_effect_set_render_api(effect_handle_, api_ver);
  if (res != 0) {
    LOG(ERROR) << "[Effect] Failed to bef_effect_set_render_api : " << res
               << ", render api ver" << api_ver;
    return false;
  }
  return true;
}

void EffectSDK::EffectConfigAB() {
  auto val = true;
  auto res =
      ms_bef_effect_config_ab_value("enable_first_frame_statistics", &val, 0);
  if (0 != res) {
    LOG(ERROR) << "[Effect] bef_effect_config_ab_value "
                  "(enable_first_frame_statistics) failed:"
               << res << ", val:" << val;
  }
  res = ms_bef_effect_config_ab_value("first_frame_force_algorithm", &val, 0);
  if (0 != res) {
    LOG(ERROR) << "[Effect] bef_effect_config_ab_value "
                  "(first_frame_force_algorithm) failed:"
               << res << ", val:" << val;
  }
  res = ms_bef_effect_config_ab_value("enable_et_data_capturing", &val, 0);
  res = ms_bef_effect_config_ab_value("enable_stuttering_data_capturing", &val,
                                      0);
}

bool EffectSDK::ProcessRelayStatistics(const std::string& json_dump) {
  if (!effect_statistics_) {
    LOG(ERROR) << "ReplayStatistics : effect_statistics_ does not exist";
    return false;
  }
  return effect_statistics_->OnComposersReplayed(json_dump);
}

bool EffectSDK::SetFinder(void* finder, void (*finder_release)(void*)) const {
  auto bef_finder = reinterpret_cast<bef_resource_finder>(finder);
  auto res = bef_api::ms_bef_effect_init_with_resource_finder_v2(
      effect_handle_, tex_width_, tex_height_, bef_finder, finder_release,
      "windows");
  if (0 != res) {
    LOG(ERROR) << "[Effect] set finder failed:" << res
               << ", finder:" << std::hex << finder;

    return false;
  }
  LOG(INFO) << "[Effect] set finder success";
  return true;
}

// static
bool EffectSDK::BEFDelegete(void* userdata,
                            unsigned int msgID,
                            int arg1,
                            int arg2,
                            const char* arg3) {
  auto ptr = static_cast<EffectSDK*>(userdata);
  if (!ptr || !ptr->notify_delegate_) {
    return false;
  }
  int64_t handle = (int64_t)(ptr->GetEffectHandle());
  nlohmann::json event_json = {
      {"msgID", msgID},
      {"arg1", arg1},
      {"arg2", arg2},
      {"arg3", arg3 ? std::string(arg3) : std::string()}};

  if (msgID == kEffectMsgIDResourceStatus &&
      arg1 == kEffectArgComposerFirstFrame && arg3 != nullptr &&
      ptr->effect_statistics_) {
    do {
      try {
        std::unordered_set<std::string> s;
        nlohmann::json composers_json = nlohmann::json::parse(arg3);
        auto effect_handle_v = composers_json.find("effect_handle");
        auto resource_added = composers_json.find("resource_added");
        if (resource_added == composers_json.end() ||
            effect_handle_v == composers_json.end()) {
          break;
        }
        for (const auto& item : resource_added.value().items()) {
          if (auto path_iter = item.value().find("path");
              path_iter != item.value().end()) {
            if (std::string basename = ptr->GetBaseName(path_iter.value());
                !basename.empty()) {
              s.emplace(basename);
            }
          }
        }
        auto v = effect_handle_v.value();
        if (v == handle) {
          ptr->RemoveNecessaryComposers(s);
        } else {
          LOG(WARNING) << "Filter a useless effect message";
          return false;
        }
      } catch (const std::exception& e) {
        LOG(WARNING) << "parse event json failed, err: " << e.what()
                     << ", event_json_str: " << arg3;
        break;
      }
    } while (false);

    return ptr->effect_statistics_->OnComposersAdded(event_json.dump());
  }

  if (msgID == kEffectComposerReplay &&
      arg1 == kEffectComposerReplayFirstFrame && arg2 == 2 &&
      ptr->effect_statistics_) {
    return ptr->ProcessRelayStatistics(event_json.dump());
  }
  ptr->notify_delegate_->Notify(effect_visual_filter::kNotifyResourceLoad,
                                event_json.dump());
  ptr->notify_delegate_->Notify(effect_visual_filter::kNotifyEffectMsg,
                                event_json.dump());
  return true;
}

bool EffectSDK::AddComposer(const std::vector<std::string>& composers,
                            const std::vector<std::string>& tags) const {
  // bef_effect_composer_append_nodes
  std::vector<const char*> nodes;
  std::vector<const char*> tags_in;
  nodes.reserve(composers.size());
  tags_in.reserve(tags.size());
  std::for_each(tags.begin(), tags.end(),
                [&](const auto& tag) { tags_in.emplace_back(tag.c_str()); });
  std::for_each(composers.begin(), composers.end(), [&](const auto& node) {
    nodes.emplace_back(node.c_str());
    if (sdk_config_.enable_stuttering_status_statistics) {
      auto base_name = GetBaseName(node);
      composers_.emplace(base_name);
    }
  });
  if (!bef_api::ms_bef_effect_composer_append_nodes ||
      !bef_api::ms_bef_effect_composer_append_nodes_with_tags) {
    LOG(ERROR) << "[Effect] load bef_effect_composer_append_nodes failed";
    return false;
  }

  bool use_tag = sdk_config_.use_interface_with_tag;
  bef_effect_result_t res;
  int64_t current_time_ms = milli_now();

  if (!use_tag) {
    res = bef_api::ms_bef_effect_composer_append_nodes(
        effect_handle_, nodes.data(), static_cast<int>(nodes.size()));
  }
  else {
    res = bef_api::ms_bef_effect_composer_append_nodes_with_tags(
            effect_handle_, nodes.data(), static_cast<int>(nodes.size()),
            tags_in.data());
  }
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_composer_append_nodes failed, res:"
               << res;
    return false;
  }
  if (effect_statistics_) {
    effect_statistics_->AddComposers(composers, current_time_ms);
  }

  return true;
}

bool EffectSDK::SetComposer(const std::vector<std::string>& composers,
                            const std::vector<std::string>& tags) const {
  // bef_effect_composer_set_nodes
  std::vector<const char*> nodes;
  std::vector<const char*> tags_in;
  tags_in.reserve(tags.size());
  nodes.reserve(composers.size());
  composers_.clear();
  std::for_each(tags.begin(), tags.end(),
                [&](const auto& tag) { tags_in.emplace_back(tag.c_str()); });
  std::for_each(composers.begin(), composers.end(), [&](const auto& node) {
    nodes.emplace_back(node.c_str());
    if (sdk_config_.enable_stuttering_status_statistics) {
      auto base_name = GetBaseName(node);
      composers_.emplace(base_name);
    }
  });

  if (!bef_api::ms_bef_effect_composer_set_nodes ||
      !bef_api::ms_bef_effect_composer_set_nodes_with_tags) {
    LOG(ERROR) << "[Effect] load bef_effect_composer_set_nodes failed";
    return false;
  }
  bool use_tag = sdk_config_.use_interface_with_tag;
  bef_effect_result_t res;
  if (!use_tag) {
    res = bef_api::ms_bef_effect_composer_set_nodes(
        effect_handle_, nodes.data(), static_cast<int>(composers.size()));
  }
  else {
    res = bef_api::ms_bef_effect_composer_set_nodes_with_tags(
        effect_handle_, nodes.data(), static_cast<int>(composers.size()),
        tags_in.data());
  }
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_composer_set_nodes failed, res:" << res;
    return false;
  }
  return true;
}

void EffectSDK::SetNecessaryComposer(
    const std::vector<std::string>& composers) const {
  std::for_each(composers.begin(), composers.end(), [&](const auto& node) {
    auto base_name = GetBaseName(node);
    necessary_composers_.emplace(base_name);
  });
}

void EffectSDK::RemoveNecessaryComposers(
    const std::unordered_set<std::string>& composers) {
  notify_delegate_->RemoveNecessaryComposers(composers);
}

void EffectSDK::RemoveNecessaryComposer(const std::string& composer) {
  if (necessary_composers_.count(composer)) {
    necessary_composers_.erase(composer);
  }
}

bool EffectSDK::UpdateComposer(const std::string& path,
                               const std::string& key,
                               float value,
                               const std::string& tag) const {
  if (!bef_api::ms_bef_effect_composer_update_node) {
    LOG(ERROR) << "[Effect] load bef_effect_composer_update_node failed";
    return false;
  }
  bef_effect_result_t res = bef_api::ms_bef_effect_composer_update_node(
      effect_handle_, path.c_str(), key.c_str(), value);
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_composer_update_node failed, res:"
               << res;
    return false;
  }
  return true;
}

bool EffectSDK::RemoveComposer(
    const std::vector<std::string>& composers) const {
  // bef_effect_composer_remove_nodes
  std::vector<const char*> nodes;
  nodes.reserve(composers.size());
  std::for_each(composers.begin(), composers.end(), [&](const auto& node) {
    nodes.emplace_back(node.c_str());
    auto base_name = GetBaseName(node);
    if (sdk_config_.enable_stuttering_status_statistics) {
      if (composers_.count(base_name)) {
        composers_.erase(base_name);
      }
    }
    if (necessary_composers_.count(base_name)) {
      necessary_composers_.erase(base_name);
    }
  });

  if (!bef_api::ms_bef_effect_composer_remove_nodes) {
    LOG(ERROR) << "[Effect] load bef_effect_composer_remove_nodes failed";
    return false;
  }

  bef_effect_result_t res = bef_api::ms_bef_effect_composer_remove_nodes(
      effect_handle_, nodes.data(), static_cast<int>(nodes.size()));
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_composer_remove_nodes failed, res:"
               << res;
    return false;
  }

  if (effect_statistics_) {
    effect_statistics_->RemoveComposers(composers);
  }

  return true;
}

bool EffectSDK::SetTextComposer(const std::string& key,
                                const std::string& value) const {
  if (!bef_api::ms_bef_effect_set_render_cache_string_value) {
    LOG(ERROR)
        << "[Effect] load bef_effect_set_render_cache_string_value failed";
    return false;
  }
  bef_effect_result_t res =
      bef_api::ms_bef_effect_set_render_cache_string_value(
          effect_handle_, key.c_str(), value.c_str());
  if (res != 0) {
    LOG(ERROR)
        << "[Effect] bef_effect_set_render_cache_string_value failed, res:"
        << res;
    return false;
  }
  return true;
}

bool EffectSDK::SetBackgroundComposer(const std::string& key,
                                      const std::string& path) const {
  if (!bef_api::ms_bef_effect_set_render_cache_texture) {
    LOG(ERROR) << "[Effect] load bef_effect_set_render_cache_texture failed";
    return false;
  }
  bef_effect_result_t res = bef_api::ms_bef_effect_set_render_cache_texture(
      effect_handle_, key.c_str(), path.c_str());
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_set_render_cache_texture failed, res:"
               << res;
    return false;
  }
  return true;
}

bool EffectSDK::ReplaceComposer(
    const std::vector<std::string>& old_paths,
    const std::vector<std::string>& new_paths, 
    const std::vector<std::string>& tags) const {
  if (!bef_api::ms_bef_effect_composer_replace_nodes ||
      !bef_api::ms_bef_effect_composer_replace_nodes_with_tags) {
    LOG(ERROR) << "[Effect] load bef_effect_composer_replace_nodes failed";
    return false;
  }
  std::vector<const char*> old_nodes;
  std::vector<const char*> new_nodes;
  std::vector<const char*> tags_in;
  old_nodes.reserve(old_paths.size());
  new_nodes.reserve(new_paths.size());
  tags_in.reserve(tags.size());
  std::for_each(tags.begin(), tags.end(),
                [&](const auto& tag) { tags_in.emplace_back(tag.c_str());
      });
  std::for_each(old_paths.begin(), old_paths.end(), [&](const auto& node) {
    old_nodes.emplace_back(node.c_str());
    if (sdk_config_.enable_stuttering_status_statistics) {
      auto base_path = GetBaseName(node);
      if (composers_.count(base_path)) {
        composers_.erase(base_path);
      }
    }
  });
  std::for_each(new_paths.begin(), new_paths.end(), [&](const auto& node) {
    new_nodes.emplace_back(node.c_str());
    if (sdk_config_.enable_stuttering_status_statistics) {
      composers_.emplace(GetBaseName(node));
    }
  });
  bool use_tag = sdk_config_.use_interface_with_tag;
  bef_effect_result_t res;
  if (!use_tag) {
    res = bef_api::ms_bef_effect_composer_replace_nodes(
        effect_handle_, old_nodes.data(), static_cast<int>(old_nodes.size()),
        new_nodes.data(), static_cast<int>(new_nodes.size()));
  }
  else {
    res = bef_api::ms_bef_effect_composer_replace_nodes_with_tags(
        effect_handle_, old_nodes.data(), static_cast<int>(old_nodes.size()),
        new_nodes.data(), static_cast<int>(new_nodes.size()), tags_in.data());
  }
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_composer_replace_nodes failed, res:"
               << res;
    return false;
  }
  int64_t current_time_ms = milli_now();
  if (effect_statistics_) {
    effect_statistics_->RemoveComposers(old_paths);
    effect_statistics_->AddComposers(new_paths, current_time_ms);
  }
  return true;
}

bool EffectSDK::SendMsg(unsigned int msg_id,
                        long arg1,
                        long arg2,
                        const std::string& arg3) const {
  if (!bef_api::ms_bef_effect_send_msg) {
    LOG(ERROR) << "[Effect] load bef_effect_send_msg failed";
    return false;
  }
  // Replay msg
  if (msg_id == kEffectComposerReplay && arg1 == 3 && arg2 == 2) {
    int64_t current_time_ms = milli_now();
    if (effect_statistics_ != nullptr) {
      effect_statistics_->AddReplayComposers(arg3, current_time_ms);
    }
  }
  bef_api::ms_bef_effect_send_msg(effect_handle_, msg_id, arg1, arg2,
                                  arg3.c_str());
  return true;
}

int EffectSDK::GetComposerExclusion(const std::string& path,
                                    const std::string& key) const {
  if (!effect_handle_) {
    return 0;
  }
  int exclusion = 0;
  auto res = bef_api::ms_bef_effect_composer_check_node_exclusion(
      effect_handle_, path.c_str(), key.c_str(), &exclusion);
  if (res != 0) {
    LOG(ERROR) << "[Effect] bef_effect_composer_check_node_exclusion failed:"
               << res << ", path:" << path << ", key:" << key;
  }
  return exclusion;
}

void EffectSDK::Notify(const std::string& key,
                       const std::string& message_json) {
  if (!notify_delegate_) {
    return;
  }
  notify_delegate_->Notify(key, message_json);
}

std::string EffectSDK::GetBaseName(const std::string& full_path) const {
  if (full_path.empty()) {
    return {};
  }

  auto semicolon_pos = full_path.find(';');
  std::string basename = (semicolon_pos != std::string::npos)
                             ? full_path.substr(0, semicolon_pos)
                             : full_path;
  basename.erase(std::find_if(basename.rbegin(), basename.rend(),
                              [](const char& ch) {
                                return std::strchr(kSeparators, ch) == nullptr;
                              })
                     .base(),
                 basename.end());

  if (auto last_separator = basename.find_last_of(kSeparators);
      last_separator != std::string::npos &&
      last_separator < basename.size() - 1) {
    basename.erase(0, last_separator + 1);
  }

  return basename;
}

void* EffectSDK::GetEffectHandle() const {
  return effect_handle_;
}

bool EffectSDK::GetAlgoUsed() const {
  return use_algo_;
}
}  // namespace mediasdk