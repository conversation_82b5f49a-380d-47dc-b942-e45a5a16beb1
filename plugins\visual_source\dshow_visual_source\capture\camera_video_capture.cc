#include "camera_video_capture.h"

#include <base/strings/utf_string_conversions.h>
#include "dshow_visual_details.h"
#include "time_helper.h"

namespace mediasdk {

CameraVideoCapture::CameraVideoCapture() {}

CameraVideoCapture::~CameraVideoCapture() {
  LOG(INFO) << "~CameraVideoCapture";
  CameraVideoCapture::Destroy();
}

bool CameraVideoCapture::Create(const nlohmann::json& json_root) {
  VideoCaptureFormat ft_video;
  AudioCaptureFormat ft_audio;
  DShowDeviceName video_name;
  DShowDeviceName audio_name;
  if (!DShowCaptureBase::Create(json_root)) {
    SetLastCreateError({dshow_visual_source::kCreateStepJson, E_INVALIDARG});
    return false;
  }
  ft_video.Reset();
  if (!UnPacketCreateJson(json_root, ft_video, ft_audio, video_name,
                          audio_name)) {
    SetLastCreateError(
        {dshow_visual_source::kCreateStepUnpackJson, E_INVALIDARG});
    return false;
  }
  SetLimitCaptureFPS(static_cast<int>(ft_video.GetLimitRate()));
  video_name_ = video_name;
  audio_name_ = audio_name;
  HRESULT hr = OpenDevice(video_name_, audio_name_,
                          dshow_visual_source::kAnalogRenderTypeNone);
  if (FAILED(hr)) {
    SetLastCreateError({dshow_visual_source::kCreateStepOpenDevice, hr});
    return false;
  }
  hr = ConnectFilter(ft_video);
  if (FAILED(hr)) {
    SetLastCreateError({dshow_visual_source::kCreateStepConnectVFilter, hr});
    return false;
  }
  if (!Start(&hr)) {
    SetLastCreateError({dshow_visual_source::kCreateStepStart, hr});
    return false;
  }
  auto wait_ms = mediasdk::milli_now();
  FILTER_STATE state = WaitForRunning();
  wait_ms = (mediasdk::milli_now() - wait_ms);
  LOG(INFO) << "wait running:" << state << ", time ms:" << wait_ms;
  SetLastCreateError({dshow_visual_source::kCreateStepStart, 0});
  return true;
}

void CameraVideoCapture::OnAudioFrameReceive(const uint8_t* data,
                                             int32_t size,
                                             int64_t tm) {
  if (!has_audio_) {
    return;
  }
  auto wave_format = audio_format_.GetFormat();
  if (!data || size <= 0 || !wave_format || wave_format->nBlockAlign == 0) {
    return;
  }
  mediasdk::AudioFormat format;
  format.SetChannel(wave_format->nChannels);
  format.SetSampleRate(wave_format->nSamplesPerSec);
  //format.SetFormat(mediasdk::WASAPIDevice::ConvertAudioFormat(wave_format, false));
  //format.SetLayout(mediasdk::WASAPIDevice::ConvertChannelLayout(wave_format));

  mediasdk::AudioSourceFrame apk = {};
  apk.timestamp_ns = tm;
  apk.data[0] = (uint8_t*)data;
  apk.block_size = wave_format->nBlockAlign;
  apk.count = size / wave_format->nBlockAlign;
  apk.sample_rate = wave_format->nSamplesPerSec;
  apk.channel_count = wave_format->nChannels;
  OnAudioFrame(format, apk, tm);
}

DShowCaptureBase::DShowCaptureType CameraVideoCapture::GetDShowType() {
  return kDShowCaptureTypeCamera;
}

const char* CameraVideoCapture::GetSubTypeName() {
  return "capture_camera";
}

bool CameraVideoCapture::HasAudio() {
  return true;
}

}  // namespace mediasdk