#pragma once

#include <memory>

#include <graphics/graphics.h>
#include <graphics/texture.h>

#include "effect_notify_delegate.h"
#include "effect_processor.h"
#include "effect_visual_filter_helper.h"
#include "mediasdk/public/plugin/visual_filter.h"
#include "mediasdk/utils/frame_rate_calculator.h"
#include "effect_sdk/effect_config.h"

namespace mediasdk {

namespace ep {
class Engine;
}

class EffectVisualFilter : public VisualFilter, public EffectNotifyDelegate {
 public:
  explicit EffectVisualFilter();

  ~EffectVisualFilter() override;

  // VisualFilter
  // called in plugin thread
  bool Create(const char* json_params) override;

  void Destroy() override;

  MediaSDKString GetProperty(const char* key) override;

  bool SetProperty(const char* key, const char* json) override;

  MediaSDKString Action(const char* action, const char* json_params) override;

  VisualFilterUsePhase GetUsePhase() override {
    return kVisualFilterUsePhasePrepare;
  }

  // VisualFilter
  // called in render thread
  void InitGraphicsResource(VisualFilterProxy* proxy) override;

  void ReleaseGraphicsResource() override;

  void ClearVideoCache() override;

  graphics::Texture* Process(graphics::Texture* texture,
                             const VisualFilterTransform& texture_trans,
                             int64_t timestamp) override;

  int64_t GetProcessDelayNS() override;

 private:
  // EffectNotifyDelegate called by effect_sdk_
  void Notify(const std::string& notify, const std::string& json_data) override;

  void OnStateChanged(bool success) override;

  void Profilter(const char* name, int64_t period_ns) override;

  void BeginCostProfilter(const char* name, int64_t begin_ns) override;

  void CostProfilter(const char* name, int64_t period_ns) override;

  int GetRenderFps() override;

  std::tuple<bool, std::string> GetAlgoAndABConfig();

  uint32_t GetEffectDestoryWaitTimeoutMSFromAB();

  bool GetGLContextEnableDownGradeFromAB();

  bool GetEnableANGLELogFromAB();

  bool GetEnableEffectFirstFrameStatisticsFromAB();

  bool GetEnableHideOriginFrameFromAB();

  bool GetPrecompileShaderFromAB();

  bool GetEnableEffectStutteringStatusFromAB();

  bool GetUseInterfaceWithTagFromAB();

  std::tuple<bool, uint8_t> GetANGLEProgramCacheConfigFromAB(
      std::vector<std::pair<int, int>>& angle_config);

  bool GetEffectConfigFromAB(EffectConfig& effect_config);

  std::string GetComposerExclusion(const std::string& action,
                                   const std::string& action_param);

  std::string GetEffectStatistic(const std::string& action);

 private:
  effect_visual_filter::InitParams init_params_{};
  std::unique_ptr<EffectProcessor> processor_;
  VisualFilterProxy* proxy_{nullptr};
  ThreadSafeFrameCalculator frame_calc_;
  ThreadSafeFrameRateCalculator in_fps_calc_;
};

}  // namespace mediasdk