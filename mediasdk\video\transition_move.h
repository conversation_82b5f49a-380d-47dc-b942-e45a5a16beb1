#pragma once

#include "mediasdk/public/mediasdk_defines_canvas.h"
#include "mediasdk/video/transition.h"

namespace mediasdk {
using MatchedItemMap = std::unordered_map<std::string, CanvasItemPtrList>;

constexpr TransitionMoveParams GetDefaultMoveParams();

struct TransitionActionMoveParams {
  int64_t duration_ms = TRANSITION_DURATION_MS_DEFAULT;
  float middle_progress = 0.5f;
  std::vector<std::pair<std::string, std::string>> matched_canvas_items;
  TransitionMoveParams default_move_params = GetDefaultMoveParams();
  std::map<std::string, TransitionMoveParams> item_move_params;
};  // namespace mediasdk

std::shared_ptr<Transition> CreateTransitionMove(const std::string& id,
                                                 TransitionProxy* proxy,
                                                 const std::string& properties);

class TransitionMove : public Transition {
 public:
  TransitionMove(const std::string& id,
                 TransitionProxy* proxy,
                 const TransitionActionMoveParams& params)
      : Transition(id, proxy), params_(params) {}

  ~TransitionMove() override = default;

  float CaculateProgress(int64_t begin_ms, int64_t used_ms) override;

  void UpdateProperties(const std::string& properties,
                        MSCallbackBool callback) override;

  void DrawTransition(graphics::Graphics& target,
                      const CanvasRender* from_canvas,
                      const CanvasRender* to_canvas,
                      graphics::Graphics& from_graphics,
                      graphics::Graphics& to_graphics,
                      const float progress,
                      const DirectX::XMFLOAT2* scale,
                      VisualDrawReason draw_reason) override;

  void IncreaseTransitionRefCount() override;

 private:
  void MatchCanvasItem(const CanvasRender* from_canvas,
                       const CanvasRender* to_canvas,
                       MatchedItemMap& from_item_matched_items,
                       MatchedItemMap& to_item_matched_items);

  void DrawTransitionInternal(graphics::Graphics& target,
                              const CanvasRender* from_canvas,
                              const CanvasRender* to_canvas,
                              graphics::Graphics& from_graphics,
                              graphics::Graphics& to_graphics,
                              const float progress,
                              const DirectX::XMFLOAT2* scale,
                              VisualDrawReason draw_reason,
                              const MatchedItemMap& from_item_matched_items,
                              const MatchedItemMap& to_item_matched_items);

  void DrawTransitionItemMoveIn(const mediasdk::CanvasItemPtr& item,
                                graphics::Graphics& target,
                                graphics::Graphics& to_graphics,
                                const float progress,
                                const DirectX::XMFLOAT2* scale,
                                VisualDrawReason draw_reason);

  void DrawTransitionItemMoveOut(const mediasdk::CanvasItemPtr& item,
                                 graphics::Graphics& target,
                                 graphics::Graphics& from_graphics,
                                 const float progress,
                                 const DirectX::XMFLOAT2* scale,
                                 VisualDrawReason draw_reason);

  void DrawTransitionItemSlide(const mediasdk::CanvasItemPtr& item,
                               graphics::Graphics& target,
                               const float progress,
                               const DirectX::XMFLOAT2* scale,
                               VisualDrawReason draw_reason,
                               TransitionDirection move_direction);

  void DrawTransitionItemMove(const mediasdk::CanvasItemPtr& from_item,
                              const mediasdk::CanvasItemPtr& to_item,
                              graphics::Graphics& target,
                              const float progress,
                              const DirectX::XMFLOAT2* scale,
                              VisualDrawReason draw_reason);

  void CaculateMaxDistanceCenterToEdge(const DirectX::XMFLOAT2& size,
                                       const DirectX::XMFLOAT2& scale,
                                       const float rotate,
                                       DirectX::XMFLOAT4& max_distance);

  void DrawTransitionItemScale(const mediasdk::CanvasItemPtr& item,
                               graphics::Graphics& target,
                               const float progress,
                               const DirectX::XMFLOAT2* scale,
                               VisualDrawReason draw_reason,
                               const DirectX::XMFLOAT2& translate);

  void DrawTransitionItemFade(const mediasdk::CanvasItemPtr& item,
                              graphics::Graphics& target,
                              graphics::Graphics& from_or_to_graphics,
                              const float progress,
                              const DirectX::XMFLOAT2* scale,
                              VisualDrawReason draw_reason);

  void DrawItemWithTransform(const mediasdk::CanvasItemPtr& item,
                             graphics::Graphics& target,
                             const float progress,
                             const DirectX::XMFLOAT2* scale,
                             VisualDrawReason draw_reason,
                             const DirectX::XMFLOAT2& from_scale,
                             const DirectX::XMFLOAT2& to_scale,
                             const DirectX::XMFLOAT2& from_translate,
                             const DirectX::XMFLOAT2& to_translate);

  const TransitionMoveParams& GetMoveParams(const std::string& item_id);

  DirectX::XMFLOAT2 GetItemSizeOnWindow(const CanvasItemPtr& item,
                                        const DirectX::XMFLOAT2& window_region,
                                        const DirectX::XMFLOAT2* scale);

  DirectX::XMFLOAT2 CalcCurrentSize(const DirectX::XMFLOAT2& from_size,
                                    const DirectX::XMFLOAT2& to_size,
                                    bool keep_ratio,
                                    const float progress);

  DirectX::XMFLOAT2 CalcTranslate(const MSTranslateF& loc,
                                  const DirectX::XMFLOAT2& target_region,
                                  const DirectX::XMFLOAT2* scale);

  graphics::Transform CalcTransform(const graphics::Transform& src_transform,
                                    const graphics::Transform& dst_transform,
                                    const DirectX::XMFLOAT2& scale_diff,
                                    float move_progress);

 private:
  TransitionActionMoveParams params_;
  std::optional<TransitionActionMoveParams> delay_params_ {std::nullopt};
};
}  // namespace mediasdk