#include "dshow_capture_decorator.h"

#include "capture/analog_video_capture.h"
#include "capture/camera_video_capture.h"

namespace {
const int kCreateContextTimeoutMs = 2000;
const int kControlDeviceTimeoutMs = 6000;
}  // namespace

namespace mediasdk {

DShowCaptureDecorator::DShowCaptureDecorator(
    DShowVideoFrameNotify* video_notify,
    DShowAudioFrameNotify* audio_notify,
    DShowOperationNotify* operation_notify)
    : video_notify_(video_notify),
      audio_notify_(audio_notify),
      operation_notify_(operation_notify),
      create_index_(0) {}

DShowCaptureDecorator::~DShowCaptureDecorator() {}

bool DShowCaptureDecorator::Create(dshow_visual_source::CaptureType type,
                                   const nlohmann::json& json_root) {
  capture_type_ = type;
  return Create(json_root);
}

bool DShowCaptureDecorator::Create(const nlohmann::json& json_root) {
  create_json_mutex_.lock();
  create_json_root_ = json_root;
  create_json_mutex_.unlock();

  thread_ = std::make_unique<DShowCaptureTaskThread>("dshow_capture_task");
  if (!thread_) {
    LOG(ERROR) << "New DShowCaptureTaskThread Error!";
    return false;
  }
  auto capture_type = capture_type_;
  auto video_notify = video_notify_;
  auto audio_notify = audio_notify_;
  auto operation_notify = operation_notify_;
  if (!thread_->CreateTaskThread(
          [capture_type, video_notify, audio_notify, operation_notify]() {
            std::shared_ptr<DShowCaptureBase> ret;
            if (capture_type == dshow_visual_source::kCaptureTypeCamera) {
              ret = std::make_shared<CameraVideoCapture>();
              if (ret) {
                ret->SetNotify(video_notify, nullptr, operation_notify);
              }
            } else {
              ret = std::make_shared<AnalogVideoCapture>();
              if (ret) {
                ret->SetNotify(video_notify, audio_notify, operation_notify);
              }
            }
            return ret;
          },
          kCreateContextTimeoutMs)) {
    LOG(ERROR) << "CreateTaskThread Error!";
    return false;
  }
  ++create_index_;
  create_time_out_ms_ = GetCreateTimeoutMS(json_root);
  if (create_time_out_ms_ < kCreateContextTimeoutMs) {
    create_time_out_ms_ = kControlDeviceTimeoutMs;
  }
  LOG(INFO) << "Create timeout ms:" << create_time_out_ms_;
  thread_->SetCameraConfig(camera_config_);
  if (thread_->CreateDShow(json_root, create_time_out_ms_)) {
    return true;
  }
  thread_->DestroyDShow();
  return false;
}

void DShowCaptureDecorator::Destroy() {
  bool destroy_ok = true;
  if (thread_) {
    thread_->DestroyDShow();
    if (thread_->DestroyTaskThread()) {
      thread_.reset();
    } else {
      destroy_ok = false;
      thread_.release();
    }
  }
  LOG(INFO) << "Destroy:" << destroy_ok;
}

nlohmann::json DShowCaptureDecorator::GetCreateJson() {
  std::lock_guard<std::mutex> lock(create_json_mutex_);
  return create_json_root_;
}

void DShowCaptureDecorator::CleanVideoTransFormBuffers() {
  if (thread_) {
    thread_->CleanVideoTransFormBuffers();
  }
}

void DShowCaptureDecorator::SetCameraConfig(const CameraConfig& camera_config) {
  camera_config_ = camera_config;
}

int32_t DShowCaptureDecorator::GetCreateIndex() {
  return create_index_.load();
}

std::vector<mediasdk::VideoProcAmpStruct>
DShowCaptureDecorator::GetVideoProcAmp() {
  if (thread_) {
    return thread_->GetVideoProcAmp();
  }
  return {};
}

bool DShowCaptureDecorator::SetVideoProcAmp(VideoProcAmpProperty pro,
                                            long value,
                                            long flag) {
  if (thread_) {
    return thread_->SetVideoProcAmp(pro, value, flag);
  }
  return false;
}

std::vector<mediasdk::CameraControlStruct>
DShowCaptureDecorator::GetCameraControl() {
  if (thread_) {
    return thread_->GetCameraControl();
  }
  return {};
}

bool DShowCaptureDecorator::SetCameraControl(CameraControlProperty pro,
                                             long value,
                                             long flag) {
  if (thread_) {
    return thread_->SetCameraControl(pro, value, flag);
  }
  return false;
}

void DShowCaptureDecorator::SetLimitCaptureFPS(int limit_fps) {
  if (thread_) {
    thread_->SetLimitCaptureFPS(limit_fps);
  }
}

void DShowCaptureDecorator::SetRenderFPS(int render_fps) {
  if (thread_) {
    thread_->SetRenderFPS(render_fps);
  }
}

void DShowCaptureDecorator::StartVideoRangeDetect(
    const int32_t detect_interval,
    const int32_t limited_detect_count) {
  if (thread_) {
    thread_->StartVideoRangeDetect(detect_interval, limited_detect_count);
  }
}

void DShowCaptureDecorator::StopVideoRangeDetect() {
  if (thread_) {
    thread_->StopVideoRangeDetect();
  }
}

VideoCaptureFormat DShowCaptureDecorator::GetCaptureVideoFormat() {
  if (thread_) {
    return thread_->GetCaptureVideoFormat();
  }
  return {};
}

bool DShowCaptureDecorator::Continue() {
  if (thread_) {
    return thread_->Continue();
  }
  return false;
}

bool DShowCaptureDecorator::Pause() {
  if (thread_) {
    return thread_->Pause();
  }
  return false;
}

bool DShowCaptureDecorator::IsPaused() {
  if (thread_) {
    return thread_->IsPaused();
  }
  return false;
}

bool DShowCaptureDecorator::HasAudio() {
  if (thread_) {
    return thread_->HasAudio();
  }
  return false;
}

bool DShowCaptureDecorator::GetState(long time_out_ms, FILTER_STATE& state) {
  if (thread_) {
    return thread_->GetState(time_out_ms, state);
  }
  return false;
}

bool DShowCaptureDecorator::IsRunningOrPause(FILTER_STATE& state) {
  if (!camera_config_.use_pause) {
    if (thread_) {
      return thread_->IsRunningOrPause(state);
    }
    return false;
  } else {
    if (thread_) {
      return thread_->GetState(0, state);
    }
    return false;
  }
}

dshow_visual_source::CreateStatus DShowCaptureDecorator::GetLastCreateError() {
  if (thread_) {
    return thread_->GetLastCreateError();
  }
  return {dshow_visual_source::kCreateStepContext, E_PENDING};
}

const char* DShowCaptureDecorator::GetSubTypeName() const {
  if (thread_) {
    return thread_->GetSubTypeName();
  }
  return nullptr;
}

int DShowCaptureDecorator::GetCreateTimeoutMS(const nlohmann::json& json_root) {
  int ret = kControlDeviceTimeoutMs;
  try {
    ret = json_root.value("create_timeout_ms", kControlDeviceTimeoutMs);
  } catch (...) {
    ret = kControlDeviceTimeoutMs;
  }
  return ret;
}

}  // namespace mediasdk