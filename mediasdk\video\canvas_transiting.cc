#include "canvas_transiting.h"
#include "mediasdk/utils/time_helper.h"

namespace mediasdk {

CanvasTransiting::CanvasTransiting(graphics::Device& device)
    : device_(device) {}

CanvasTransiting::~CanvasTransiting() {
  items_.clear();
  transiting_item_.reset();  // Release the transiting item pointe
}

std::shared_ptr<CanvasTransiting> CanvasTransiting::Create(
    graphics::Device& device,
    const std::shared_ptr<CanvasRender>& from_canvas,
    const std::shared_ptr<CanvasRender>& to_canvas,
    const std::shared_ptr<Transition>& transition,
    const TransitingGraphicsPtr& transiting_graphics) {
  if (!from_canvas || !to_canvas || !transition || !transiting_graphics) {
    LOG(INFO) << "[Transiting] Create failed, from_canvas is_null("
              << !from_canvas << ", to_canvas is_null(" << !to_canvas
              << ", transition is_null(" << !transition
              << ", transiting_graphics is_null(" << !transiting_graphics
              << ")";
    return nullptr;
  }

  auto transiting = std::make_shared<CanvasTransiting>(device);
  transiting->SetTransitingItem(TransitingCanvasItem::Create(
      from_canvas, to_canvas, transition, transiting_graphics, *transiting));
  return transiting;
}

void CanvasTransiting::SetTransitingItem(
    std::shared_ptr<TransitingCanvasItem> transiting_item) {
  transiting_item_ = transiting_item;
  items_.clear();
  items_.push_back(std::dynamic_pointer_cast<CanvasItem>(transiting_item_));
}

const CanvasItemPtrList& CanvasTransiting::GetItems() const {
  return items_;
}

bool CanvasTransiting::IsActive() const {
  return is_active_ && !!transiting_item_ && transiting_item_->IsActive();
}

}  // namespace mediasdk
